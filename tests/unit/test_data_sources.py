"""
Unit tests for data sources.
Tests the base data source functionality and specific implementations.
"""

import pytest
import asyncio
import aiohttp
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import pandas as pd
from datetime import datetime

from src.quantum_edge.data.sources.base import (
    BaseDataSource, 
    RateLimiter, 
    DataSourceError, 
    APIError, 
    RateLimitError
)
from src.quantum_edge.data.sources.polygon_client import PolygonClient
from src.quantum_edge.data.sources.alpaca_client import AlpacaClient


class TestRateLimiter:
    """Test rate limiter functionality."""
    
    @pytest.mark.asyncio
    async def test_rate_limiter_basic(self):
        """Test basic rate limiting functionality."""
        limiter = RateLimiter(requests_per_minute=2)
        
        # Should allow first two requests immediately
        await limiter.acquire()
        await limiter.acquire()
        
        # Third request should be delayed
        start_time = asyncio.get_event_loop().time()
        await limiter.acquire()
        end_time = asyncio.get_event_loop().time()
        
        # Should have been delayed (allowing some tolerance for test execution)
        assert end_time - start_time >= 0.1  # At least some delay
    
    @pytest.mark.asyncio
    async def test_rate_limiter_hourly(self):
        """Test hourly rate limiting."""
        limiter = RateLimiter(requests_per_minute=60, requests_per_hour=2)
        
        # Should allow first two requests
        await limiter.acquire()
        await limiter.acquire()
        
        # Third request should raise exception
        with pytest.raises(RateLimitError):
            await limiter.acquire()


class TestBaseDataSource:
    """Test base data source functionality."""
    
    class MockDataSource(BaseDataSource):
        """Mock implementation for testing."""
        
        async def test_connection(self) -> bool:
            return True
        
        async def get_market_data(self, symbols, **kwargs):
            return {"test": "data"}
    
    @pytest.fixture
    def mock_data_source(self):
        """Create mock data source for testing."""
        return self.MockDataSource(
            base_url="https://api.example.com",
            headers={"Authorization": "Bearer test"},
            timeout_seconds=10,
            retry_attempts=2
        )
    
    @pytest.mark.asyncio
    async def test_session_management(self, mock_data_source):
        """Test session creation and cleanup."""
        # Session should be created when needed
        session = await mock_data_source._get_session()
        assert isinstance(session, aiohttp.ClientSession)
        assert not session.closed
        
        # Should reuse existing session
        session2 = await mock_data_source._get_session()
        assert session is session2
        
        # Should close session
        await mock_data_source.close()
        assert session.closed
    
    @pytest.mark.asyncio
    async def test_context_manager(self, mock_data_source):
        """Test async context manager functionality."""
        async with mock_data_source as ds:
            session = await ds._get_session()
            assert not session.closed
        
        # Session should be closed after context exit
        assert session.closed
    
    def test_validate_symbols(self, mock_data_source):
        """Test symbol validation."""
        # Test string input
        result = mock_data_source.validate_symbols("AAPL")
        assert result == ["AAPL"]
        
        # Test list input
        result = mock_data_source.validate_symbols(["aapl", "MSFT", "  googl  "])
        assert result == ["AAPL", "MSFT", "GOOGL"]
        
        # Test invalid symbols
        result = mock_data_source.validate_symbols(["", "TOOLONGSYMBOL", "VALID"])
        assert result == ["VALID"]
    
    def test_datetime_formatting(self, mock_data_source):
        """Test datetime formatting and parsing."""
        dt = datetime(2023, 12, 25, 15, 30, 45)
        
        # Test formatting
        formatted = mock_data_source.format_datetime(dt)
        assert formatted == "2023-12-25"
        
        # Test parsing
        parsed = mock_data_source.parse_datetime("2023-12-25T15:30:45Z")
        assert parsed.year == 2023
        assert parsed.month == 12
        assert parsed.day == 25


class TestPolygonClient:
    """Test Polygon.io client functionality."""
    
    @pytest.fixture
    def polygon_client(self):
        """Create Polygon client for testing."""
        return PolygonClient(api_key="test_key", rate_limit_per_minute=60)
    
    @pytest.mark.asyncio
    async def test_initialization(self, polygon_client):
        """Test client initialization."""
        assert polygon_client.api_key == "test_key"
        assert polygon_client.base_url == "https://api.polygon.io"
        assert "Authorization" in polygon_client.headers
    
    @pytest.mark.asyncio
    async def test_test_connection_success(self, polygon_client):
        """Test successful connection test."""
        with patch.object(polygon_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = {"results": []}
            
            result = await polygon_client.test_connection()
            assert result is True
            mock_get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_test_connection_failure(self, polygon_client):
        """Test failed connection test."""
        with patch.object(polygon_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.side_effect = Exception("Connection failed")
            
            result = await polygon_client.test_connection()
            assert result is False
    
    @pytest.mark.asyncio
    async def test_get_gainers(self, polygon_client):
        """Test getting gainers data."""
        mock_response = {
            "results": [
                {
                    "ticker": "AAPL",
                    "value": {"c": 150.0, "v": 1000000, "cp": 2.5},
                    "lastTrade": {"p": 150.0},
                    "prevDay": {"c": 146.34},
                }
            ]
        }
        
        with patch.object(polygon_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = mock_response
            
            result = await polygon_client.get_gainers(limit=10)
            
            assert isinstance(result, pd.DataFrame)
            assert len(result) == 1
            assert result.iloc[0]["symbol"] == "AAPL"
            assert result.iloc[0]["price"] == 150.0
            mock_get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_gainers_empty_response(self, polygon_client):
        """Test getting gainers with empty response."""
        with patch.object(polygon_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = {}
            
            result = await polygon_client.get_gainers()
            
            assert isinstance(result, pd.DataFrame)
            assert len(result) == 0
    
    @pytest.mark.asyncio
    async def test_get_ticker_details(self, polygon_client):
        """Test getting ticker details."""
        mock_response = {
            "results": {
                "ticker": "AAPL",
                "name": "Apple Inc.",
                "market": "stocks",
                "market_cap": 3000000000000,
            }
        }
        
        with patch.object(polygon_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = mock_response
            
            result = await polygon_client.get_ticker_details("AAPL")
            
            assert result["symbol"] == "AAPL"
            assert result["name"] == "Apple Inc."
            assert result["market_cap"] == 3000000000000
    
    @pytest.mark.asyncio
    async def test_get_aggregates(self, polygon_client):
        """Test getting aggregate data."""
        mock_response = {
            "results": [
                {
                    "t": 1640995200000,  # Timestamp in milliseconds
                    "o": 100.0,
                    "h": 105.0,
                    "l": 99.0,
                    "c": 104.0,
                    "v": 1000000,
                }
            ]
        }
        
        with patch.object(polygon_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = mock_response
            
            result = await polygon_client.get_aggregates("AAPL")
            
            assert isinstance(result, pd.DataFrame)
            assert len(result) == 1
            assert result.iloc[0]["open"] == 100.0
            assert result.iloc[0]["close"] == 104.0


class TestAlpacaClient:
    """Test Alpaca client functionality."""
    
    @pytest.fixture
    def alpaca_client(self):
        """Create Alpaca client for testing."""
        return AlpacaClient(
            api_key="test_key",
            secret_key="test_secret",
            rate_limit_per_minute=200
        )
    
    @pytest.mark.asyncio
    async def test_initialization(self, alpaca_client):
        """Test client initialization."""
        assert alpaca_client.api_key == "test_key"
        assert alpaca_client.secret_key == "test_secret"
        assert alpaca_client.base_url == "https://data.alpaca.markets"
        assert "APCA-API-KEY-ID" in alpaca_client.headers
        assert "APCA-API-SECRET-KEY" in alpaca_client.headers
    
    @pytest.mark.asyncio
    async def test_get_assets(self, alpaca_client):
        """Test getting asset information."""
        mock_response = [
            {
                "symbol": "AAPL",
                "name": "Apple Inc.",
                "exchange": "NASDAQ",
                "class": "us_equity",
                "status": "active",
                "tradable": True,
                "marginable": True,
                "shortable": True,
            }
        ]
        
        with patch.object(alpaca_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = mock_response
            
            result = await alpaca_client.get_assets(["AAPL"])
            
            assert len(result) == 1
            assert result[0]["symbol"] == "AAPL"
            assert result[0]["name"] == "Apple Inc."
            assert result[0]["tradable"] is True
    
    @pytest.mark.asyncio
    async def test_get_latest_quotes(self, alpaca_client):
        """Test getting latest quotes."""
        mock_response = {
            "quotes": {
                "AAPL": {
                    "bp": 149.50,
                    "bs": 100,
                    "ap": 149.55,
                    "as": 200,
                    "t": "2023-12-25T15:30:00Z",
                }
            }
        }
        
        with patch.object(alpaca_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = mock_response
            
            result = await alpaca_client.get_latest_quotes(["AAPL"])
            
            assert "AAPL" in result
            assert result["AAPL"]["bid_price"] == 149.50
            assert result["AAPL"]["ask_price"] == 149.55
    
    @pytest.mark.asyncio
    async def test_get_bars(self, alpaca_client):
        """Test getting historical bars."""
        mock_response = {
            "bars": {
                "AAPL": [
                    {
                        "t": "2023-12-25T15:30:00Z",
                        "o": 100.0,
                        "h": 105.0,
                        "l": 99.0,
                        "c": 104.0,
                        "v": 1000000,
                    }
                ]
            }
        }
        
        with patch.object(alpaca_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = mock_response
            
            result = await alpaca_client.get_bars(["AAPL"])
            
            assert "AAPL" in result
            assert isinstance(result["AAPL"], pd.DataFrame)
            assert len(result["AAPL"]) == 1
            assert result["AAPL"].iloc[0]["open"] == 100.0
