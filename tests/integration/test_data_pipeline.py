"""
Integration tests for data pipeline.
Tests the complete data flow from sources to aggregation.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch
import pandas as pd

from src.quantum_edge.config.settings import QuantumEdgeConfig
from src.quantum_edge.data.sources.polygon_client import PolygonClient
from src.quantum_edge.data.sources.alpaca_client import AlpacaClient


@pytest.mark.integration
class TestDataPipelineIntegration:
    """Integration tests for data pipeline."""
    
    @pytest.fixture
    def config(self):
        """Get test configuration."""
        return QuantumEdgeConfig()
    
    @pytest.fixture
    def polygon_client(self):
        """Create Polygon client for testing."""
        return PolygonClient(api_key="test_key", rate_limit_per_minute=60)
    
    @pytest.fixture
    def alpaca_client(self):
        """Create Alpaca client for testing."""
        return AlpacaClient(
            api_key="test_key",
            secret_key="test_secret",
            rate_limit_per_minute=200
        )
    
    @pytest.mark.asyncio
    async def test_polygon_gainers_pipeline(self, polygon_client):
        """Test complete Polygon gainers data pipeline."""
        # Mock the API response
        mock_response = {
            "results": [
                {
                    "ticker": "AAPL",
                    "value": {"c": 150.0, "v": 1000000, "cp": 2.5, "h": 152.0, "l": 148.0, "o": 149.0},
                    "lastTrade": {"p": 150.0},
                    "prevDay": {"c": 146.34},
                },
                {
                    "ticker": "TSLA", 
                    "value": {"c": 250.0, "v": 2000000, "cp": 4.17, "h": 255.0, "l": 245.0, "o": 248.0},
                    "lastTrade": {"p": 250.0},
                    "prevDay": {"c": 240.0},
                }
            ]
        }
        
        with patch.object(polygon_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = mock_response
            
            # Test the complete pipeline
            result = await polygon_client.get_gainers(limit=10)
            
            # Validate results
            assert isinstance(result, pd.DataFrame)
            assert len(result) == 2
            
            # Check AAPL data
            aapl_row = result[result['symbol'] == 'AAPL'].iloc[0]
            assert aapl_row['price'] == 150.0
            assert aapl_row['prev_close'] == 146.34
            assert abs(aapl_row['gap_pct'] - 2.5) < 0.1  # Allow small floating point differences
            
            # Check TSLA data
            tsla_row = result[result['symbol'] == 'TSLA'].iloc[0]
            assert tsla_row['price'] == 250.0
            assert tsla_row['prev_close'] == 240.0
            assert abs(tsla_row['gap_pct'] - 4.17) < 0.1
    
    @pytest.mark.asyncio
    async def test_alpaca_assets_pipeline(self, alpaca_client):
        """Test complete Alpaca assets data pipeline."""
        # Mock the API response
        mock_response = [
            {
                "symbol": "AAPL",
                "name": "Apple Inc.",
                "exchange": "NASDAQ",
                "class": "us_equity",
                "status": "active",
                "tradable": True,
                "marginable": True,
                "shortable": True,
                "easy_to_borrow": True,
                "fractionable": True,
            },
            {
                "symbol": "TSLA",
                "name": "Tesla Inc.",
                "exchange": "NASDAQ", 
                "class": "us_equity",
                "status": "active",
                "tradable": True,
                "marginable": True,
                "shortable": False,
                "easy_to_borrow": False,
                "fractionable": True,
            }
        ]
        
        with patch.object(alpaca_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = mock_response
            
            # Test the complete pipeline
            result = await alpaca_client.get_assets(["AAPL", "TSLA"])
            
            # Validate results
            assert len(result) == 2
            
            # Check AAPL data
            aapl_data = next(item for item in result if item['symbol'] == 'AAPL')
            assert aapl_data['name'] == "Apple Inc."
            assert aapl_data['tradable'] is True
            assert aapl_data['shortable'] is True
            
            # Check TSLA data
            tsla_data = next(item for item in result if item['symbol'] == 'TSLA')
            assert tsla_data['name'] == "Tesla Inc."
            assert tsla_data['tradable'] is True
            assert tsla_data['shortable'] is False
    
    @pytest.mark.asyncio
    async def test_multi_source_data_aggregation(self, polygon_client, alpaca_client):
        """Test aggregating data from multiple sources."""
        # Mock Polygon response
        polygon_mock_response = {
            "results": [
                {
                    "ticker": "AAPL",
                    "value": {"c": 150.0, "v": 1000000, "cp": 2.5},
                    "lastTrade": {"p": 150.0},
                    "prevDay": {"c": 146.34},
                }
            ]
        }
        
        # Mock Alpaca response
        alpaca_mock_response = [
            {
                "symbol": "AAPL",
                "name": "Apple Inc.",
                "exchange": "NASDAQ",
                "class": "us_equity",
                "tradable": True,
                "shortable": True,
            }
        ]
        
        with patch.object(polygon_client, 'get', new_callable=AsyncMock) as polygon_mock:
            with patch.object(alpaca_client, 'get', new_callable=AsyncMock) as alpaca_mock:
                polygon_mock.return_value = polygon_mock_response
                alpaca_mock.return_value = alpaca_mock_response
                
                # Get data from both sources
                gainers_data = await polygon_client.get_gainers(limit=10)
                assets_data = await alpaca_client.get_assets(["AAPL"])
                
                # Validate we can combine the data
                assert len(gainers_data) == 1
                assert len(assets_data) == 1
                
                # Check that we have complementary data
                gainer = gainers_data.iloc[0]
                asset = assets_data[0]
                
                assert gainer['symbol'] == asset['symbol'] == "AAPL"
                assert gainer['price'] == 150.0
                assert asset['name'] == "Apple Inc."
                assert asset['shortable'] is True
    
    @pytest.mark.asyncio
    async def test_error_handling_pipeline(self, polygon_client):
        """Test error handling in data pipeline."""
        # Test with empty response
        with patch.object(polygon_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = {}
            
            result = await polygon_client.get_gainers()
            assert isinstance(result, pd.DataFrame)
            assert len(result) == 0
        
        # Test with malformed response
        with patch.object(polygon_client, 'get', new_callable=AsyncMock) as mock_get:
            mock_get.return_value = {"results": [{"invalid": "data"}]}
            
            result = await polygon_client.get_gainers()
            assert isinstance(result, pd.DataFrame)
            # Should handle malformed data gracefully
    
    def test_configuration_validation(self, config):
        """Test configuration validation."""
        # Test that configuration loads properly
        assert config.system.environment == "test"
        assert config.trading.gap_threshold_percent == 5.0
        assert config.trading.microcap_ceiling == 300000000

        # Test sector keywords
        keywords = config.get_sector_keywords()
        assert "ai_ml" in keywords
        assert "quantum" in keywords
        assert "biotech" in keywords

        # Test API validation (keys are set in conftest.py for testing)
        missing_keys = config.api.validate()
        assert isinstance(missing_keys, list)  # Should return a list
