"""
Pytest configuration and fixtures for QuantumEdge tests.
"""

import pytest
import asyncio
import os
import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

# Set test environment variables
os.environ["ENVIRONMENT"] = "test"
os.environ["DEBUG"] = "true"
os.environ["LOG_LEVEL"] = "DEBUG"

# Mock API keys for testing
os.environ["POLYGON_API_KEY"] = "test_polygon_key"
os.environ["ALPACA_API_KEY"] = "test_alpaca_key"
os.environ["ALPACA_SECRET_KEY"] = "test_alpaca_secret"
os.environ["ALPHA_VANTAGE_API_KEY"] = "test_alpha_vantage_key"
os.environ["BENZINGA_API_KEY"] = "test_benzinga_key"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    from src.quantum_edge.config.settings import QuantumEdgeConfig
    return QuantumEdgeConfig()


@pytest.fixture
def sample_market_data():
    """Sample market data for testing."""
    return {
        "AAPL": {
            "price": 150.0,
            "volume": 1000000,
            "change": 2.5,
            "change_pct": 1.69,
            "high": 152.0,
            "low": 148.0,
            "open": 149.0,
            "prev_close": 147.5,
        },
        "MSFT": {
            "price": 300.0,
            "volume": 500000,
            "change": -1.5,
            "change_pct": -0.50,
            "high": 302.0,
            "low": 299.0,
            "open": 301.5,
            "prev_close": 301.5,
        }
    }


@pytest.fixture
def sample_gainers_data():
    """Sample gainers data for testing."""
    return [
        {
            "symbol": "AAPL",
            "price": 150.0,
            "prev_close": 147.5,
            "gap_pct": 1.69,
            "volume": 1000000,
            "change": 2.5,
            "change_pct": 1.69,
        },
        {
            "symbol": "TSLA",
            "price": 250.0,
            "prev_close": 240.0,
            "gap_pct": 4.17,
            "volume": 2000000,
            "change": 10.0,
            "change_pct": 4.17,
        }
    ]


@pytest.fixture
def sample_news_data():
    """Sample news data for testing."""
    return [
        {
            "id": "1",
            "title": "Apple Reports Strong Quarterly Earnings",
            "publisher": "TechNews",
            "published_utc": "2023-12-25T15:30:00Z",
            "tickers": ["AAPL"],
            "description": "Apple Inc. reported better than expected earnings...",
        },
        {
            "id": "2", 
            "title": "Tesla Announces New Model",
            "publisher": "AutoNews",
            "published_utc": "2023-12-25T14:00:00Z",
            "tickers": ["TSLA"],
            "description": "Tesla unveiled its latest electric vehicle model...",
        }
    ]


@pytest.fixture
def sample_sentiment_data():
    """Sample sentiment data for testing."""
    return {
        "AAPL": {
            "sentiment_score": 0.8,
            "social_volume": 1500,
            "news_count": 5,
        },
        "TSLA": {
            "sentiment_score": 0.6,
            "social_volume": 2000,
            "news_count": 8,
        }
    }


# Async test utilities
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "asyncio: mark test as async"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


# Skip integration tests by default
def pytest_collection_modifyitems(config, items):
    """Modify test collection to handle markers."""
    if config.getoption("--integration"):
        # Run integration tests
        return
    
    skip_integration = pytest.mark.skip(reason="need --integration option to run")
    for item in items:
        if "integration" in item.keywords:
            item.add_marker(skip_integration)


def pytest_addoption(parser):
    """Add custom command line options."""
    parser.addoption(
        "--integration",
        action="store_true",
        default=False,
        help="run integration tests"
    )
    parser.addoption(
        "--slow",
        action="store_true", 
        default=False,
        help="run slow tests"
    )
