#!/usr/bin/env python3
"""
QuantumEdge Discovery Engine Startup Script
"""

import asyncio
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from quantum_edge.config.settings import QuantumEdgeConfig
from quantum_edge.config.logging_config import setup_logging
from quantum_edge.data.sources.polygon_client import PolygonClient
from quantum_edge.data.sources.alpha_vantage_client import AlphaVantageClient
from quantum_edge.data.sources.benzinga_client import BenzingaClient
from quantum_edge.data.aggregators.market_data_aggregator import MarketDataAggregator
from quantum_edge.discovery.microcap_universe import MicrocapUniverse
from quantum_edge.discovery.catalyst_detector import CatalystDetector
from quantum_edge.discovery.growth_scorer import GrowthScorer
from quantum_edge.discovery.discovery_engine import DiscoveryEngine

async def main():
    """Run discovery engine."""
    # Setup logging
    setup_logging(log_level="INFO")
    
    # Initialize configuration
    config = QuantumEdgeConfig()
    
    # Initialize data sources
    polygon_client = PolygonClient(api_key=config.api.polygon_api_key) if config.api.polygon_api_key else None
    alpha_vantage_client = AlphaVantageClient(api_key=config.api.alpha_vantage_api_key) if config.api.alpha_vantage_api_key else None
    benzinga_client = BenzingaClient(api_key=config.api.benzinga_api_key) if config.api.benzinga_api_key else None
    
    # Initialize aggregator
    aggregator = MarketDataAggregator(
        polygon_client=polygon_client,
        alpha_vantage_client=alpha_vantage_client,
        benzinga_client=benzinga_client
    )
    
    # Initialize discovery components
    microcap_universe = MicrocapUniverse(polygon_client=polygon_client)
    catalyst_detector = CatalystDetector()
    growth_scorer = GrowthScorer()
    
    # Initialize discovery engine
    discovery_engine = DiscoveryEngine(
        market_data_aggregator=aggregator,
        microcap_universe=microcap_universe,
        catalyst_detector=catalyst_detector,
        growth_scorer=growth_scorer
    )
    
    print("🚀 Starting QuantumEdge Discovery Engine...")
    
    # Run discovery
    results = await discovery_engine.discover_growth_opportunities(
        force_refresh_universe=True,
        max_results=50
    )
    
    print(f"✅ Discovery completed!")
    print(f"📊 Found {len(results.get('top_opportunities', []))} opportunities")
    
    # Cleanup
    await aggregator.close()

if __name__ == '__main__':
    asyncio.run(main())
