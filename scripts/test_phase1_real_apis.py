#!/usr/bin/env python3
"""
Phase 1 Real API Testing Script
Tests the complete Phase 1 implementation with real API calls.
"""

import asyncio
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from quantum_edge.config.settings import QuantumEdgeConfig
from quantum_edge.config.logging_config import setup_logging
from quantum_edge.data.sources.polygon_client import PolygonClient
from quantum_edge.data.sources.alpaca_client import AlpacaClient
from quantum_edge.data.aggregators.market_data_aggregator import MarketDataAggregator


async def test_configuration():
    """Test configuration loading and validation."""
    print("=" * 60)
    print("TESTING CONFIGURATION")
    print("=" * 60)
    
    try:
        config = QuantumEdgeConfig()
        print(f"✓ Configuration loaded successfully")
        print(f"  Environment: {config.system.environment}")
        print(f"  Gap threshold: {config.trading.gap_threshold_percent}%")
        print(f"  Microcap ceiling: ${config.trading.microcap_ceiling:,}")
        print(f"  Max position size: {config.trading.max_position_size_percent}%")
        
        # Test API key validation
        missing_keys = config.api.validate()
        if missing_keys:
            print(f"⚠ Missing API keys: {missing_keys}")
        else:
            print("✓ All required API keys present")
        
        # Test sector keywords
        keywords = config.get_sector_keywords()
        print(f"✓ Sector keywords loaded: {list(keywords.keys())}")
        
        return config
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return None


async def test_polygon_client(config):
    """Test Polygon.io client functionality."""
    print("\n" + "=" * 60)
    print("TESTING POLYGON.IO CLIENT")
    print("=" * 60)
    
    if not config.api.polygon_api_key:
        print("⚠ Polygon API key not available, skipping tests")
        return None
    
    try:
        polygon = PolygonClient(
            api_key=config.api.polygon_api_key,
            rate_limit_per_minute=5
        )
        
        # Test connection
        print("Testing connection...")
        connected = await polygon.test_connection()
        if connected:
            print("✓ Polygon connection successful")
        else:
            print("✗ Polygon connection failed")
            return None
        
        # Test getting gainers
        print("Testing gainers endpoint...")
        gainers = await polygon.get_gainers(limit=10)
        print(f"✓ Retrieved {len(gainers)} gainers")
        if not gainers.empty:
            print(f"  Top gainer: {gainers.iloc[0]['symbol']} (+{gainers.iloc[0]['gap_pct']:.1f}%)")
        
        # Test ticker details for a major stock
        print("Testing ticker details...")
        details = await polygon.get_ticker_details("AAPL")
        if details:
            print(f"✓ Retrieved details for AAPL: {details.get('name', 'N/A')}")
        
        # Test news
        print("Testing news endpoint...")
        news = await polygon.get_news(limit=5)
        print(f"✓ Retrieved {len(news)} news articles")
        
        await polygon.close()
        return polygon
        
    except Exception as e:
        print(f"✗ Polygon client test failed: {e}")
        return None


async def test_alpaca_client(config):
    """Test Alpaca client functionality."""
    print("\n" + "=" * 60)
    print("TESTING ALPACA CLIENT")
    print("=" * 60)

    # Check for Alpaca keys - we need both API key and secret
    alpaca_api_key = os.getenv("ALPACA_API_KEY") or "dummy_key"  # Use dummy for paper trading
    alpaca_secret_key = os.getenv("ALPACA_SECRET_KEY")

    if not alpaca_secret_key:
        print("⚠ Alpaca secret key not available, skipping tests")
        return None
    
    try:
        alpaca = AlpacaClient(
            api_key=alpaca_api_key,
            secret_key=alpaca_secret_key,
            rate_limit_per_minute=200
        )
        
        # Test connection
        print("Testing connection...")
        connected = await alpaca.test_connection()
        if connected:
            print("✓ Alpaca connection successful")
        else:
            print("✗ Alpaca connection failed")
            return None
        
        # Test getting assets
        print("Testing assets endpoint...")
        assets = await alpaca.get_assets(["AAPL", "MSFT", "GOOGL"])
        print(f"✓ Retrieved {len(assets)} assets")
        if assets:
            print(f"  Sample asset: {assets[0]['symbol']} - {assets[0]['name']}")
        
        # Test latest quotes
        print("Testing latest quotes...")
        quotes = await alpaca.get_latest_quotes(["AAPL", "MSFT"])
        print(f"✓ Retrieved quotes for {len(quotes)} symbols")
        
        # Test snapshots
        print("Testing snapshots...")
        snapshots = await alpaca.get_snapshots(["AAPL", "MSFT"])
        print(f"✓ Retrieved snapshots for {len(snapshots)} symbols")
        
        await alpaca.close()
        return alpaca
        
    except Exception as e:
        print(f"✗ Alpaca client test failed: {e}")
        return None


async def test_market_data_aggregator(config):
    """Test market data aggregator with real APIs."""
    print("\n" + "=" * 60)
    print("TESTING MARKET DATA AGGREGATOR")
    print("=" * 60)
    
    try:
        # Initialize clients
        polygon = None
        alpaca = None
        
        if config.api.polygon_api_key:
            polygon = PolygonClient(
                api_key=config.api.polygon_api_key,
                rate_limit_per_minute=5
            )
        
        alpaca_secret_key = os.getenv("ALPACA_SECRET_KEY")
        if alpaca_secret_key:
            alpaca_api_key = os.getenv("ALPACA_API_KEY") or "dummy_key"
            alpaca = AlpacaClient(
                api_key=alpaca_api_key,
                secret_key=alpaca_secret_key,
                rate_limit_per_minute=200
            )
        
        # Create aggregator
        aggregator = MarketDataAggregator(
            polygon_client=polygon,
            alpaca_client=alpaca
        )
        
        print(f"✓ Aggregator initialized with sources: {aggregator.available_sources}")
        
        # Test premarket gappers
        if polygon:
            print("Testing premarket gappers...")
            gappers = await aggregator.get_premarket_gappers(
                gap_threshold=2.0,  # Lower threshold for testing
                limit=10,
                max_price=50.0
            )
            print(f"✓ Found {len(gappers)} gappers")
            if not gappers.empty:
                top_gapper = gappers.iloc[0]
                print(f"  Top gapper: {top_gapper['symbol']} (+{top_gapper['gap_pct']:.1f}%)")
        
        # Test fundamental enrichment
        test_symbols = ["AAPL", "MSFT", "GOOGL"]
        print(f"Testing fundamental enrichment for {test_symbols}...")
        fundamentals = await aggregator.enrich_with_fundamentals(
            symbols=test_symbols,
            include_assets=True,
            include_details=True
        )
        print(f"✓ Enriched {len(fundamentals)} symbols with fundamental data")
        
        # Show sample enriched data
        if "AAPL" in fundamentals:
            aapl_data = fundamentals["AAPL"]
            print(f"  AAPL data sources: {aapl_data['data_sources']}")
            print(f"  AAPL name: {aapl_data.get('name', 'N/A')}")
            print(f"  AAPL market cap: ${aapl_data.get('market_cap', 0):,}")
        
        # Test market snapshots
        print("Testing market snapshots...")
        snapshots = await aggregator.get_market_snapshots(
            symbols=test_symbols,
            include_quotes=True,
            include_trades=True
        )
        print(f"✓ Retrieved snapshots for {len(snapshots)} symbols")
        
        # Show sample snapshot data
        if "AAPL" in snapshots:
            aapl_snapshot = snapshots["AAPL"]
            print(f"  AAPL snapshot sources: {aapl_snapshot['data_sources']}")
            print(f"  AAPL price: ${aapl_snapshot.get('price', 0):.2f}")
            print(f"  AAPL volume: {aapl_snapshot.get('volume', 0):,}")
        
        await aggregator.close()
        return True
        
    except Exception as e:
        print(f"✗ Market data aggregator test failed: {e}")
        return False


async def test_end_to_end_workflow(config):
    """Test complete end-to-end workflow."""
    print("\n" + "=" * 60)
    print("TESTING END-TO-END WORKFLOW")
    print("=" * 60)
    
    try:
        # Initialize system
        polygon = None
        alpaca = None
        
        if config.api.polygon_api_key:
            polygon = PolygonClient(
                api_key=config.api.polygon_api_key,
                rate_limit_per_minute=5
            )
        
        alpaca_secret_key = os.getenv("ALPACA_SECRET_KEY")
        if alpaca_secret_key:
            alpaca_api_key = os.getenv("ALPACA_API_KEY") or "dummy_key"
            alpaca = AlpacaClient(
                api_key=alpaca_api_key,
                secret_key=alpaca_secret_key,
                rate_limit_per_minute=200
            )
        
        async with MarketDataAggregator(polygon, alpaca) as aggregator:
            print("✓ System initialized")
            
            # Step 1: Find gappers
            print("Step 1: Finding pre-market gappers...")
            gappers = await aggregator.get_premarket_gappers(
                gap_threshold=config.trading.gap_threshold_percent,
                limit=20,
                max_price=config.trading.penny_stock_limit
            )
            print(f"✓ Found {len(gappers)} qualifying gappers")
            
            if gappers.empty:
                print("⚠ No gappers found, using sample symbols for testing")
                sample_symbols = ["AAPL", "MSFT", "GOOGL"]
            else:
                sample_symbols = gappers['symbol'].head(5).tolist()
                print(f"  Top gappers: {', '.join(sample_symbols)}")
            
            # Step 2: Enrich with fundamentals
            print("Step 2: Enriching with fundamental data...")
            fundamentals = await aggregator.enrich_with_fundamentals(
                symbols=sample_symbols,
                include_assets=True,
                include_details=True
            )
            print(f"✓ Enriched {len(fundamentals)} symbols")
            
            # Step 3: Get market snapshots
            print("Step 3: Getting market snapshots...")
            snapshots = await aggregator.get_market_snapshots(
                symbols=sample_symbols,
                include_quotes=True,
                include_trades=True
            )
            print(f"✓ Retrieved snapshots for {len(snapshots)} symbols")
            
            # Step 4: Combine and analyze data
            print("Step 4: Combining and analyzing data...")
            combined_data = []
            
            for symbol in sample_symbols:
                if symbol in fundamentals and symbol in snapshots:
                    fundamental = fundamentals[symbol]
                    snapshot = snapshots[symbol]
                    
                    combined_data.append({
                        'symbol': symbol,
                        'name': fundamental.get('name', 'N/A'),
                        'price': snapshot.get('price', 0),
                        'volume': snapshot.get('volume', 0),
                        'market_cap': fundamental.get('market_cap', 0),
                        'tradable': fundamental.get('tradable', False),
                        'shortable': fundamental.get('shortable', False),
                        'data_sources': len(set(fundamental.get('data_sources', []) + snapshot.get('data_sources', [])))
                    })
            
            print(f"✓ Combined data for {len(combined_data)} symbols")
            
            # Display results
            print("\nCOMBINED RESULTS:")
            print("-" * 80)
            for data in combined_data:
                print(f"{data['symbol']:6} | {data['name'][:20]:20} | ${data['price']:8.2f} | "
                      f"{data['volume']:10,} | ${data['market_cap']:12,} | {data['data_sources']} sources")
            
            return True
            
    except Exception as e:
        print(f"✗ End-to-end workflow test failed: {e}")
        return False


async def main():
    """Main test execution."""
    print("QUANTUMEDGE PHASE 1 REAL API TESTING")
    print("=" * 60)
    
    # Setup logging
    setup_logging(log_level="INFO")
    
    # Test configuration
    config = await test_configuration()
    if not config:
        print("Configuration test failed. Exiting.")
        return False
    
    # Test individual clients
    polygon_result = await test_polygon_client(config)
    alpaca_result = await test_alpaca_client(config)
    
    # Test aggregator
    aggregator_result = await test_market_data_aggregator(config)
    
    # Test end-to-end workflow
    workflow_result = await test_end_to_end_workflow(config)
    
    # Summary
    print("\n" + "=" * 60)
    print("PHASE 1 TEST SUMMARY")
    print("=" * 60)
    
    tests = [
        ("Configuration", config is not None),
        ("Polygon Client", polygon_result is not None),
        ("Alpaca Client", alpaca_result is not None),
        ("Market Data Aggregator", aggregator_result),
        ("End-to-End Workflow", workflow_result),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:25} | {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"OVERALL RESULT: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Phase 1 implementation is working correctly!")
        return True
    else:
        print("⚠ Some tests failed. Check the output above for details.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
