#!/usr/bin/env python3
"""
A/B Testing Framework for Discovery Thresholds
Compare different discovery thresholds against market performance.
"""

import asyncio
import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from quantum_edge.discovery.growth_scorer import GrowthScorer
from quantum_edge.risk.risk_manager import RiskManager


class ABTestingFramework:
    """A/B testing framework for discovery optimization."""
    
    def __init__(self):
        """Initialize A/B testing framework."""
        self.test_results = {}
        self.market_winners = {}
        
        print("🧪 A/B TESTING FRAMEWORK - DISCOVERY OPTIMIZATION")
        print("=" * 70)
        print(f"Testing started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
    
    async def execute_ab_testing(self):
        """Execute comprehensive A/B testing."""
        try:
            # 1. Define Market Winners Dataset
            self.define_market_winners()
            
            # 2. Test Different Discovery Thresholds
            await self.test_discovery_thresholds()
            
            # 3. Test Sector-Specific Thresholds
            await self.test_sector_thresholds()
            
            # 4. Test Risk-Adjusted Thresholds
            await self.test_risk_adjusted_thresholds()
            
            # 5. Generate A/B Testing Report
            self.generate_ab_testing_report()
            
        except Exception as e:
            print(f"❌ A/B testing error: {e}")
            import traceback
            traceback.print_exc()
    
    def define_market_winners(self):
        """Define market winners for backtesting validation."""
        print("\n📈 DEFINING MARKET WINNERS DATASET")
        print("-" * 50)
        
        # Real market winners from recent periods (2024 examples)
        self.market_winners = {
            # High performers (50%+ gains)
            "NVDA": {
                "sector": "ai_ml",
                "market_cap": 1_000_000_000_000,  # Large cap for comparison
                "actual_gain": 200,  # 200% gain
                "catalyst": "AI chip demand surge",
                "discovery_period": "Q1 2024"
            },
            "SMCI": {
                "sector": "ai_ml", 
                "market_cap": 50_000_000_000,
                "actual_gain": 150,  # 150% gain
                "catalyst": "AI server demand",
                "discovery_period": "Q1 2024"
            },
            "SAVA": {
                "sector": "biotech",
                "market_cap": 200_000_000,
                "actual_gain": 75,   # 75% gain
                "catalyst": "Alzheimer's drug progress",
                "discovery_period": "Q2 2024"
            },
            "AVXL": {
                "sector": "biotech",
                "market_cap": 400_000_000,
                "actual_gain": 120,  # 120% gain
                "catalyst": "Clinical trial results",
                "discovery_period": "Q2 2024"
            },
            "PLUG": {
                "sector": "clean_energy",
                "market_cap": 800_000_000,
                "actual_gain": 60,   # 60% gain
                "catalyst": "Hydrogen infrastructure deals",
                "discovery_period": "Q3 2024"
            },
            "RKLB": {
                "sector": "space_tech",
                "market_cap": 600_000_000,
                "actual_gain": 180,  # 180% gain
                "catalyst": "Successful launches",
                "discovery_period": "Q3 2024"
            },
            "ASTR": {
                "sector": "space_tech",
                "market_cap": 300_000_000,
                "actual_gain": 90,   # 90% gain
                "catalyst": "Launch contracts",
                "discovery_period": "Q4 2024"
            },
            "TENB": {
                "sector": "cybersecurity",
                "market_cap": 1_500_000_000,
                "actual_gain": 55,   # 55% gain
                "catalyst": "Enterprise security demand",
                "discovery_period": "Q4 2024"
            }
        }
        
        print(f"   📊 Market Winners Dataset: {len(self.market_winners)} stocks")
        print(f"   🎯 Sectors: {len(set(w['sector'] for w in self.market_winners.values()))}")
        print(f"   📈 Average Gain: {sum(w['actual_gain'] for w in self.market_winners.values()) / len(self.market_winners):.1f}%")
        
        # Filter to microcap range for primary testing
        self.microcap_winners = {
            symbol: data for symbol, data in self.market_winners.items()
            if 50_000_000 <= data["market_cap"] <= 2_000_000_000
        }
        
        print(f"   🔍 Microcap Winners: {len(self.microcap_winners)} stocks")
    
    async def test_discovery_thresholds(self):
        """Test different discovery thresholds."""
        print("\n🎯 TESTING DISCOVERY THRESHOLDS")
        print("-" * 50)
        
        thresholds = [40, 50, 60, 70, 80]
        threshold_results = {}
        
        growth_scorer = GrowthScorer()
        
        for threshold in thresholds:
            print(f"\n   🧪 Testing Threshold: {threshold}")
            
            discovered_winners = []
            total_scores = []
            
            for symbol, winner_data in self.microcap_winners.items():
                # Score the winner
                company_data = {
                    "symbol": symbol,
                    "market_cap": winner_data["market_cap"],
                    "sector_category": winner_data["sector"]
                }
                
                growth_score = await growth_scorer.score_growth_potential(
                    symbol, company_data, [], None, None, None
                )
                
                # Apply sector boost (as in comprehensive discovery)
                sector_boost = self._get_sector_boost(winner_data["sector"])
                adjusted_score = min(100, growth_score.total_score + sector_boost)
                
                total_scores.append(adjusted_score)
                
                # Check if would be discovered
                if adjusted_score >= threshold:
                    discovered_winners.append({
                        "symbol": symbol,
                        "score": adjusted_score,
                        "actual_gain": winner_data["actual_gain"],
                        "sector": winner_data["sector"]
                    })
                    print(f"     ✅ {symbol}: Score {adjusted_score:.1f} (Actual: {winner_data['actual_gain']}%)")
                else:
                    print(f"     ❌ {symbol}: Score {adjusted_score:.1f} < {threshold}")
            
            # Calculate metrics
            discovery_rate = len(discovered_winners) / len(self.microcap_winners) * 100
            avg_gain_discovered = sum(w["actual_gain"] for w in discovered_winners) / len(discovered_winners) if discovered_winners else 0
            avg_score = sum(total_scores) / len(total_scores) if total_scores else 0
            
            threshold_results[threshold] = {
                "discovery_rate": discovery_rate,
                "discovered_count": len(discovered_winners),
                "avg_gain_discovered": avg_gain_discovered,
                "avg_score": avg_score,
                "discovered_winners": discovered_winners
            }
            
            print(f"     📊 Discovery Rate: {discovery_rate:.1f}%")
            print(f"     📈 Avg Gain of Discovered: {avg_gain_discovered:.1f}%")
        
        self.test_results["threshold_testing"] = threshold_results
    
    def _get_sector_boost(self, sector):
        """Get sector-specific boost for scoring."""
        sector_boosts = {
            "biotech": 15,
            "ai_ml": 12,
            "space_tech": 10,
            "clean_energy": 8,
            "cybersecurity": 6,
            "fintech": 5
        }
        return sector_boosts.get(sector, 0)
    
    async def test_sector_thresholds(self):
        """Test sector-specific thresholds."""
        print("\n🎯 TESTING SECTOR-SPECIFIC THRESHOLDS")
        print("-" * 50)
        
        # Optimized thresholds per sector based on risk/reward
        sector_thresholds = {
            "biotech": 35,        # Lower threshold for high-risk/high-reward
            "ai_ml": 45,          # Medium threshold for growth sector
            "space_tech": 40,     # Lower for emerging sector
            "clean_energy": 50,   # Medium for policy-driven sector
            "cybersecurity": 55,  # Higher for stable sector
            "fintech": 60         # Higher for mature sector
        }
        
        growth_scorer = GrowthScorer()
        sector_results = {}
        
        for sector, threshold in sector_thresholds.items():
            print(f"\n   🧪 Testing {sector.replace('_', ' ').title()}: Threshold {threshold}")
            
            sector_winners = {
                symbol: data for symbol, data in self.microcap_winners.items()
                if data["sector"] == sector
            }
            
            if not sector_winners:
                print(f"     ⚠️ No winners in {sector} sector")
                continue
            
            discovered_winners = []
            
            for symbol, winner_data in sector_winners.items():
                company_data = {
                    "symbol": symbol,
                    "market_cap": winner_data["market_cap"],
                    "sector_category": winner_data["sector"]
                }
                
                growth_score = await growth_scorer.score_growth_potential(
                    symbol, company_data, [], None, None, None
                )
                
                sector_boost = self._get_sector_boost(sector)
                adjusted_score = min(100, growth_score.total_score + sector_boost)
                
                if adjusted_score >= threshold:
                    discovered_winners.append({
                        "symbol": symbol,
                        "score": adjusted_score,
                        "actual_gain": winner_data["actual_gain"]
                    })
                    print(f"     ✅ {symbol}: Score {adjusted_score:.1f} (Actual: {winner_data['actual_gain']}%)")
                else:
                    print(f"     ❌ {symbol}: Score {adjusted_score:.1f} < {threshold}")
            
            discovery_rate = len(discovered_winners) / len(sector_winners) * 100 if sector_winners else 0
            avg_gain = sum(w["actual_gain"] for w in discovered_winners) / len(discovered_winners) if discovered_winners else 0
            
            sector_results[sector] = {
                "threshold": threshold,
                "discovery_rate": discovery_rate,
                "discovered_count": len(discovered_winners),
                "total_winners": len(sector_winners),
                "avg_gain_discovered": avg_gain
            }
            
            print(f"     📊 Discovery Rate: {discovery_rate:.1f}%")
        
        self.test_results["sector_thresholds"] = sector_results
    
    async def test_risk_adjusted_thresholds(self):
        """Test risk-adjusted thresholds."""
        print("\n🎯 TESTING RISK-ADJUSTED THRESHOLDS")
        print("-" * 50)
        
        growth_scorer = GrowthScorer()
        risk_manager = RiskManager()
        
        # Risk-adjusted discovery: Lower threshold for lower risk stocks
        risk_adjusted_results = {}
        
        for symbol, winner_data in self.microcap_winners.items():
            company_data = {
                "symbol": symbol,
                "market_cap": winner_data["market_cap"],
                "sector_category": winner_data["sector"],
                "avg_volume": 1_000_000  # Estimated
            }
            
            # Get growth score
            growth_score = await growth_scorer.score_growth_potential(
                symbol, company_data, [], None, None, None
            )
            
            # Get risk assessment
            risk_assessment = await risk_manager.assess_risk(symbol, company_data, None, None)
            
            # Calculate risk-adjusted threshold
            base_threshold = 50
            risk_adjustment = (100 - risk_assessment.risk_score) * 0.3  # Lower risk = lower threshold
            adjusted_threshold = max(30, base_threshold - risk_adjustment)
            
            # Apply sector boost
            sector_boost = self._get_sector_boost(winner_data["sector"])
            final_score = min(100, growth_score.total_score + sector_boost)
            
            would_discover = final_score >= adjusted_threshold
            
            risk_adjusted_results[symbol] = {
                "growth_score": final_score,
                "risk_score": risk_assessment.risk_score,
                "adjusted_threshold": adjusted_threshold,
                "would_discover": would_discover,
                "actual_gain": winner_data["actual_gain"],
                "sector": winner_data["sector"]
            }
            
            status = "✅ DISCOVERED" if would_discover else "❌ MISSED"
            print(f"   {symbol}: Score {final_score:.1f}, Risk {risk_assessment.risk_score:.1f}, "
                  f"Threshold {adjusted_threshold:.1f} - {status}")
        
        # Calculate overall performance
        discovered_count = sum(1 for r in risk_adjusted_results.values() if r["would_discover"])
        discovery_rate = discovered_count / len(risk_adjusted_results) * 100
        
        discovered_gains = [r["actual_gain"] for r in risk_adjusted_results.values() if r["would_discover"]]
        avg_gain_discovered = sum(discovered_gains) / len(discovered_gains) if discovered_gains else 0
        
        self.test_results["risk_adjusted"] = {
            "discovery_rate": discovery_rate,
            "discovered_count": discovered_count,
            "avg_gain_discovered": avg_gain_discovered,
            "results": risk_adjusted_results
        }
        
        print(f"\n   📊 Risk-Adjusted Discovery Rate: {discovery_rate:.1f}%")
        print(f"   📈 Average Gain of Discovered: {avg_gain_discovered:.1f}%")
    
    def generate_ab_testing_report(self):
        """Generate comprehensive A/B testing report."""
        print("\n" + "=" * 70)
        print("🧪 A/B TESTING COMPREHENSIVE REPORT")
        print("=" * 70)
        
        # Threshold testing summary
        threshold_results = self.test_results.get("threshold_testing", {})
        
        print(f"\n📊 DISCOVERY THRESHOLD ANALYSIS:")
        print(f"{'Threshold':<10} {'Discovery Rate':<15} {'Avg Gain':<12} {'Count':<8}")
        print("-" * 50)
        
        best_threshold = None
        best_score = 0
        
        for threshold, results in threshold_results.items():
            discovery_rate = results["discovery_rate"]
            avg_gain = results["avg_gain_discovered"]
            count = results["discovered_count"]
            
            # Calculate composite score (discovery rate * avg gain / 100)
            composite_score = (discovery_rate * avg_gain) / 100
            
            if composite_score > best_score:
                best_score = composite_score
                best_threshold = threshold
            
            print(f"{threshold:<10} {discovery_rate:<15.1f} {avg_gain:<12.1f} {count:<8}")
        
        print(f"\n🏆 OPTIMAL THRESHOLD: {best_threshold} (Composite Score: {best_score:.1f})")
        
        # Sector threshold analysis
        sector_results = self.test_results.get("sector_thresholds", {})
        
        print(f"\n🎯 SECTOR-SPECIFIC THRESHOLD ANALYSIS:")
        print(f"{'Sector':<15} {'Threshold':<10} {'Discovery Rate':<15} {'Avg Gain':<12}")
        print("-" * 60)
        
        for sector, results in sector_results.items():
            sector_name = sector.replace('_', ' ').title()
            threshold = results["threshold"]
            discovery_rate = results["discovery_rate"]
            avg_gain = results["avg_gain_discovered"]
            
            print(f"{sector_name:<15} {threshold:<10} {discovery_rate:<15.1f} {avg_gain:<12.1f}")
        
        # Risk-adjusted analysis
        risk_results = self.test_results.get("risk_adjusted", {})
        
        print(f"\n🛡️ RISK-ADJUSTED THRESHOLD ANALYSIS:")
        print(f"   Discovery Rate: {risk_results.get('discovery_rate', 0):.1f}%")
        print(f"   Average Gain: {risk_results.get('avg_gain_discovered', 0):.1f}%")
        print(f"   Discovered Count: {risk_results.get('discovered_count', 0)}")
        
        # Recommendations
        print(f"\n💡 A/B TESTING RECOMMENDATIONS:")
        
        if best_threshold:
            print(f"   🎯 OPTIMAL CONFIGURATION:")
            print(f"     • Universal Threshold: {best_threshold}")
            print(f"     • Expected Discovery Rate: {threshold_results[best_threshold]['discovery_rate']:.1f}%")
            print(f"     • Expected Average Gain: {threshold_results[best_threshold]['avg_gain_discovered']:.1f}%")
        
        print(f"\n   🎯 SECTOR-SPECIFIC OPTIMIZATION:")
        for sector, results in sector_results.items():
            if results["discovery_rate"] > 50:  # Only recommend if >50% discovery rate
                print(f"     • {sector.replace('_', ' ').title()}: Use threshold {results['threshold']}")
        
        print(f"\n   🛡️ RISK-ADJUSTED APPROACH:")
        if risk_results.get("discovery_rate", 0) > 60:
            print(f"     • Risk-adjusted thresholds show {risk_results['discovery_rate']:.1f}% discovery rate")
            print(f"     • Recommended for conservative approach")
        else:
            print(f"     • Risk-adjusted approach may be too conservative")
        
        # Final recommendation
        print(f"\n🚀 FINAL RECOMMENDATION:")
        
        # Compare approaches
        universal_performance = threshold_results.get(best_threshold, {}).get("discovery_rate", 0) if best_threshold else 0
        sector_avg_performance = sum(r["discovery_rate"] for r in sector_results.values()) / len(sector_results) if sector_results else 0
        risk_performance = risk_results.get("discovery_rate", 0)
        
        if sector_avg_performance > universal_performance and sector_avg_performance > risk_performance:
            print(f"   🎯 USE SECTOR-SPECIFIC THRESHOLDS")
            print(f"     • Best overall performance: {sector_avg_performance:.1f}% discovery rate")
            print(f"     • Tailored to sector characteristics")
        elif universal_performance > risk_performance:
            print(f"   🎯 USE UNIVERSAL THRESHOLD: {best_threshold}")
            print(f"     • Simplest implementation")
            print(f"     • Good performance: {universal_performance:.1f}% discovery rate")
        else:
            print(f"   🎯 USE RISK-ADJUSTED THRESHOLDS")
            print(f"     • Most conservative approach")
            print(f"     • Better risk management")
        
        print("\n" + "=" * 70)


async def main():
    """Run A/B testing framework."""
    tester = ABTestingFramework()
    await tester.execute_ab_testing()


if __name__ == "__main__":
    asyncio.run(main())
