#!/usr/bin/env python3
"""
Elite Environment Configuration Validation
Comprehensive validation of all API keys, connectivity, and configuration.
"""

import asyncio
import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv
from datetime import datetime
import aiohttp

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from quantum_edge.config.settings import QuantumEdgeConfig
from quantum_edge.config.logging_config import setup_logging


class EnvironmentValidator:
    """Elite environment validation with zero tolerance for failures."""
    
    def __init__(self):
        """Initialize validator."""
        self.config = QuantumEdgeConfig()
        self.validation_results = {}
        self.critical_failures = []
        
        print("🔬 ELITE ENVIRONMENT CONFIGURATION VALIDATION")
        print("=" * 70)
        print(f"Validation started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
    
    async def validate_all(self):
        """Execute comprehensive environment validation."""
        try:
            # 1. Environment Variables Validation
            await self.validate_environment_variables()
            
            # 2. API Key Configuration Validation
            await self.validate_api_keys()
            
            # 3. API Connectivity Testing
            await self.validate_api_connectivity()
            
            # 4. Rate Limiting Validation
            await self.validate_rate_limiting()
            
            # 5. Configuration System Validation
            await self.validate_configuration_system()
            
            # 6. Generate Comprehensive Report
            self.generate_validation_report()
            
        except Exception as e:
            print(f"❌ CRITICAL VALIDATION FAILURE: {e}")
            import traceback
            traceback.print_exc()
    
    async def validate_environment_variables(self):
        """Validate all required environment variables."""
        print("\n🔧 ENVIRONMENT VARIABLES VALIDATION")
        print("-" * 50)
        
        required_vars = {
            "POLYGON_API_KEY": "Critical - Market data source",
            "ALPHA_VANTAGE_API_KEY": "Critical - Fundamental data",
            "BENZINGA_API_KEY": "Critical - News and sentiment"
        }
        
        optional_vars = {
            "SEC_API_KEY": "Optional - SEC filings",
            "GOOGLE_PATENTS_API_KEY": "Optional - Patent data",
            "USPTO_API_KEY": "Optional - USPTO patent data",
            "QUANTUM_EDGE_ENV": "System environment",
            "LOG_LEVEL": "Logging configuration"
        }
        
        env_results = {"required": {}, "optional": {}}
        
        # Check required variables
        for var, description in required_vars.items():
            value = os.getenv(var)
            if value and len(value.strip()) > 10:  # Reasonable key length
                env_results["required"][var] = "✅ CONFIGURED"
                print(f"   ✅ {var}: {description}")
            else:
                env_results["required"][var] = "❌ MISSING/INVALID"
                print(f"   ❌ {var}: {description} - MISSING OR INVALID")
                self.critical_failures.append(f"Missing required API key: {var}")
        
        # Check optional variables
        for var, description in optional_vars.items():
            value = os.getenv(var)
            if value:
                env_results["optional"][var] = "✅ CONFIGURED"
                print(f"   ✅ {var}: {description}")
            else:
                env_results["optional"][var] = "⚠️ NOT SET"
                print(f"   ⚠️ {var}: {description} - Not configured")
        
        self.validation_results["environment_variables"] = env_results
        
        # Check .env file
        env_file = Path(".env")
        if env_file.exists():
            print(f"   ✅ .env file exists ({env_file.stat().st_size} bytes)")
        else:
            print("   ❌ .env file not found")
            self.critical_failures.append(".env file not found")
    
    async def validate_api_keys(self):
        """Validate API key format and basic structure."""
        print("\n🔑 API KEY FORMAT VALIDATION")
        print("-" * 50)
        
        api_key_patterns = {
            "POLYGON_API_KEY": {"min_length": 20, "pattern": "alphanumeric"},
            "ALPHA_VANTAGE_API_KEY": {"min_length": 15, "pattern": "alphanumeric"},
            "BENZINGA_API_KEY": {"min_length": 30, "pattern": "alphanumeric"}
        }
        
        key_validation = {}
        
        for key_name, requirements in api_key_patterns.items():
            key_value = os.getenv(key_name)
            
            if not key_value:
                key_validation[key_name] = "❌ MISSING"
                print(f"   ❌ {key_name}: Missing")
                continue
            
            # Length validation
            if len(key_value) < requirements["min_length"]:
                key_validation[key_name] = "❌ TOO SHORT"
                print(f"   ❌ {key_name}: Too short ({len(key_value)} chars, need {requirements['min_length']}+)")
                continue
            
            # Pattern validation
            if requirements["pattern"] == "alphanumeric":
                if not key_value.replace("_", "").replace("-", "").isalnum():
                    key_validation[key_name] = "❌ INVALID FORMAT"
                    print(f"   ❌ {key_name}: Invalid format")
                    continue
            
            key_validation[key_name] = "✅ VALID FORMAT"
            print(f"   ✅ {key_name}: Valid format ({len(key_value)} chars)")
        
        self.validation_results["api_key_validation"] = key_validation
    
    async def validate_api_connectivity(self):
        """Test actual API connectivity and authentication."""
        print("\n🌐 API CONNECTIVITY TESTING")
        print("-" * 50)
        
        connectivity_results = {}
        
        # Test Polygon API
        if os.getenv("POLYGON_API_KEY"):
            polygon_result = await self._test_polygon_connectivity()
            connectivity_results["polygon"] = polygon_result
        
        # Test Alpha Vantage API
        if os.getenv("ALPHA_VANTAGE_API_KEY"):
            av_result = await self._test_alpha_vantage_connectivity()
            connectivity_results["alpha_vantage"] = av_result
        
        # Test Benzinga API
        if os.getenv("BENZINGA_API_KEY"):
            benzinga_result = await self._test_benzinga_connectivity()
            connectivity_results["benzinga"] = benzinga_result
        
        self.validation_results["api_connectivity"] = connectivity_results
    
    async def _test_polygon_connectivity(self):
        """Test Polygon API connectivity."""
        try:
            api_key = os.getenv("POLYGON_API_KEY")
            url = f"https://api.polygon.io/v3/reference/tickers/AAPL?apikey={api_key}"
            
            async with aiohttp.ClientSession() as session:
                start_time = time.time()
                async with session.get(url) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        if "results" in data:
                            print(f"   ✅ Polygon API: Connected ({response_time:.2f}s)")
                            return {"status": "✅ CONNECTED", "response_time": response_time}
                        else:
                            print(f"   ❌ Polygon API: Invalid response format")
                            return {"status": "❌ INVALID RESPONSE", "response_time": response_time}
                    elif response.status == 401:
                        print(f"   ❌ Polygon API: Authentication failed")
                        self.critical_failures.append("Polygon API authentication failed")
                        return {"status": "❌ AUTH FAILED", "response_time": response_time}
                    elif response.status == 429:
                        print(f"   ⚠️ Polygon API: Rate limited")
                        return {"status": "⚠️ RATE LIMITED", "response_time": response_time}
                    else:
                        print(f"   ❌ Polygon API: HTTP {response.status}")
                        return {"status": f"❌ HTTP {response.status}", "response_time": response_time}
        
        except Exception as e:
            print(f"   ❌ Polygon API: Connection error - {e}")
            self.critical_failures.append(f"Polygon API connection error: {e}")
            return {"status": "❌ CONNECTION ERROR", "error": str(e)}
    
    async def _test_alpha_vantage_connectivity(self):
        """Test Alpha Vantage API connectivity."""
        try:
            api_key = os.getenv("ALPHA_VANTAGE_API_KEY")
            url = f"https://www.alphavantage.co/query?function=OVERVIEW&symbol=AAPL&apikey={api_key}"
            
            async with aiohttp.ClientSession() as session:
                start_time = time.time()
                async with session.get(url) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        if "Symbol" in data:
                            print(f"   ✅ Alpha Vantage API: Connected ({response_time:.2f}s)")
                            return {"status": "✅ CONNECTED", "response_time": response_time}
                        elif "Error Message" in data:
                            print(f"   ❌ Alpha Vantage API: {data['Error Message']}")
                            return {"status": "❌ API ERROR", "response_time": response_time}
                        elif "Note" in data:
                            print(f"   ⚠️ Alpha Vantage API: Rate limited")
                            return {"status": "⚠️ RATE LIMITED", "response_time": response_time}
                        else:
                            print(f"   ❌ Alpha Vantage API: Unexpected response")
                            return {"status": "❌ UNEXPECTED RESPONSE", "response_time": response_time}
                    else:
                        print(f"   ❌ Alpha Vantage API: HTTP {response.status}")
                        return {"status": f"❌ HTTP {response.status}", "response_time": response_time}
        
        except Exception as e:
            print(f"   ❌ Alpha Vantage API: Connection error - {e}")
            self.critical_failures.append(f"Alpha Vantage API connection error: {e}")
            return {"status": "❌ CONNECTION ERROR", "error": str(e)}
    
    async def _test_benzinga_connectivity(self):
        """Test Benzinga API connectivity."""
        try:
            api_key = os.getenv("BENZINGA_API_KEY")
            url = f"https://api.benzinga.com/api/v2/news?token={api_key}&pagesize=1"
            
            async with aiohttp.ClientSession() as session:
                start_time = time.time()
                async with session.get(url) as response:
                    response_time = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        if isinstance(data, list) or "data" in data:
                            print(f"   ✅ Benzinga API: Connected ({response_time:.2f}s)")
                            return {"status": "✅ CONNECTED", "response_time": response_time}
                        else:
                            print(f"   ❌ Benzinga API: Unexpected response format")
                            return {"status": "❌ INVALID RESPONSE", "response_time": response_time}
                    elif response.status == 401:
                        print(f"   ❌ Benzinga API: Authentication failed")
                        self.critical_failures.append("Benzinga API authentication failed")
                        return {"status": "❌ AUTH FAILED", "response_time": response_time}
                    elif response.status == 429:
                        print(f"   ⚠️ Benzinga API: Rate limited")
                        return {"status": "⚠️ RATE LIMITED", "response_time": response_time}
                    else:
                        print(f"   ❌ Benzinga API: HTTP {response.status}")
                        return {"status": f"❌ HTTP {response.status}", "response_time": response_time}
        
        except Exception as e:
            print(f"   ❌ Benzinga API: Connection error - {e}")
            self.critical_failures.append(f"Benzinga API connection error: {e}")
            return {"status": "❌ CONNECTION ERROR", "error": str(e)}
    
    async def validate_rate_limiting(self):
        """Validate rate limiting implementation."""
        print("\n⏱️ RATE LIMITING VALIDATION")
        print("-" * 50)
        
        # Test rate limiting with multiple rapid requests
        rate_limit_results = {}
        
        if os.getenv("POLYGON_API_KEY"):
            polygon_rate_test = await self._test_rate_limiting_polygon()
            rate_limit_results["polygon"] = polygon_rate_test
        
        self.validation_results["rate_limiting"] = rate_limit_results
    
    async def _test_rate_limiting_polygon(self):
        """Test Polygon API rate limiting."""
        try:
            api_key = os.getenv("POLYGON_API_KEY")
            url = f"https://api.polygon.io/v3/reference/tickers/AAPL?apikey={api_key}"
            
            # Make 3 rapid requests to test rate limiting
            response_times = []
            statuses = []
            
            async with aiohttp.ClientSession() as session:
                for i in range(3):
                    start_time = time.time()
                    try:
                        async with session.get(url) as response:
                            response_time = time.time() - start_time
                            response_times.append(response_time)
                            statuses.append(response.status)
                            
                            if i < 2:  # Small delay between requests
                                await asyncio.sleep(0.1)
                    
                    except Exception as e:
                        print(f"   ⚠️ Request {i+1} failed: {e}")
            
            # Analyze results
            if len(statuses) >= 2:
                if all(status == 200 for status in statuses):
                    print(f"   ✅ Rate limiting: Requests successful (avg: {sum(response_times)/len(response_times):.2f}s)")
                    return {"status": "✅ WORKING", "avg_response_time": sum(response_times)/len(response_times)}
                elif 429 in statuses:
                    print(f"   ✅ Rate limiting: Properly enforced (got 429)")
                    return {"status": "✅ ENFORCED", "rate_limited": True}
                else:
                    print(f"   ⚠️ Rate limiting: Mixed results {statuses}")
                    return {"status": "⚠️ MIXED RESULTS", "statuses": statuses}
            else:
                print(f"   ❌ Rate limiting: Insufficient test data")
                return {"status": "❌ INSUFFICIENT DATA"}
        
        except Exception as e:
            print(f"   ❌ Rate limiting test error: {e}")
            return {"status": "❌ TEST ERROR", "error": str(e)}
    
    async def validate_configuration_system(self):
        """Validate the configuration system."""
        print("\n⚙️ CONFIGURATION SYSTEM VALIDATION")
        print("-" * 50)
        
        config_results = {}
        
        try:
            # Test configuration loading
            config = QuantumEdgeConfig()
            
            # Validate API configuration
            if hasattr(config, 'api'):
                print(f"   ✅ API configuration loaded")
                config_results["api_config"] = "✅ LOADED"
                
                # Check specific API keys
                if hasattr(config.api, 'polygon_api_key') and config.api.polygon_api_key:
                    print(f"   ✅ Polygon API key loaded")
                    config_results["polygon_key"] = "✅ LOADED"
                else:
                    print(f"   ❌ Polygon API key not loaded")
                    config_results["polygon_key"] = "❌ NOT LOADED"
                
                if hasattr(config.api, 'alpha_vantage_api_key') and config.api.alpha_vantage_api_key:
                    print(f"   ✅ Alpha Vantage API key loaded")
                    config_results["alpha_vantage_key"] = "✅ LOADED"
                else:
                    print(f"   ❌ Alpha Vantage API key not loaded")
                    config_results["alpha_vantage_key"] = "❌ NOT LOADED"
            else:
                print(f"   ❌ API configuration not found")
                config_results["api_config"] = "❌ NOT FOUND"
                self.critical_failures.append("API configuration not found")
            
            # Validate system configuration
            if hasattr(config, 'system'):
                print(f"   ✅ System configuration loaded")
                config_results["system_config"] = "✅ LOADED"
            else:
                print(f"   ❌ System configuration not found")
                config_results["system_config"] = "❌ NOT FOUND"
            
        except Exception as e:
            print(f"   ❌ Configuration system error: {e}")
            config_results["error"] = str(e)
            self.critical_failures.append(f"Configuration system error: {e}")
        
        self.validation_results["configuration_system"] = config_results
    
    def generate_validation_report(self):
        """Generate comprehensive validation report."""
        print("\n" + "=" * 70)
        print("📋 COMPREHENSIVE ENVIRONMENT VALIDATION REPORT")
        print("=" * 70)
        
        # Calculate overall scores
        total_checks = 0
        passed_checks = 0
        
        for category, results in self.validation_results.items():
            if isinstance(results, dict):
                for check, status in results.items():
                    if isinstance(status, dict):
                        for subcheck, substatus in status.items():
                            total_checks += 1
                            if "✅" in str(substatus):
                                passed_checks += 1
                    else:
                        total_checks += 1
                        if "✅" in str(status):
                            passed_checks += 1
        
        success_rate = (passed_checks / total_checks * 100) if total_checks > 0 else 0
        
        print(f"\n📊 VALIDATION SUMMARY:")
        print(f"   Total Checks: {total_checks}")
        print(f"   Passed: {passed_checks}")
        print(f"   Failed: {total_checks - passed_checks}")
        print(f"   Success Rate: {success_rate:.1f}%")
        
        # Critical failures
        if self.critical_failures:
            print(f"\n🚨 CRITICAL FAILURES ({len(self.critical_failures)}):")
            for i, failure in enumerate(self.critical_failures, 1):
                print(f"   {i}. {failure}")
        else:
            print(f"\n✅ NO CRITICAL FAILURES DETECTED")
        
        # Overall assessment
        if success_rate >= 90 and not self.critical_failures:
            print(f"\n🎉 ENVIRONMENT STATUS: EXCELLENT")
            print("✅ System ready for production deployment")
        elif success_rate >= 80 and len(self.critical_failures) <= 2:
            print(f"\n⚠️ ENVIRONMENT STATUS: GOOD WITH MINOR ISSUES")
            print("⚠️ Address critical failures before production")
        else:
            print(f"\n❌ ENVIRONMENT STATUS: NEEDS ATTENTION")
            print("❌ Multiple issues must be resolved")
        
        print("\n" + "=" * 70)


async def main():
    """Run comprehensive environment validation."""
    setup_logging(log_level="INFO")
    
    validator = EnvironmentValidator()
    await validator.validate_all()


if __name__ == "__main__":
    asyncio.run(main())
