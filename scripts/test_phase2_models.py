#!/usr/bin/env python3
"""
Phase 2 Model Testing - Quantum-Enhanced Prediction Engine
Tests the complete machine learning and quantum computing pipeline.
"""

import asyncio
import sys
import os
import numpy as np
import pandas as pd
from pathlib import Path
from dotenv import load_dotenv
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from quantum_edge.config.settings import QuantumEdgeConfig
from quantum_edge.config.logging_config import setup_logging
from quantum_edge.data.sources.polygon_client import PolygonClient
from quantum_edge.data.sources.alpha_vantage_client import AlphaVantageClient
from quantum_edge.data.sources.benzinga_client import Benzinga<PERSON>lient
from quantum_edge.data.aggregators.market_data_aggregator import MarketDataAggregator
from quantum_edge.features.feature_pipeline import FeaturePipeline
from quantum_edge.models.ensemble.xgboost_model import XGBoostModel
from quantum_edge.models.ensemble.lightgbm_model import LightGBMModel
from quantum_edge.models.ensemble.ensemble_predictor import EnsemblePredictor
from quantum_edge.models.quantum.quantum_classifier import QuantumClassifier


async def test_phase2_models():
    """Test Phase 2 quantum-enhanced prediction models."""
    print("🚀 PHASE 2: QUANTUM-ENHANCED PREDICTION ENGINE TEST")
    print("=" * 70)
    
    # Setup logging
    setup_logging(log_level="INFO")
    
    try:
        # 1. Initialize Configuration
        print("1️⃣ Initializing Configuration...")
        config = QuantumEdgeConfig()
        print(f"   ✅ Environment: {config.system.environment}")
        
        # 2. Initialize Data Sources
        print("\n2️⃣ Initializing Data Sources...")
        
        # Initialize working data sources
        polygon_client = PolygonClient(api_key=config.api.polygon_api_key)
        alpha_vantage_client = AlphaVantageClient(api_key=config.api.alpha_vantage_api_key)
        benzinga_client = BenzingaClient(api_key=config.api.benzinga_api_key)
        
        aggregator = MarketDataAggregator(
            polygon_client=polygon_client,
            alpha_vantage_client=alpha_vantage_client,
            benzinga_client=benzinga_client
        )
        
        print(f"   ✅ Data aggregator initialized with sources: {aggregator.available_sources}")
        
        # 3. Get Sample Data
        print("\n3️⃣ Fetching Sample Data...")
        
        test_symbols = ["AAPL", "MSFT"]
        
        # Get market data
        market_data = {}
        for symbol in test_symbols:
            try:
                # Get historical data from Polygon
                end_date = datetime.now()
                start_date = end_date - timedelta(days=100)
                
                historical_data = await polygon_client.get_aggregates(
                    symbol=symbol,
                    from_date=start_date.strftime("%Y-%m-%d"),
                    to_date=end_date.strftime("%Y-%m-%d"),
                    timespan="day"
                )

                if len(historical_data) > 50:
                    market_data[symbol] = historical_data
                    print(f"   ✅ {symbol}: {len(historical_data)} days of data")
                else:
                    print(f"   ⚠️ {symbol}: Insufficient data")
                
            except Exception as e:
                print(f"   ❌ {symbol}: Error - {e}")
        
        if not market_data:
            print("   ❌ No market data available for testing")
            return False
        
        # Get comprehensive analysis
        comprehensive_data = await aggregator.get_comprehensive_analysis(
            symbols=list(market_data.keys()),
            include_fundamentals=True,
            include_sentiment=True,
            include_insider_data=False,  # Skip SEC for now
            include_patents=False  # Skip patents for now
        )
        
        print(f"   ✅ Comprehensive analysis completed for {len(comprehensive_data)} symbols")
        
        # 4. Test Feature Engineering
        print("\n4️⃣ Testing Feature Engineering Pipeline...")
        
        feature_pipeline = FeaturePipeline(
            include_technical=True,
            include_quantum=True,
            include_fundamental=True,
            include_sentiment=True,
            quantum_qubits=4,
            feature_selection=True,
            max_features=50
        )
        
        # Create features for each symbol
        feature_data = {}
        for symbol, data in market_data.items():
            try:
                # Get additional data
                fundamental_data = comprehensive_data.get(symbol, {}).get('fundamental_data', {})
                sentiment_data = comprehensive_data.get(symbol, {}).get('sentiment_data', {})
                
                # Create features
                features = await feature_pipeline.create_features(
                    market_data=data,
                    fundamental_data=fundamental_data,
                    sentiment_data=sentiment_data,
                    symbol=symbol
                )
                
                feature_data[symbol] = features
                print(f"   ✅ {symbol}: {features.shape[1]} features created")
                
            except Exception as e:
                print(f"   ❌ {symbol}: Feature creation error - {e}")
        
        if not feature_data:
            print("   ❌ No features created for testing")
            return False
        
        # 5. Test Individual Models
        print("\n5️⃣ Testing Individual Models...")
        
        # Use AAPL data for model testing
        test_symbol = "AAPL"
        if test_symbol not in feature_data:
            test_symbol = list(feature_data.keys())[0]
        
        features = feature_data[test_symbol]
        prices = market_data[test_symbol]["close"]
        
        # Create target (next day return > 0)
        target = (prices.shift(-1) > prices).astype(int)
        
        # Align features and target
        aligned_data = pd.concat([features, target.rename("target")], axis=1).dropna()
        
        if len(aligned_data) < 50:
            print("   ❌ Insufficient aligned data for model training")
            return False
        
        X = aligned_data.drop("target", axis=1)
        y = aligned_data["target"]
        
        # Split data
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
        y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]
        
        print(f"   📊 Training data: {len(X_train)} samples, {X_train.shape[1]} features")
        print(f"   📊 Test data: {len(X_test)} samples")
        
        # Test XGBoost Model
        print("\n   🔸 Testing XGBoost Model...")
        try:
            xgb_model = XGBoostModel(
                feature_columns=X.columns.tolist(),
                model_params={
                    "max_depth": 4,
                    "n_estimators": 100,
                    "learning_rate": 0.1
                }
            )
            
            xgb_results = await xgb_model.train(X_train, y_train, (X_test, y_test))
            print(f"      ✅ XGBoost trained - Train Acc: {xgb_results['train_metrics']['accuracy']:.3f}, "
                  f"Val Acc: {xgb_results['val_metrics']['accuracy']:.3f}")
            
        except Exception as e:
            print(f"      ❌ XGBoost error: {e}")
            xgb_model = None
        
        # Test LightGBM Model
        print("\n   🔸 Testing LightGBM Model...")
        try:
            lgb_model = LightGBMModel(
                feature_columns=X.columns.tolist(),
                model_params={
                    "num_leaves": 31,
                    "n_estimators": 100,
                    "learning_rate": 0.1
                }
            )
            
            lgb_results = await lgb_model.train(X_train, y_train, (X_test, y_test))
            print(f"      ✅ LightGBM trained - Train Acc: {lgb_results['train_metrics']['accuracy']:.3f}, "
                  f"Val Acc: {lgb_results['val_metrics']['accuracy']:.3f}")
            
        except Exception as e:
            print(f"      ❌ LightGBM error: {e}")
            lgb_model = None
        
        # Test Quantum Classifier
        print("\n   🔸 Testing Quantum Classifier...")
        try:
            quantum_model = QuantumClassifier(
                n_qubits=4,
                feature_columns=X.columns.tolist()[:4],  # Limit to 4 features for quantum
                classical_fallback=True
            )
            
            # Use subset of features for quantum model
            X_train_quantum = X_train.iloc[:, :4]
            X_test_quantum = X_test.iloc[:, :4]
            
            quantum_results = await quantum_model.train(X_train_quantum, y_train, (X_test_quantum, y_test))
            
            if quantum_results.get("quantum_training_completed"):
                print(f"      ✅ Quantum model trained - Train Acc: {quantum_results['train_accuracy']:.3f}, "
                      f"Val Acc: {quantum_results['val_accuracy']:.3f}")
            else:
                print(f"      ⚠️ Quantum training failed, using classical fallback")
            
        except Exception as e:
            print(f"      ❌ Quantum model error: {e}")
            quantum_model = None
        
        # 6. Test Ensemble Model
        print("\n6️⃣ Testing Ensemble Model...")
        
        # Collect trained models
        trained_models = []
        if xgb_model and xgb_model.is_trained:
            trained_models.append(xgb_model)
        if lgb_model and lgb_model.is_trained:
            trained_models.append(lgb_model)
        if quantum_model and quantum_model.is_trained:
            trained_models.append(quantum_model)
        
        if len(trained_models) >= 2:
            try:
                ensemble = EnsemblePredictor(
                    models=trained_models,
                    ensemble_method="weighted_voting",
                    feature_columns=X.columns.tolist()
                )
                
                ensemble_results = await ensemble.train(X_train, y_train, (X_test, y_test))
                
                print(f"   ✅ Ensemble trained with {len(trained_models)} models")
                print(f"   📊 Ensemble metrics: {ensemble_results.get('ensemble_metrics', {})}")
                
                # Test predictions
                predictions = await ensemble.predict(X_test, return_individual_predictions=True)
                
                if isinstance(predictions, dict):
                    ensemble_pred = predictions["ensemble_predictions"]
                    individual_pred = predictions["individual_predictions"]
                    
                    ensemble_accuracy = np.mean(ensemble_pred == y_test.values)
                    print(f"   🎯 Ensemble test accuracy: {ensemble_accuracy:.3f}")
                    
                    print(f"   🔍 Individual model predictions: {len(individual_pred)} models")
                
            except Exception as e:
                print(f"   ❌ Ensemble error: {e}")
        else:
            print(f"   ⚠️ Insufficient models for ensemble ({len(trained_models)} available)")
        
        # 7. Performance Summary
        print("\n" + "=" * 70)
        print("📋 PHASE 2 MODEL TESTING SUMMARY")
        print("=" * 70)
        
        # Feature engineering summary
        feature_info = feature_pipeline.get_feature_info()
        print(f"Feature Engineering:")
        print(f"  ✅ Technical Indicators: {feature_info['include_technical']}")
        print(f"  ✅ Quantum Features: {feature_info['include_quantum']}")
        print(f"  ✅ Fundamental Features: {feature_info['include_fundamental']}")
        print(f"  ✅ Sentiment Features: {feature_info['include_sentiment']}")
        print(f"  📊 Total Features Created: {feature_info['total_features']}")
        
        # Model performance summary
        print(f"\nModel Performance:")
        if xgb_model and xgb_model.is_trained:
            print(f"  ✅ XGBoost: Trained successfully")
        else:
            print(f"  ❌ XGBoost: Failed")
        
        if lgb_model and lgb_model.is_trained:
            print(f"  ✅ LightGBM: Trained successfully")
        else:
            print(f"  ❌ LightGBM: Failed")
        
        if quantum_model and quantum_model.is_trained:
            quantum_info = quantum_model.get_model_info()
            print(f"  ✅ Quantum Classifier: {quantum_info['quantum_available']} (quantum available)")
        else:
            print(f"  ❌ Quantum Classifier: Failed")
        
        # Overall assessment
        successful_models = sum([
            1 if xgb_model and xgb_model.is_trained else 0,
            1 if lgb_model and lgb_model.is_trained else 0,
            1 if quantum_model and quantum_model.is_trained else 0
        ])
        
        success_rate = (successful_models / 3) * 100
        
        print(f"\nOverall Success Rate: {success_rate:.1f}% ({successful_models}/3 models)")
        
        if success_rate >= 66:
            print("\n🎉 PHASE 2 TESTING SUCCESSFUL!")
            print("✅ Quantum-enhanced prediction engine operational")
            print("✅ Multi-model ensemble capabilities available")
            print("✅ Advanced feature engineering working")
            print("✅ Ready for Phase 3 implementation")
        else:
            print("\n⚠️ PHASE 2 PARTIAL SUCCESS")
            print("✅ Core ML functionality operational")
            print("⚠️ Some advanced features may need optimization")
        
        await aggregator.close()
        return success_rate >= 50
        
    except Exception as e:
        print(f"\n❌ PHASE 2 TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_phase2_models())
    sys.exit(0 if success else 1)
