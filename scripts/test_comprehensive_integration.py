#!/usr/bin/env python3
"""
Comprehensive Integration Test - All APIs
Tests the complete multi-source data infrastructure with all available APIs.
"""

import asyncio
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from quantum_edge.config.settings import QuantumEdgeConfig
from quantum_edge.config.logging_config import setup_logging
from quantum_edge.data.sources.polygon_client import PolygonClient
from quantum_edge.data.sources.alpaca_client import AlpacaClient
from quantum_edge.data.sources.alpha_vantage_client import AlphaVantageClient
from quantum_edge.data.sources.benzinga_client import BenzingaClient
from quantum_edge.data.sources.sec_client import SECClient
from quantum_edge.data.sources.patents_client import PatentsClient
from quantum_edge.data.aggregators.market_data_aggregator import MarketDataAggregator


async def test_all_integrations():
    """Test all API integrations comprehensively."""
    print("🚀 QUANTUMEDGE COMPREHENSIVE INTEGRATION TEST")
    print("=" * 70)
    
    # Setup logging
    setup_logging(log_level="INFO")
    
    try:
        # 1. Configuration Test
        print("1️⃣ Testing Configuration...")
        config = QuantumEdgeConfig()
        print(f"   ✅ Environment: {config.system.environment}")
        
        # 2. Initialize All Clients
        print("\n2️⃣ Initializing All Data Sources...")
        
        clients = {}
        
        # Polygon (working)
        if config.api.polygon_api_key:
            clients['polygon'] = PolygonClient(api_key=config.api.polygon_api_key)
            print("   ✅ Polygon client initialized")
        
        # Alpaca (needs real key)
        alpaca_secret = os.getenv("ALPACA_SECRET_KEY")
        if alpaca_secret:
            alpaca_key = os.getenv("ALPACA_API_KEY") or "dummy_key"
            clients['alpaca'] = AlpacaClient(api_key=alpaca_key, secret_key=alpaca_secret)
            print("   ✅ Alpaca client initialized")
        
        # Alpha Vantage
        if config.api.alpha_vantage_api_key:
            clients['alpha_vantage'] = AlphaVantageClient(api_key=config.api.alpha_vantage_api_key)
            print("   ✅ Alpha Vantage client initialized")
        
        # Benzinga
        if config.api.benzinga_api_key:
            clients['benzinga'] = BenzingaClient(api_key=config.api.benzinga_api_key)
            print("   ✅ Benzinga client initialized")
        
        # SEC API
        sec_key = os.getenv("SEC_API_KEY")
        if sec_key:
            clients['sec'] = SECClient(api_key=sec_key)
            print("   ✅ SEC client initialized")
        
        # Patents
        google_patents_key = os.getenv("GOOGLE_PATENTS_API_KEY")
        uspto_key = os.getenv("USPTO_API_KEY")
        if google_patents_key or uspto_key:
            clients['patents'] = PatentsClient(
                google_api_key=google_patents_key,
                uspto_api_key=uspto_key
            )
            print("   ✅ Patents client initialized")
        
        print(f"   📊 Total clients initialized: {len(clients)}")
        
        # 3. Test Individual Connections
        print("\n3️⃣ Testing Individual Connections...")
        
        connection_results = {}
        for name, client in clients.items():
            try:
                connected = await client.test_connection()
                connection_results[name] = connected
                status = "✅" if connected else "❌"
                print(f"   {status} {name.title()}: {'Connected' if connected else 'Failed'}")
            except Exception as e:
                connection_results[name] = False
                print(f"   ❌ {name.title()}: Error - {e}")
        
        # 4. Test Comprehensive Aggregator
        print("\n4️⃣ Testing Comprehensive Market Data Aggregator...")
        
        aggregator = MarketDataAggregator(
            polygon_client=clients.get('polygon'),
            alpaca_client=clients.get('alpaca'),
            alpha_vantage_client=clients.get('alpha_vantage'),
            benzinga_client=clients.get('benzinga'),
            sec_client=clients.get('sec'),
            patents_client=clients.get('patents')
        )
        
        print(f"   ✅ Aggregator initialized with sources: {aggregator.available_sources}")
        
        # Test with sample symbols
        test_symbols = ["AAPL", "MSFT", "GOOGL"]
        
        # 5. Test Comprehensive Analysis (limited to avoid rate limits)
        print("\n5️⃣ Testing Comprehensive Analysis...")
        
        try:
            # Test only with available and working clients
            comprehensive_data = await aggregator.get_comprehensive_analysis(
                symbols=test_symbols[:2],  # Limit to 2 symbols to avoid rate limits
                include_fundamentals=bool(clients.get('alpha_vantage')),
                include_sentiment=bool(clients.get('benzinga')),
                include_insider_data=bool(clients.get('sec')),
                include_patents=bool(clients.get('patents'))
            )
            
            print(f"   ✅ Comprehensive analysis completed for {len(comprehensive_data)} symbols")
            
            # Show sample results
            for symbol, data in comprehensive_data.items():
                sources = data.get('data_sources', [])
                print(f"      📈 {symbol}: {len(sources)} data sources - {', '.join(sources)}")
                
                # Show fundamental data if available
                fundamental = data.get('fundamental_data', {})
                if fundamental.get('market_cap'):
                    print(f"         💰 Market Cap: ${fundamental['market_cap']:,}")
                
                # Show sentiment if available
                sentiment = data.get('sentiment_data', {})
                if sentiment.get('sentiment_score') is not None:
                    score = sentiment['sentiment_score']
                    print(f"         📊 Sentiment Score: {score:.2f}")
                
                # Show insider data if available
                insider = data.get('insider_data', {})
                if insider.get('total_transactions'):
                    print(f"         👥 Insider Transactions: {insider['total_transactions']}")
                
                # Show patent data if available
                patents = data.get('patent_data', {})
                if patents.get('patent_count'):
                    print(f"         🔬 Recent Patents: {patents['patent_count']}")
        
        except Exception as e:
            print(f"   ⚠️ Comprehensive analysis error: {e}")
        
        # 6. Test Innovation Analysis (if patents available)
        if clients.get('patents'):
            print("\n6️⃣ Testing Innovation Analysis...")
            try:
                innovation_data = await aggregator.get_innovation_analysis(
                    sectors={
                        "AI/ML": ["artificial intelligence", "machine learning"],
                        "Quantum": ["quantum computing"]
                    },
                    days_back=180
                )
                
                print(f"   ✅ Innovation analysis completed for {len(innovation_data)} sectors")
                
                for sector, data in innovation_data.items():
                    total_patents = data.get('total_patents', 0)
                    innovation_score = data.get('innovation_score', 0)
                    print(f"      🔬 {sector}: {total_patents} patents, score: {innovation_score:.1f}")
            
            except Exception as e:
                print(f"   ⚠️ Innovation analysis error: {e}")
        
        # 7. Performance Summary
        print("\n" + "=" * 70)
        print("📋 COMPREHENSIVE INTEGRATION SUMMARY")
        print("=" * 70)
        
        # Connection summary
        connected_count = sum(1 for connected in connection_results.values() if connected)
        total_clients = len(clients)
        
        print(f"API Connections: {connected_count}/{total_clients}")
        for name, connected in connection_results.items():
            status = "✅ CONNECTED" if connected else "❌ FAILED"
            print(f"  {name.title():15} | {status}")
        
        # Data sources summary
        print(f"\nData Sources Available: {len(aggregator.available_sources)}")
        for source in aggregator.available_sources:
            print(f"  ✅ {source.title()}")
        
        # Overall assessment
        success_rate = (connected_count / total_clients) * 100 if total_clients > 0 else 0
        
        print(f"\nOverall Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("\n🎉 COMPREHENSIVE INTEGRATION SUCCESSFUL!")
            print("✅ Multi-source data infrastructure operational")
            print("✅ Advanced analytics capabilities available")
            print("✅ Ready for production deployment")
        elif success_rate >= 60:
            print("\n⚠️ PARTIAL INTEGRATION SUCCESS")
            print("✅ Core functionality operational")
            print("⚠️ Some advanced features may be limited")
        else:
            print("\n❌ INTEGRATION NEEDS IMPROVEMENT")
            print("❌ Multiple API connections failed")
        
        await aggregator.close()
        return success_rate >= 60
        
    except Exception as e:
        print(f"\n❌ COMPREHENSIVE TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(test_all_integrations())
    sys.exit(0 if success else 1)
