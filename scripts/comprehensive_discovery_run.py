#!/usr/bin/env python3
"""
Comprehensive Microcap Discovery Run
Enhanced discovery system with expanded universe and lowered thresholds.
"""

import asyncio
import sys
import time
from pathlib import Path
from dotenv import load_dotenv
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from quantum_edge.config.settings import QuantumEdgeConfig
from quantum_edge.config.logging_config import setup_logging
from quantum_edge.data.sources.polygon_client import PolygonClient
from quantum_edge.data.sources.alpha_vantage_client import AlphaVantageClient
from quantum_edge.data.sources.benzinga_client import BenzingaClient
from quantum_edge.discovery.catalyst_detector import CatalystDetector
from quantum_edge.discovery.growth_scorer import GrowthScorer
from quantum_edge.risk.risk_manager import RiskManager
from quantum_edge.risk.position_sizer import PositionSizer


class ComprehensiveDiscoveryRun:
    """Enhanced discovery system for comprehensive microcap analysis."""
    
    def __init__(self):
        """Initialize comprehensive discovery system."""
        self.config = QuantumEdgeConfig()
        self.discovery_results = {}
        self.validated_opportunities = []
        
        print("🎯 COMPREHENSIVE MICROCAP DISCOVERY RUN")
        print("=" * 70)
        print(f"Discovery started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
    
    async def execute_comprehensive_discovery(self):
        """Execute comprehensive discovery with expanded universe."""
        try:
            # 1. Build Expanded Universe
            await self.build_expanded_universe()
            
            # 2. Enhanced Discovery Analysis
            await self.enhanced_discovery_analysis()
            
            # 3. Deep Validation Process
            await self.deep_validation_process()
            
            # 4. Generate Comprehensive Report
            self.generate_comprehensive_report()
            
        except Exception as e:
            print(f"❌ Discovery error: {e}")
            import traceback
            traceback.print_exc()
    
    async def build_expanded_universe(self):
        """Build expanded microcap universe across all target sectors."""
        print("\n🌌 BUILDING EXPANDED MICROCAP UNIVERSE")
        print("-" * 50)
        
        # Expanded universe across all target sectors
        expanded_universe = {
            # Biotech microcaps
            "SAVA": {"sector": "biotech", "focus": "Alzheimer's treatment", "catalyst_potential": "high"},
            "AVXL": {"sector": "biotech", "focus": "Neurological disorders", "catalyst_potential": "high"},
            "CTMX": {"sector": "biotech", "focus": "Cancer immunotherapy", "catalyst_potential": "medium"},
            "ADMA": {"sector": "biotech", "focus": "Plasma-derived biologics", "catalyst_potential": "medium"},
            "ATOS": {"sector": "biotech", "focus": "Oncology treatments", "catalyst_potential": "high"},
            "CPRX": {"sector": "biotech", "focus": "CNS disorders", "catalyst_potential": "medium"},
            
            # AI/ML microcaps
            "BBAI": {"sector": "ai_ml", "focus": "Decision intelligence", "catalyst_potential": "medium"},
            "SOUN": {"sector": "ai_ml", "focus": "Voice AI technology", "catalyst_potential": "high"},
            "AITX": {"sector": "ai_ml", "focus": "AI security solutions", "catalyst_potential": "medium"},
            "GFAI": {"sector": "ai_ml", "focus": "AI-powered platforms", "catalyst_potential": "medium"},
            "VERI": {"sector": "ai_ml", "focus": "Video analytics AI", "catalyst_potential": "medium"},
            
            # Clean Energy microcaps
            "PLUG": {"sector": "clean_energy", "focus": "Hydrogen fuel cells", "catalyst_potential": "high"},
            "FCEL": {"sector": "clean_energy", "focus": "Fuel cell power", "catalyst_potential": "medium"},
            "BLDP": {"sector": "clean_energy", "focus": "Clean energy solutions", "catalyst_potential": "medium"},
            "HYLN": {"sector": "clean_energy", "focus": "Hybrid powertrains", "catalyst_potential": "medium"},
            "CLNE": {"sector": "clean_energy", "focus": "Natural gas solutions", "catalyst_potential": "medium"},
            
            # Space Tech microcaps
            "RKLB": {"sector": "space_tech", "focus": "Launch services", "catalyst_potential": "high"},
            "ASTR": {"sector": "space_tech", "focus": "Rocket technology", "catalyst_potential": "high"},
            "SPCE": {"sector": "space_tech", "focus": "Space tourism", "catalyst_potential": "medium"},
            "ASTS": {"sector": "space_tech", "focus": "Space-based cellular", "catalyst_potential": "high"},
            
            # Cybersecurity microcaps
            "CYBR": {"sector": "cybersecurity", "focus": "Privileged access security", "catalyst_potential": "medium"},
            "TENB": {"sector": "cybersecurity", "focus": "Vulnerability management", "catalyst_potential": "medium"},
            "VRNS": {"sector": "cybersecurity", "focus": "Security analytics", "catalyst_potential": "medium"},
            "QLYS": {"sector": "cybersecurity", "focus": "Cloud security", "catalyst_potential": "medium"},
            
            # FinTech microcaps
            "UPST": {"sector": "fintech", "focus": "AI lending platform", "catalyst_potential": "medium"},
            "AFRM": {"sector": "fintech", "focus": "Buy now pay later", "catalyst_potential": "medium"},
            "SOFI": {"sector": "fintech", "focus": "Digital banking", "catalyst_potential": "medium"},
            "LC": {"sector": "fintech", "focus": "Online lending", "catalyst_potential": "low"},
        }
        
        print(f"   📊 Universe Size: {len(expanded_universe)} companies")
        print(f"   🎯 Target Sectors: 6 sectors")
        
        # Validate with live data (subset to avoid rate limits)
        validated_universe = {}
        
        if self.config.api.polygon_api_key:
            polygon_client = PolygonClient(api_key=self.config.api.polygon_api_key)
            
            # Validate top candidates from each sector
            priority_symbols = [
                "SAVA", "AVXL", "ATOS",  # Biotech
                "BBAI", "SOUN", "AITX",  # AI/ML
                "PLUG", "FCEL", "HYLN",  # Clean Energy
                "RKLB", "ASTR", "ASTS",  # Space Tech
                "TENB", "VRNS",          # Cybersecurity
                "UPST", "SOFI"           # FinTech
            ]
            
            for symbol in priority_symbols:
                try:
                    ticker_data = await polygon_client.get_ticker_details(symbol)
                    
                    if ticker_data and "market_cap" in ticker_data:
                        market_cap = ticker_data["market_cap"]
                        name = ticker_data.get("name", symbol)
                        
                        # Validate microcap range (expanded to include some larger caps for diversity)
                        if 25_000_000 <= market_cap <= 5_000_000_000:  # $25M-$5B range
                            company_data = expanded_universe[symbol].copy()
                            company_data.update({
                                "symbol": symbol,
                                "name": name,
                                "market_cap": market_cap,
                                "validated": True
                            })
                            validated_universe[symbol] = company_data
                            
                            print(f"   ✅ {symbol}: {name} (${market_cap:,.0f})")
                        else:
                            print(f"   ⚠️ {symbol}: Outside range (${market_cap:,.0f})")
                    
                    # Rate limiting
                    await asyncio.sleep(0.3)
                    
                except Exception as e:
                    print(f"   ❌ {symbol}: Error - {e}")
            
            await polygon_client.close()
        else:
            # Use demo data if no API
            for symbol, data in list(expanded_universe.items())[:15]:
                company_data = data.copy()
                company_data.update({
                    "symbol": symbol,
                    "name": f"{symbol} Corp",
                    "market_cap": 500_000_000,  # Demo
                    "validated": False
                })
                validated_universe[symbol] = company_data
        
        self.discovery_results["universe"] = validated_universe
        print(f"\n   📈 Validated Universe: {len(validated_universe)} companies")
        
        # Sector breakdown
        sector_counts = {}
        for company in validated_universe.values():
            sector = company["sector"]
            sector_counts[sector] = sector_counts.get(sector, 0) + 1
        
        print(f"   🎯 Sector Distribution:")
        for sector, count in sector_counts.items():
            print(f"     {sector.replace('_', ' ').title()}: {count}")
    
    async def enhanced_discovery_analysis(self):
        """Enhanced discovery analysis with lowered thresholds."""
        print("\n🔍 ENHANCED DISCOVERY ANALYSIS")
        print("-" * 50)
        
        universe = self.discovery_results["universe"]
        discovery_threshold = 40.0  # Lowered from 60 to 40
        
        print(f"   🎯 Discovery Threshold: {discovery_threshold} (lowered for broader discovery)")
        
        # Initialize analysis components
        catalyst_detector = CatalystDetector()
        growth_scorer = GrowthScorer()
        risk_manager = RiskManager()
        position_sizer = PositionSizer(portfolio_value=100_000)
        
        analyzed_opportunities = []
        
        for symbol, company_data in universe.items():
            try:
                print(f"\n   🔍 Analyzing {symbol} ({company_data['name']})...")
                
                # Enhanced catalyst detection
                catalysts = await catalyst_detector.detect_catalysts([symbol], 60, 30.0)  # Extended lookback, lower threshold
                
                # Enhanced growth scoring with sector-specific adjustments
                scoring_data = {
                    "symbol": symbol,
                    "market_cap": company_data["market_cap"],
                    "sector_category": company_data["sector"],
                    "catalyst_potential": company_data.get("catalyst_potential", "medium")
                }
                
                growth_score = await growth_scorer.score_growth_potential(
                    symbol, scoring_data, catalysts, None, None, None
                )
                
                # Apply sector-specific boost for high-potential sectors
                sector_boost = self._get_sector_boost(company_data["sector"])
                adjusted_score = min(100, growth_score.total_score + sector_boost)
                
                # Enhanced risk assessment
                risk_data = {
                    "symbol": symbol,
                    "market_cap": company_data["market_cap"],
                    "sector_category": company_data["sector"],
                    "avg_volume": 1_000_000,  # Estimated
                    "catalyst_potential": company_data.get("catalyst_potential", "medium")
                }
                
                risk_assessment = await risk_manager.assess_risk(symbol, risk_data, None, None)
                
                # Position sizing
                position_size = await position_sizer.calculate_position_size(
                    symbol, 10.0, adjusted_score, risk_assessment, None, None
                )
                
                # Check if meets discovery threshold
                if adjusted_score >= discovery_threshold:
                    opportunity = {
                        "symbol": symbol,
                        "name": company_data["name"],
                        "sector": company_data["sector"],
                        "focus": company_data["focus"],
                        "market_cap": company_data["market_cap"],
                        "growth_score": adjusted_score,
                        "original_score": growth_score.total_score,
                        "sector_boost": sector_boost,
                        "catalyst_score": growth_score.catalyst_score,
                        "technical_score": growth_score.technical_score,
                        "fundamental_score": growth_score.fundamental_score,
                        "risk_score": risk_assessment.risk_score,
                        "risk_level": risk_assessment.overall_risk_level.value,
                        "max_position": risk_assessment.max_position_size_pct,
                        "recommended_size": position_size.recommended_size_pct,
                        "kelly_fraction": position_size.kelly_fraction,
                        "catalysts": catalysts,
                        "catalyst_count": len(catalysts),
                        "catalyst_potential": company_data.get("catalyst_potential", "medium"),
                        "validated": company_data.get("validated", False)
                    }
                    
                    analyzed_opportunities.append(opportunity)
                    
                    print(f"     ✅ DISCOVERED: Score {adjusted_score:.1f} (threshold: {discovery_threshold})")
                    print(f"        Original: {growth_score.total_score:.1f} + Sector Boost: {sector_boost}")
                    print(f"        Risk: {risk_assessment.overall_risk_level.value}")
                    print(f"        Position: {position_size.recommended_size_pct:.1%}")
                else:
                    print(f"     ❌ Below threshold: {adjusted_score:.1f} < {discovery_threshold}")
                
                # Rate limiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f"     ❌ Analysis error for {symbol}: {e}")
        
        # Sort by adjusted growth score
        analyzed_opportunities.sort(key=lambda x: x["growth_score"], reverse=True)
        
        self.discovery_results["opportunities"] = analyzed_opportunities
        print(f"\n   🎯 Discovered Opportunities: {len(analyzed_opportunities)}")
        print(f"   📊 Discovery Rate: {len(analyzed_opportunities)}/{len(universe)} ({len(analyzed_opportunities)/len(universe)*100:.1f}%)")
    
    def _get_sector_boost(self, sector):
        """Get sector-specific boost for high-potential sectors."""
        sector_boosts = {
            "biotech": 15,        # High catalyst potential
            "ai_ml": 12,          # Strong growth trends
            "space_tech": 10,     # Emerging sector
            "clean_energy": 8,    # Policy tailwinds
            "cybersecurity": 6,   # Steady demand
            "fintech": 5          # Mature but growing
        }
        return sector_boosts.get(sector, 0)
    
    async def deep_validation_process(self):
        """Deep validation process for discovered opportunities."""
        print("\n🔬 DEEP VALIDATION PROCESS")
        print("-" * 50)
        
        opportunities = self.discovery_results["opportunities"]
        
        # Take top 10 for deep validation
        top_opportunities = opportunities[:10]
        
        validated_opportunities = []
        
        for i, opp in enumerate(top_opportunities, 1):
            print(f"\n   {i}. VALIDATING {opp['symbol']} - {opp['name']}")
            print(f"      Growth Score: {opp['growth_score']:.1f}")
            
            # Market cap validation
            market_cap = opp["market_cap"]
            if 25_000_000 <= market_cap <= 5_000_000_000:
                print(f"      ✅ Market Cap: ${market_cap:,.0f} (valid range)")
            else:
                print(f"      ⚠️ Market Cap: ${market_cap:,.0f} (outside preferred range)")
            
            # Liquidity assessment
            liquidity_score = self._assess_liquidity(opp)
            print(f"      📊 Liquidity Score: {liquidity_score}/100")
            
            # Catalyst analysis
            catalyst_analysis = self._analyze_catalysts(opp)
            print(f"      🚀 Catalyst Analysis: {catalyst_analysis}")
            
            # Sector positioning
            sector_position = self._assess_sector_positioning(opp)
            print(f"      🎯 Sector Position: {sector_position}")
            
            # Investment thesis
            investment_thesis = self._generate_investment_thesis(opp)
            
            # Enhanced opportunity data
            enhanced_opp = opp.copy()
            enhanced_opp.update({
                "liquidity_score": liquidity_score,
                "catalyst_analysis": catalyst_analysis,
                "sector_positioning": sector_position,
                "investment_thesis": investment_thesis,
                "validation_score": (liquidity_score + opp["growth_score"]) / 2
            })
            
            validated_opportunities.append(enhanced_opp)
            print(f"      ✅ VALIDATED: Overall score {enhanced_opp['validation_score']:.1f}")
        
        # Sort by validation score
        validated_opportunities.sort(key=lambda x: x["validation_score"], reverse=True)
        
        self.validated_opportunities = validated_opportunities
        print(f"\n   🏆 Top Validated Opportunities: {len(validated_opportunities)}")
    
    def _assess_liquidity(self, opportunity):
        """Assess liquidity based on market cap and sector."""
        market_cap = opportunity["market_cap"]
        sector = opportunity["sector"]
        
        # Base score from market cap
        if market_cap > 1_000_000_000:
            base_score = 80
        elif market_cap > 500_000_000:
            base_score = 70
        elif market_cap > 200_000_000:
            base_score = 60
        elif market_cap > 100_000_000:
            base_score = 50
        else:
            base_score = 40
        
        # Sector adjustment
        sector_adjustments = {
            "biotech": -5,        # Often lower liquidity
            "ai_ml": 5,           # Higher interest
            "space_tech": 0,      # Variable
            "clean_energy": 0,    # Variable
            "cybersecurity": 5,   # Steady interest
            "fintech": 5          # Good liquidity
        }
        
        adjustment = sector_adjustments.get(sector, 0)
        return min(100, max(20, base_score + adjustment))
    
    def _analyze_catalysts(self, opportunity):
        """Analyze potential catalysts for the opportunity."""
        sector = opportunity["sector"]
        catalyst_potential = opportunity.get("catalyst_potential", "medium")
        catalyst_count = opportunity.get("catalyst_count", 0)
        
        sector_catalysts = {
            "biotech": ["FDA approvals", "Clinical trial results", "Partnership deals", "Patent approvals"],
            "ai_ml": ["Product launches", "Partnership announcements", "Revenue growth", "Market expansion"],
            "space_tech": ["Launch successes", "Contract wins", "Technology milestones", "Regulatory approvals"],
            "clean_energy": ["Policy changes", "Infrastructure deals", "Technology breakthroughs", "Partnerships"],
            "cybersecurity": ["Product releases", "Enterprise deals", "Acquisition rumors", "Market expansion"],
            "fintech": ["Regulatory approvals", "Partnership deals", "User growth", "Product launches"]
        }
        
        potential_catalysts = sector_catalysts.get(sector, ["General business catalysts"])
        
        if catalyst_potential == "high":
            return f"High potential: {', '.join(potential_catalysts[:2])}"
        elif catalyst_potential == "medium":
            return f"Medium potential: {', '.join(potential_catalysts[:1])}"
        else:
            return f"Low potential: General business developments"
    
    def _assess_sector_positioning(self, opportunity):
        """Assess competitive positioning within sector."""
        sector = opportunity["sector"]
        market_cap = opportunity["market_cap"]
        
        sector_characteristics = {
            "biotech": "Innovation-driven, high-risk/high-reward",
            "ai_ml": "Rapidly growing, competitive landscape",
            "space_tech": "Emerging sector, high barriers to entry",
            "clean_energy": "Policy-driven, infrastructure dependent",
            "cybersecurity": "Essential services, steady demand",
            "fintech": "Mature sector, regulatory considerations"
        }
        
        if market_cap > 1_000_000_000:
            size_position = "Large microcap, established player"
        elif market_cap > 500_000_000:
            size_position = "Mid microcap, growing presence"
        else:
            size_position = "Small microcap, early stage"
        
        return f"{size_position} in {sector_characteristics.get(sector, 'developing sector')}"
    
    def _generate_investment_thesis(self, opportunity):
        """Generate investment thesis for the opportunity."""
        symbol = opportunity["symbol"]
        sector = opportunity["sector"]
        focus = opportunity["focus"]
        growth_score = opportunity["growth_score"]
        risk_level = opportunity["risk_level"]
        
        thesis_templates = {
            "biotech": f"{symbol} is positioned in {focus} with {growth_score:.0f}/100 growth potential. Biotech offers high upside from clinical successes but carries {risk_level} risk from regulatory uncertainties.",
            "ai_ml": f"{symbol} operates in {focus} with {growth_score:.0f}/100 growth score. AI/ML sector benefits from strong adoption trends, though faces {risk_level} risk from competition.",
            "space_tech": f"{symbol} focuses on {focus} with {growth_score:.0f}/100 potential. Space technology is an emerging high-growth sector with {risk_level} execution risk.",
            "clean_energy": f"{symbol} specializes in {focus} scoring {growth_score:.0f}/100. Clean energy benefits from policy tailwinds but has {risk_level} risk from market volatility.",
            "cybersecurity": f"{symbol} provides {focus} with {growth_score:.0f}/100 growth score. Cybersecurity offers steady demand growth with {risk_level} competitive risk.",
            "fintech": f"{symbol} operates in {focus} with {growth_score:.0f}/100 potential. FinTech benefits from digital transformation but faces {risk_level} regulatory risk."
        }
        
        return thesis_templates.get(sector, f"{symbol} operates in {focus} with {growth_score:.0f}/100 growth potential and {risk_level} risk profile.")
    
    def generate_comprehensive_report(self):
        """Generate comprehensive discovery report."""
        print("\n" + "=" * 70)
        print("🏆 COMPREHENSIVE DISCOVERY REPORT")
        print("=" * 70)
        
        opportunities = self.validated_opportunities
        
        if not opportunities:
            print("\n❌ No opportunities discovered. Consider lowering thresholds further.")
            return
        
        print(f"\n📊 DISCOVERY SUMMARY:")
        print(f"   Total Opportunities: {len(opportunities)}")
        print(f"   Average Growth Score: {sum(o['growth_score'] for o in opportunities) / len(opportunities):.1f}")
        print(f"   Sectors Represented: {len(set(o['sector'] for o in opportunities))}")
        
        print(f"\n🏆 TOP {min(10, len(opportunities))} VALIDATED OPPORTUNITIES:")
        print("=" * 70)
        
        for i, opp in enumerate(opportunities[:10], 1):
            print(f"\n{i}. {opp['symbol']} - {opp['name']}")
            print(f"   Sector: {opp['sector'].replace('_', ' ').title()}")
            print(f"   Focus: {opp['focus']}")
            print(f"   Market Cap: ${opp['market_cap']:,.0f}")
            print(f"   Growth Score: {opp['growth_score']:.1f}/100 (Original: {opp['original_score']:.1f} + Boost: {opp['sector_boost']})")
            print(f"   Score Breakdown:")
            print(f"     • Catalyst Score: {opp['catalyst_score']:.1f}")
            print(f"     • Technical Score: {opp['technical_score']:.1f}")
            print(f"     • Fundamental Score: {opp['fundamental_score']:.1f}")
            print(f"   Risk Assessment:")
            print(f"     • Risk Level: {opp['risk_level'].title()}")
            print(f"     • Risk Score: {opp['risk_score']:.1f}/100")
            print(f"     • Max Position: {opp['max_position']:.1%}")
            print(f"   Position Sizing:")
            print(f"     • Recommended Size: {opp['recommended_size']:.1%}")
            print(f"     • Kelly Fraction: {opp['kelly_fraction']:.3f}")
            print(f"   Catalyst Analysis: {opp['catalyst_analysis']}")
            print(f"   Sector Positioning: {opp['sector_positioning']}")
            print(f"   Liquidity Score: {opp['liquidity_score']}/100")
            print(f"   Investment Thesis: {opp['investment_thesis']}")
            print(f"   Validation Score: {opp['validation_score']:.1f}/100")
            
            if opp['validated']:
                print(f"   ✅ LIVE DATA VALIDATED")
            else:
                print(f"   ⚠️ Demo data (API validation needed)")
        
        # Sector analysis
        print(f"\n📈 SECTOR ANALYSIS:")
        sector_breakdown = {}
        for opp in opportunities:
            sector = opp['sector']
            if sector not in sector_breakdown:
                sector_breakdown[sector] = []
            sector_breakdown[sector].append(opp)
        
        for sector, sector_opps in sector_breakdown.items():
            avg_score = sum(o['growth_score'] for o in sector_opps) / len(sector_opps)
            print(f"   {sector.replace('_', ' ').title()}: {len(sector_opps)} opportunities, avg score {avg_score:.1f}")
        
        # Risk distribution
        print(f"\n🛡️ RISK DISTRIBUTION:")
        risk_levels = {}
        for opp in opportunities:
            risk = opp['risk_level']
            risk_levels[risk] = risk_levels.get(risk, 0) + 1
        
        for risk, count in risk_levels.items():
            print(f"   {risk.title()} Risk: {count} opportunities")
        
        # Investment recommendations
        print(f"\n💡 INVESTMENT RECOMMENDATIONS:")
        print(f"   🎯 Top 3 Immediate Opportunities:")
        for i, opp in enumerate(opportunities[:3], 1):
            print(f"     {i}. {opp['symbol']}: {opp['recommended_size']:.1%} position, {opp['growth_score']:.1f} score")
        
        print(f"\n   📊 Portfolio Allocation Suggestion:")
        total_allocation = sum(opp['recommended_size'] for opp in opportunities[:5])
        print(f"     Total Top 5 Allocation: {total_allocation:.1%}")
        print(f"     Diversification: {len(set(o['sector'] for o in opportunities[:5]))} sectors")
        
        print(f"\n🚀 NEXT STEPS:")
        print(f"   1. Conduct detailed due diligence on top 3 opportunities")
        print(f"   2. Monitor catalyst timelines and upcoming events")
        print(f"   3. Start with small positions (1-2%) for validation")
        print(f"   4. Scale positions based on performance and conviction")
        print(f"   5. Maintain sector diversification across portfolio")
        
        print("\n" + "=" * 70)


async def main():
    """Run comprehensive discovery."""
    setup_logging(log_level="INFO")
    
    discovery = ComprehensiveDiscoveryRun()
    await discovery.execute_comprehensive_discovery()


if __name__ == "__main__":
    asyncio.run(main())
