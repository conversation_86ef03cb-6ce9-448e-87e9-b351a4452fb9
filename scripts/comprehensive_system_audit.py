#!/usr/bin/env python3
"""
Comprehensive System Audit & Validation
Elite technical audit of the QuantumEdge microcap discovery system.
"""

import os
import sys
import ast
import time
import asyncio
import inspect
from pathlib import Path
from datetime import datetime
import importlib.util

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


class ComprehensiveSystemAudit:
    """Elite system audit with zero tolerance for issues."""
    
    def __init__(self):
        """Initialize comprehensive audit."""
        self.audit_results = {}
        self.code_issues = []
        self.performance_metrics = {}
        self.security_findings = []
        
        print("🔬 COMPREHENSIVE SYSTEM AUDIT & VALIDATION")
        print("=" * 70)
        print(f"Audit started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
    
    def execute_comprehensive_audit(self):
        """Execute comprehensive system audit."""
        try:
            # 1. Codebase Structure Analysis
            self.analyze_codebase_structure()
            
            # 2. Code Quality Assessment
            self.assess_code_quality()
            
            # 3. Architecture Pattern Validation
            self.validate_architecture_patterns()
            
            # 4. Security Vulnerability Scan
            self.scan_security_vulnerabilities()
            
            # 5. Performance Analysis
            self.analyze_performance_characteristics()
            
            # 6. Generate Audit Report
            self.generate_audit_report()
            
        except Exception as e:
            print(f"❌ CRITICAL AUDIT FAILURE: {e}")
            import traceback
            traceback.print_exc()
    
    def analyze_codebase_structure(self):
        """Analyze complete codebase structure."""
        print("\n📁 CODEBASE STRUCTURE ANALYSIS")
        print("-" * 50)
        
        project_root = Path(__file__).parent.parent
        structure_analysis = {
            "total_files": 0,
            "python_files": 0,
            "config_files": 0,
            "documentation_files": 0,
            "test_files": 0,
            "directories": {},
            "file_sizes": {},
            "complexity_metrics": {}
        }
        
        # Analyze directory structure
        key_directories = [
            "src/quantum_edge",
            "scripts",
            "docs",
            "tests"
        ]
        
        for dir_path in key_directories:
            full_path = project_root / dir_path
            if full_path.exists():
                dir_analysis = self._analyze_directory(full_path)
                structure_analysis["directories"][dir_path] = dir_analysis
                print(f"   📂 {dir_path}: {dir_analysis['file_count']} files")
            else:
                print(f"   ❌ {dir_path}: Directory not found")
        
        # Core module analysis
        src_path = project_root / "src" / "quantum_edge"
        if src_path.exists():
            core_modules = [
                "config",
                "data",
                "discovery", 
                "risk",
                "backtesting",
                "web"
            ]
            
            print(f"\n   🔍 Core Module Analysis:")
            for module in core_modules:
                module_path = src_path / module
                if module_path.exists():
                    module_analysis = self._analyze_module(module_path)
                    structure_analysis["complexity_metrics"][module] = module_analysis
                    print(f"     ✅ {module}: {module_analysis['files']} files, {module_analysis['lines']} lines")
                else:
                    print(f"     ❌ {module}: Module missing")
                    self.code_issues.append(f"Missing core module: {module}")
        
        self.audit_results["codebase_structure"] = structure_analysis
    
    def _analyze_directory(self, directory_path):
        """Analyze a specific directory."""
        analysis = {
            "file_count": 0,
            "python_files": 0,
            "total_lines": 0,
            "avg_file_size": 0
        }
        
        files = list(directory_path.rglob("*"))
        python_files = [f for f in files if f.suffix == ".py" and f.is_file()]
        
        analysis["file_count"] = len([f for f in files if f.is_file()])
        analysis["python_files"] = len(python_files)
        
        total_lines = 0
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                    total_lines += lines
            except Exception:
                pass
        
        analysis["total_lines"] = total_lines
        analysis["avg_file_size"] = total_lines / len(python_files) if python_files else 0
        
        return analysis
    
    def _analyze_module(self, module_path):
        """Analyze a specific module."""
        analysis = {
            "files": 0,
            "lines": 0,
            "classes": 0,
            "functions": 0,
            "complexity": 0
        }
        
        python_files = list(module_path.rglob("*.py"))
        analysis["files"] = len(python_files)
        
        total_lines = 0
        total_classes = 0
        total_functions = 0
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = len(content.splitlines())
                    total_lines += lines
                
                # Parse AST for complexity analysis
                try:
                    tree = ast.parse(content)
                    for node in ast.walk(tree):
                        if isinstance(node, ast.ClassDef):
                            total_classes += 1
                        elif isinstance(node, ast.FunctionDef):
                            total_functions += 1
                except:
                    pass
                    
            except Exception:
                pass
        
        analysis["lines"] = total_lines
        analysis["classes"] = total_classes
        analysis["functions"] = total_functions
        analysis["complexity"] = (total_classes + total_functions) / len(python_files) if python_files else 0
        
        return analysis
    
    def assess_code_quality(self):
        """Assess code quality across the system."""
        print("\n🔍 CODE QUALITY ASSESSMENT")
        print("-" * 50)
        
        quality_metrics = {
            "import_issues": [],
            "naming_conventions": [],
            "documentation_coverage": 0,
            "error_handling": [],
            "type_hints": [],
            "best_practices": []
        }
        
        # Check core modules for quality issues
        src_path = Path(__file__).parent.parent / "src" / "quantum_edge"
        
        core_files = [
            "config/settings.py",
            "data/sources/polygon_client.py",
            "discovery/growth_scorer.py",
            "risk/risk_manager.py",
            "web/app.py"
        ]
        
        for file_path in core_files:
            full_path = src_path / file_path
            if full_path.exists():
                file_quality = self._assess_file_quality(full_path)
                print(f"   📄 {file_path}:")
                print(f"     Documentation: {file_quality['documentation_score']}/100")
                print(f"     Error Handling: {file_quality['error_handling_score']}/100")
                print(f"     Code Style: {file_quality['style_score']}/100")
                
                if file_quality['issues']:
                    for issue in file_quality['issues']:
                        self.code_issues.append(f"{file_path}: {issue}")
            else:
                print(f"   ❌ {file_path}: File not found")
                self.code_issues.append(f"Missing core file: {file_path}")
        
        self.audit_results["code_quality"] = quality_metrics
    
    def _assess_file_quality(self, file_path):
        """Assess quality of a specific file."""
        quality = {
            "documentation_score": 0,
            "error_handling_score": 0,
            "style_score": 0,
            "issues": []
        }
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.splitlines()
            
            # Documentation assessment
            docstring_lines = len([line for line in lines if '"""' in line or "'''" in line])
            comment_lines = len([line for line in lines if line.strip().startswith('#')])
            total_lines = len([line for line in lines if line.strip()])
            
            if total_lines > 0:
                doc_ratio = (docstring_lines + comment_lines) / total_lines
                quality["documentation_score"] = min(100, doc_ratio * 200)
            
            # Error handling assessment
            try_blocks = content.count('try:')
            except_blocks = content.count('except')
            if try_blocks > 0:
                quality["error_handling_score"] = min(100, (except_blocks / try_blocks) * 100)
            else:
                quality["error_handling_score"] = 50  # No try blocks found
            
            # Style assessment (basic checks)
            style_score = 100
            
            # Check for long lines
            long_lines = [i for i, line in enumerate(lines) if len(line) > 120]
            if long_lines:
                style_score -= min(20, len(long_lines) * 2)
                quality["issues"].append(f"{len(long_lines)} lines exceed 120 characters")
            
            # Check for proper imports
            import_lines = [line for line in lines if line.strip().startswith('import ') or line.strip().startswith('from ')]
            if len(import_lines) > 20:
                style_score -= 10
                quality["issues"].append("High number of imports (consider refactoring)")
            
            quality["style_score"] = max(0, style_score)
            
        except Exception as e:
            quality["issues"].append(f"File analysis error: {e}")
        
        return quality
    
    def validate_architecture_patterns(self):
        """Validate architecture patterns and design principles."""
        print("\n🏗️ ARCHITECTURE PATTERN VALIDATION")
        print("-" * 50)
        
        architecture_assessment = {
            "separation_of_concerns": 0,
            "dependency_injection": 0,
            "error_handling_patterns": 0,
            "async_patterns": 0,
            "design_patterns": []
        }
        
        # Check separation of concerns
        modules = ["config", "data", "discovery", "risk", "backtesting", "web"]
        existing_modules = []
        
        src_path = Path(__file__).parent.parent / "src" / "quantum_edge"
        for module in modules:
            if (src_path / module).exists():
                existing_modules.append(module)
        
        separation_score = (len(existing_modules) / len(modules)) * 100
        architecture_assessment["separation_of_concerns"] = separation_score
        
        print(f"   ✅ Separation of Concerns: {separation_score:.1f}% ({len(existing_modules)}/{len(modules)} modules)")
        
        # Check async patterns
        async_files = []
        for py_file in src_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'async def' in content or 'await ' in content:
                        async_files.append(py_file.name)
            except:
                pass
        
        if async_files:
            print(f"   ✅ Async Patterns: Found in {len(async_files)} files")
            architecture_assessment["async_patterns"] = 100
        else:
            print(f"   ⚠️ Async Patterns: Limited async usage")
            architecture_assessment["async_patterns"] = 50
        
        # Check error handling patterns
        error_handling_files = []
        for py_file in src_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'try:' in content and 'except' in content:
                        error_handling_files.append(py_file.name)
            except:
                pass
        
        if error_handling_files:
            print(f"   ✅ Error Handling: Implemented in {len(error_handling_files)} files")
            architecture_assessment["error_handling_patterns"] = 100
        else:
            print(f"   ❌ Error Handling: Limited error handling")
            architecture_assessment["error_handling_patterns"] = 30
        
        self.audit_results["architecture_patterns"] = architecture_assessment
    
    def scan_security_vulnerabilities(self):
        """Scan for security vulnerabilities."""
        print("\n🛡️ SECURITY VULNERABILITY SCAN")
        print("-" * 50)
        
        security_findings = {
            "api_key_exposure": [],
            "sql_injection_risks": [],
            "input_validation": [],
            "authentication_issues": [],
            "data_encryption": []
        }
        
        # Check for API key exposure
        src_path = Path(__file__).parent.parent / "src" / "quantum_edge"
        
        for py_file in src_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    # Check for hardcoded API keys
                    if 'api_key' in content.lower() and ('=' in content):
                        lines = content.splitlines()
                        for i, line in enumerate(lines):
                            if 'api_key' in line.lower() and '=' in line and not line.strip().startswith('#'):
                                if '"' in line or "'" in line:
                                    # Check if it's not using environment variables
                                    if 'os.getenv' not in line and 'os.environ' not in line:
                                        security_findings["api_key_exposure"].append(f"{py_file.name}:{i+1}")
                    
                    # Check for SQL injection risks (if any SQL usage)
                    if 'SELECT' in content.upper() or 'INSERT' in content.upper():
                        if '%s' in content or '.format(' in content:
                            security_findings["sql_injection_risks"].append(py_file.name)
                    
                    # Check input validation
                    if 'request.' in content and 'validate' not in content.lower():
                        security_findings["input_validation"].append(py_file.name)
                        
            except:
                pass
        
        # Report findings
        if not any(security_findings.values()):
            print("   ✅ No critical security vulnerabilities found")
        else:
            for category, findings in security_findings.items():
                if findings:
                    print(f"   ⚠️ {category.replace('_', ' ').title()}: {len(findings)} issues")
                    for finding in findings:
                        self.security_findings.append(f"{category}: {finding}")
        
        self.audit_results["security_scan"] = security_findings
    
    def analyze_performance_characteristics(self):
        """Analyze performance characteristics."""
        print("\n⚡ PERFORMANCE CHARACTERISTICS ANALYSIS")
        print("-" * 50)
        
        performance_analysis = {
            "async_usage": 0,
            "caching_implementation": 0,
            "database_optimization": 0,
            "memory_efficiency": 0,
            "algorithmic_complexity": {}
        }
        
        # Check async usage
        src_path = Path(__file__).parent.parent / "src" / "quantum_edge"
        async_count = 0
        total_functions = 0
        
        for py_file in src_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    async_count += content.count('async def')
                    total_functions += content.count('def ')
            except:
                pass
        
        if total_functions > 0:
            async_ratio = (async_count / total_functions) * 100
            performance_analysis["async_usage"] = async_ratio
            print(f"   📊 Async Usage: {async_ratio:.1f}% ({async_count}/{total_functions} functions)")
        
        # Check caching implementation
        caching_files = []
        for py_file in src_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'cache' in content.lower() or 'lru_cache' in content:
                        caching_files.append(py_file.name)
            except:
                pass
        
        if caching_files:
            print(f"   ✅ Caching: Implemented in {len(caching_files)} files")
            performance_analysis["caching_implementation"] = 100
        else:
            print(f"   ⚠️ Caching: Limited caching implementation")
            performance_analysis["caching_implementation"] = 30
        
        # Memory efficiency check
        large_data_structures = []
        for py_file in src_path.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'pandas' in content or 'numpy' in content:
                        large_data_structures.append(py_file.name)
            except:
                pass
        
        if large_data_structures:
            print(f"   📊 Memory Usage: {len(large_data_structures)} files use large data structures")
            performance_analysis["memory_efficiency"] = 70
        else:
            print(f"   ✅ Memory Usage: Efficient data structure usage")
            performance_analysis["memory_efficiency"] = 90
        
        self.audit_results["performance_analysis"] = performance_analysis
    
    def generate_audit_report(self):
        """Generate comprehensive audit report."""
        print("\n" + "=" * 70)
        print("📋 COMPREHENSIVE SYSTEM AUDIT REPORT")
        print("=" * 70)
        
        # Overall system health
        total_issues = len(self.code_issues) + len(self.security_findings)
        
        print(f"\n🎯 SYSTEM HEALTH OVERVIEW:")
        print(f"   Total Issues Found: {total_issues}")
        print(f"   Code Quality Issues: {len(self.code_issues)}")
        print(f"   Security Findings: {len(self.security_findings)}")
        
        # Codebase structure summary
        structure = self.audit_results.get("codebase_structure", {})
        print(f"\n📁 CODEBASE STRUCTURE:")
        print(f"   Directories Analyzed: {len(structure.get('directories', {}))}")
        
        complexity = structure.get("complexity_metrics", {})
        if complexity:
            total_files = sum(m.get('files', 0) for m in complexity.values())
            total_lines = sum(m.get('lines', 0) for m in complexity.values())
            print(f"   Total Python Files: {total_files}")
            print(f"   Total Lines of Code: {total_lines}")
        
        # Architecture assessment
        arch = self.audit_results.get("architecture_patterns", {})
        print(f"\n🏗️ ARCHITECTURE ASSESSMENT:")
        print(f"   Separation of Concerns: {arch.get('separation_of_concerns', 0):.1f}%")
        print(f"   Async Patterns: {arch.get('async_patterns', 0):.1f}%")
        print(f"   Error Handling: {arch.get('error_handling_patterns', 0):.1f}%")
        
        # Performance analysis
        perf = self.audit_results.get("performance_analysis", {})
        print(f"\n⚡ PERFORMANCE ANALYSIS:")
        print(f"   Async Usage: {perf.get('async_usage', 0):.1f}%")
        print(f"   Caching Implementation: {perf.get('caching_implementation', 0):.1f}%")
        print(f"   Memory Efficiency: {perf.get('memory_efficiency', 0):.1f}%")
        
        # Critical issues
        if self.code_issues:
            print(f"\n🚨 CRITICAL CODE ISSUES:")
            for issue in self.code_issues[:10]:  # Show top 10
                print(f"   • {issue}")
        
        if self.security_findings:
            print(f"\n🛡️ SECURITY FINDINGS:")
            for finding in self.security_findings[:5]:  # Show top 5
                print(f"   • {finding}")
        
        # Overall assessment
        arch_numeric = {k: v for k, v in arch.items() if isinstance(v, (int, float))}
        perf_numeric = {k: v for k, v in perf.items() if isinstance(v, (int, float))}

        architecture_score = sum(arch_numeric.values()) / len(arch_numeric) if arch_numeric else 0
        performance_score = sum(perf_numeric.values()) / len(perf_numeric) if perf_numeric else 0
        security_score = 100 - min(100, len(self.security_findings) * 20)
        quality_score = 100 - min(100, len(self.code_issues) * 10)
        
        overall_score = (architecture_score + performance_score + security_score + quality_score) / 4
        
        print(f"\n🎯 OVERALL SYSTEM SCORE: {overall_score:.1f}/100")
        
        if overall_score >= 90:
            status = "🎉 EXCELLENT - Production Ready"
        elif overall_score >= 80:
            status = "✅ GOOD - Minor Issues"
        elif overall_score >= 70:
            status = "⚠️ ACCEPTABLE - Needs Attention"
        else:
            status = "❌ NEEDS MAJOR WORK"
        
        print(f"   System Status: {status}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if overall_score >= 85:
            print(f"   • System is ready for production deployment")
            print(f"   • Implement monitoring and alerting")
            print(f"   • Continue regular code reviews")
        else:
            print(f"   • Address critical code issues before deployment")
            print(f"   • Enhance error handling and validation")
            print(f"   • Implement comprehensive testing")
        
        print("\n" + "=" * 70)


def main():
    """Run comprehensive system audit."""
    auditor = ComprehensiveSystemAudit()
    auditor.execute_comprehensive_audit()


if __name__ == "__main__":
    main()
