# QuantumEdge Microcap Radar - Environment Configuration

# =============================================================================
# API KEYS - REQUIRED FOR PRODUCTION
# =============================================================================

# Market Data APIs
POLYGON_API_KEY=your_polygon_api_key_here
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# News & Sentiment APIs
BENZINGA_API_KEY=your_benzinga_api_key_here
STOCKTWITS_ACCESS_TOKEN=your_stocktwits_token_here
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here

# AI & ML APIs (Optional)
OPENAI_API_KEY=your_openai_api_key_here
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Redis Cache
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# PostgreSQL Database
DATABASE_URL=postgresql://user:password@localhost:5432/quantum_edge
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=quantum_edge
DATABASE_USER=quantum_user
DATABASE_PASSWORD=secure_password

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================

# Environment
ENVIRONMENT=development  # development, staging, production
DEBUG=true
LOG_LEVEL=INFO

# Trading Parameters
PRE_MARKET_START_HOUR=4
PRE_MARKET_START_MINUTE=0
GAP_THRESHOLD_PERCENT=5.0
MICROCAP_CEILING=300000000  # $300M
PENNY_STOCK_LIMIT=5.0
MAX_POSITION_SIZE_PERCENT=1.0
VWAP_TRAIL_PERCENT=2.0
PORTFOLIO_BETA_CAP=1.5

# Quantum Computing
QUANTUM_DEVICE=lightning.qubit
QUANTUM_WIRES=4

# Model Configuration
MODEL_PATH=data/models/quantum_edge_v3.pkl
MODEL_RETRAIN_INTERVAL_HOURS=24
FEATURE_CACHE_TTL_SECONDS=300

# =============================================================================
# MONITORING & ALERTS
# =============================================================================

# Discord Webhook (Optional)
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your_webhook_here

# Slack Webhook (Optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your_webhook_here

# Email Alerts (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
ALERT_EMAIL_TO=<EMAIL>

# =============================================================================
# PERFORMANCE & SCALING
# =============================================================================

# Concurrency
MAX_CONCURRENT_REQUESTS=50
REQUEST_TIMEOUT_SECONDS=30
RETRY_ATTEMPTS=3
RETRY_DELAY_SECONDS=1

# Caching
CACHE_TTL_SECONDS=300
MAX_CACHE_SIZE=1000

# Rate Limiting
POLYGON_RATE_LIMIT_PER_MINUTE=5
ALPACA_RATE_LIMIT_PER_MINUTE=200
BENZINGA_RATE_LIMIT_PER_MINUTE=100

# =============================================================================
# BACKTESTING & VALIDATION
# =============================================================================

# Backtesting Parameters
BACKTEST_START_DATE=2023-01-01
BACKTEST_END_DATE=2024-12-31
WALK_FORWARD_WINDOW_MONTHS=6
MONTE_CARLO_ITERATIONS=10000

# Validation Thresholds
MIN_WIN_RATE_PERCENT=60
MIN_SHARPE_RATIO=2.0
MAX_DRAWDOWN_PERCENT=15
MIN_PROFIT_FACTOR=2.5

# =============================================================================
# SECTOR-SPECIFIC CONFIGURATION
# =============================================================================

# AI/ML Sector Keywords
AI_KEYWORDS=artificial intelligence,machine learning,neural network,deep learning,computer vision,natural language processing

# Quantum Computing Keywords
QUANTUM_KEYWORDS=quantum computing,quantum algorithm,quantum supremacy,quantum advantage,qubit

# Biotech Keywords
BIOTECH_KEYWORDS=gene therapy,CRISPR,immunotherapy,clinical trial,FDA approval,biomarker

# Clean Energy Keywords
CLEAN_ENERGY_KEYWORDS=solar,wind,battery,energy storage,renewable,carbon capture

# Space Technology Keywords
SPACE_KEYWORDS=satellite,space,aerospace,rocket,orbital,constellation

# Cybersecurity Keywords
CYBERSECURITY_KEYWORDS=cybersecurity,zero trust,encryption,threat detection,security

# =============================================================================
# DEVELOPMENT & TESTING
# =============================================================================

# Testing
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5432/quantum_edge_test
PYTEST_TIMEOUT_SECONDS=300

# Development
JUPYTER_PORT=8888
DASHBOARD_PORT=8080
API_PORT=8000

# Logging
LOG_FILE_PATH=logs/quantum_edge.log
LOG_MAX_SIZE_MB=100
LOG_BACKUP_COUNT=5
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
