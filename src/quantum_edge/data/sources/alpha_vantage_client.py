"""
Alpha Vantage API client for fundamental data and extended market information.
Provides comprehensive fundamental analysis data including financials and earnings.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import pandas as pd

from .base import BaseDataSource, RateLimiter, DataSourceError, APIError
from ...config.api_config import APIProvider, APIConfig


class AlphaVantageClient(BaseDataSource):
    """
    Alpha Vantage API client for fundamental data.
    Supports company overviews, financial statements, and earnings data.
    """
    
    def __init__(self, api_key: str, rate_limit_per_minute: int = 5):
        """
        Initialize Alpha Vantage client.
        
        Args:
            api_key: Alpha Vantage API key
            rate_limit_per_minute: Rate limit for API calls
        """
        if not api_key:
            raise ValueError("Alpha Vantage API key is required")
        
        api_config = APIConfig()
        config = api_config.get_config(APIProvider.ALPHA_VANTAGE)
        
        rate_limiter = RateLimiter(
            requests_per_minute=rate_limit_per_minute,
            requests_per_hour=config.rate_limit.requests_per_day // 24 if config.rate_limit and config.rate_limit.requests_per_day else None
        )
        
        super().__init__(
            base_url=config.base_url,
            timeout_seconds=config.timeout_seconds,
            retry_attempts=config.retry_attempts,
            retry_delay_seconds=config.retry_delay_seconds,
            rate_limiter=rate_limiter
        )
        
        self.api_key = api_key
        self._api_config = api_config
    
    async def test_connection(self) -> bool:
        """Test connection to Alpha Vantage API."""
        try:
            response = await self.get("/query", params={
                "function": "GLOBAL_QUOTE",
                "symbol": "AAPL",
                "apikey": self.api_key
            })
            return "Global Quote" in response or "01. symbol" in response.get("Global Quote", {})
        except Exception as e:
            self.logger.error(f"Alpha Vantage connection test failed: {e}")
            return False
    
    async def get_market_data(self, symbols: List[str], **kwargs) -> Dict[str, Any]:
        """Get basic market data for symbols."""
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        # Use company overview for basic data
        return await self.get_company_overviews(symbols)
    
    async def get_company_overview(self, symbol: str) -> Dict[str, Any]:
        """
        Get comprehensive company overview.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with company overview data
        """
        symbol = symbol.upper().strip()
        
        try:
            response = await self.get("/query", params={
                "function": "OVERVIEW",
                "symbol": symbol,
                "apikey": self.api_key
            })
            
            if not response or "Symbol" not in response:
                return {}
            
            return {
                "symbol": response.get("Symbol"),
                "name": response.get("Name"),
                "description": response.get("Description"),
                "exchange": response.get("Exchange"),
                "currency": response.get("Currency"),
                "country": response.get("Country"),
                "sector": response.get("Sector"),
                "industry": response.get("Industry"),
                "market_cap": self._safe_float(response.get("MarketCapitalization")),
                "pe_ratio": self._safe_float(response.get("PERatio")),
                "peg_ratio": self._safe_float(response.get("PEGRatio")),
                "book_value": self._safe_float(response.get("BookValue")),
                "dividend_per_share": self._safe_float(response.get("DividendPerShare")),
                "dividend_yield": self._safe_float(response.get("DividendYield")),
                "eps": self._safe_float(response.get("EPS")),
                "revenue_per_share": self._safe_float(response.get("RevenuePerShareTTM")),
                "profit_margin": self._safe_float(response.get("ProfitMargin")),
                "operating_margin": self._safe_float(response.get("OperatingMarginTTM")),
                "return_on_assets": self._safe_float(response.get("ReturnOnAssetsTTM")),
                "return_on_equity": self._safe_float(response.get("ReturnOnEquityTTM")),
                "revenue_ttm": self._safe_float(response.get("RevenueTTM")),
                "gross_profit_ttm": self._safe_float(response.get("GrossProfitTTM")),
                "diluted_eps_ttm": self._safe_float(response.get("DilutedEPSTTM")),
                "quarterly_earnings_growth": self._safe_float(response.get("QuarterlyEarningsGrowthYOY")),
                "quarterly_revenue_growth": self._safe_float(response.get("QuarterlyRevenueGrowthYOY")),
                "analyst_target_price": self._safe_float(response.get("AnalystTargetPrice")),
                "trailing_pe": self._safe_float(response.get("TrailingPE")),
                "forward_pe": self._safe_float(response.get("ForwardPE")),
                "price_to_sales": self._safe_float(response.get("PriceToSalesRatioTTM")),
                "price_to_book": self._safe_float(response.get("PriceToBookRatio")),
                "ev_to_revenue": self._safe_float(response.get("EVToRevenue")),
                "ev_to_ebitda": self._safe_float(response.get("EVToEBITDA")),
                "beta": self._safe_float(response.get("Beta")),
                "52_week_high": self._safe_float(response.get("52WeekHigh")),
                "52_week_low": self._safe_float(response.get("52WeekLow")),
                "50_day_ma": self._safe_float(response.get("50DayMovingAverage")),
                "200_day_ma": self._safe_float(response.get("200DayMovingAverage")),
                "shares_outstanding": self._safe_float(response.get("SharesOutstanding")),
                "dividend_date": response.get("DividendDate"),
                "ex_dividend_date": response.get("ExDividendDate"),
            }
        
        except Exception as e:
            self.logger.error(f"Error fetching company overview for {symbol}: {e}")
            return {}
    
    async def get_company_overviews(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Get company overviews for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary mapping symbols to overview data
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        overviews = {}
        
        # Process symbols with rate limiting
        for symbol in symbols:
            try:
                overview = await self.get_company_overview(symbol)
                if overview:
                    overviews[symbol] = overview
                
                # Rate limiting delay
                await asyncio.sleep(12)  # 5 calls per minute = 12 seconds between calls
                
            except Exception as e:
                self.logger.warning(f"Error getting overview for {symbol}: {e}")
                continue
        
        return overviews
    
    async def get_income_statement(self, symbol: str) -> pd.DataFrame:
        """
        Get income statement data.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            DataFrame with income statement data
        """
        symbol = symbol.upper().strip()
        
        try:
            response = await self.get("/query", params={
                "function": "INCOME_STATEMENT",
                "symbol": symbol,
                "apikey": self.api_key
            })
            
            if "annualReports" not in response:
                return pd.DataFrame()
            
            reports = response["annualReports"]
            if not reports:
                return pd.DataFrame()
            
            # Convert to DataFrame
            df = pd.DataFrame(reports)
            
            # Convert fiscal date to datetime
            if "fiscalDateEnding" in df.columns:
                df["fiscalDateEnding"] = pd.to_datetime(df["fiscalDateEnding"])
                df.set_index("fiscalDateEnding", inplace=True)
            
            # Convert numeric columns
            numeric_columns = [
                "totalRevenue", "costOfRevenue", "grossProfit", "researchAndDevelopment",
                "sellingGeneralAndAdministrative", "operatingIncome", "interestIncome",
                "interestExpense", "incomeBeforeTax", "incomeTaxExpense", "netIncome"
            ]
            
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            return df
        
        except Exception as e:
            self.logger.error(f"Error fetching income statement for {symbol}: {e}")
            return pd.DataFrame()
    
    async def get_balance_sheet(self, symbol: str) -> pd.DataFrame:
        """
        Get balance sheet data.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            DataFrame with balance sheet data
        """
        symbol = symbol.upper().strip()
        
        try:
            response = await self.get("/query", params={
                "function": "BALANCE_SHEET",
                "symbol": symbol,
                "apikey": self.api_key
            })
            
            if "annualReports" not in response:
                return pd.DataFrame()
            
            reports = response["annualReports"]
            if not reports:
                return pd.DataFrame()
            
            # Convert to DataFrame
            df = pd.DataFrame(reports)
            
            # Convert fiscal date to datetime
            if "fiscalDateEnding" in df.columns:
                df["fiscalDateEnding"] = pd.to_datetime(df["fiscalDateEnding"])
                df.set_index("fiscalDateEnding", inplace=True)
            
            # Convert numeric columns
            numeric_columns = [
                "totalAssets", "totalCurrentAssets", "cashAndCashEquivalentsAtCarryingValue",
                "cashAndShortTermInvestments", "inventory", "currentNetReceivables",
                "totalNonCurrentAssets", "propertyPlantEquipment", "accumulatedDepreciationAmortizationPPE",
                "intangibleAssets", "intangibleAssetsExcludingGoodwill", "goodwill",
                "totalLiabilities", "totalCurrentLiabilities", "currentAccountsPayable",
                "deferredRevenue", "currentDebt", "shortTermDebt", "totalNonCurrentLiabilities",
                "capitalLeaseObligations", "longTermDebt", "currentLongTermDebt",
                "longTermDebtNoncurrent", "shortLongTermDebtTotal", "otherCurrentLiabilities",
                "otherNonCurrentLiabilities", "totalShareholderEquity", "treasuryStock",
                "retainedEarnings", "commonStock", "commonStockSharesOutstanding"
            ]
            
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            return df
        
        except Exception as e:
            self.logger.error(f"Error fetching balance sheet for {symbol}: {e}")
            return pd.DataFrame()
    
    async def get_earnings(self, symbol: str) -> Dict[str, Any]:
        """
        Get earnings data.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with earnings data
        """
        symbol = symbol.upper().strip()
        
        try:
            response = await self.get("/query", params={
                "function": "EARNINGS",
                "symbol": symbol,
                "apikey": self.api_key
            })
            
            return {
                "symbol": symbol,
                "annual_earnings": response.get("annualEarnings", []),
                "quarterly_earnings": response.get("quarterlyEarnings", [])
            }
        
        except Exception as e:
            self.logger.error(f"Error fetching earnings for {symbol}: {e}")
            return {}
    
    def _safe_float(self, value: Any) -> Optional[float]:
        """Safely convert value to float."""
        if value is None or value == "None" or value == "":
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
