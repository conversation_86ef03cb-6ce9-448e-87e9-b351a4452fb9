"""
Benzinga API client for news, sentiment, and market events.
Provides real-time news, analyst ratings, earnings calendars, and market sentiment.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import pandas as pd

from .base import BaseDataSource, RateLimiter, DataSourceError, APIError
from ...config.api_config import APIProvider, APIConfig


class BenzingaClient(BaseDataSource):
    """
    Benzinga API client for news and market events.
    Supports real-time news, analyst ratings, earnings calendars, and sentiment analysis.
    """
    
    def __init__(self, api_key: str, rate_limit_per_minute: int = 100):
        """
        Initialize Benzinga client.
        
        Args:
            api_key: Benzinga API key
            rate_limit_per_minute: Rate limit for API calls
        """
        if not api_key:
            raise ValueError("Benzinga API key is required")
        
        api_config = APIConfig()
        config = api_config.get_config(APIProvider.BENZINGA)
        
        headers = {"accept": "application/json"}
        rate_limiter = RateLimiter(requests_per_minute=rate_limit_per_minute)
        
        super().__init__(
            base_url=config.base_url,
            headers=headers,
            timeout_seconds=config.timeout_seconds,
            retry_attempts=config.retry_attempts,
            retry_delay_seconds=config.retry_delay_seconds,
            rate_limiter=rate_limiter
        )
        
        self.api_key = api_key
        self._api_config = api_config
    
    async def test_connection(self) -> bool:
        """Test connection to Benzinga API."""
        try:
            response = await self.get("/api/v2/news", params={
                "token": self.api_key,
                "pagesize": 1
            })
            return isinstance(response, list) or "data" in response
        except Exception as e:
            self.logger.error(f"Benzinga connection test failed: {e}")
            return False
    
    async def get_market_data(self, symbols: List[str], **kwargs) -> Dict[str, Any]:
        """Get basic market data for symbols."""
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        # Use news data as basic market data
        return await self.get_news_for_symbols(symbols)
    
    async def get_news(
        self,
        symbols: Optional[List[str]] = None,
        channels: Optional[List[str]] = None,
        topics: Optional[List[str]] = None,
        limit: int = 50,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get news articles.
        
        Args:
            symbols: List of stock symbols to filter by
            channels: List of news channels to filter by
            topics: List of topics to filter by
            limit: Maximum number of articles
            date_from: Start date (YYYY-MM-DD)
            date_to: End date (YYYY-MM-DD)
            
        Returns:
            List of news articles
        """
        params = {
            "token": self.api_key,
            "pagesize": min(limit, 100)  # API limit
        }
        
        if symbols:
            symbols = self.validate_symbols(symbols)
            params["tickers"] = ",".join(symbols)
        
        if channels:
            params["channels"] = ",".join(channels)
        
        if topics:
            params["topics"] = ",".join(topics)
        
        if date_from:
            params["dateFrom"] = date_from
        
        if date_to:
            params["dateTo"] = date_to
        
        try:
            response = await self.get("/api/v2/news", params=params)
            
            # Handle different response formats
            if isinstance(response, list):
                articles = response
            elif isinstance(response, dict) and "data" in response:
                articles = response["data"]
            else:
                return []
            
            news_data = []
            for article in articles:
                news_data.append({
                    "id": article.get("id"),
                    "title": article.get("title"),
                    "teaser": article.get("teaser"),
                    "body": article.get("body"),
                    "url": article.get("url"),
                    "image": article.get("image"),
                    "author": article.get("author"),
                    "created": article.get("created"),
                    "updated": article.get("updated"),
                    "channels": article.get("channels", []),
                    "stocks": article.get("stocks", []),
                    "tags": article.get("tags", []),
                })
            
            return news_data
        
        except Exception as e:
            self.logger.error(f"Error fetching news: {e}")
            return []
    
    async def get_news_for_symbols(self, symbols: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get news for specific symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary mapping symbols to news articles
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        try:
            # Get news for all symbols at once
            news = await self.get_news(symbols=symbols, limit=100)
            
            # Group news by symbol
            symbol_news = {symbol: [] for symbol in symbols}
            
            for article in news:
                article_symbols = article.get("stocks", [])
                for symbol in symbols:
                    if any(stock.get("name") == symbol for stock in article_symbols):
                        symbol_news[symbol].append(article)
            
            return symbol_news
        
        except Exception as e:
            self.logger.error(f"Error fetching news for symbols: {e}")
            return {}
    
    async def get_analyst_ratings(
        self,
        symbols: Optional[List[str]] = None,
        limit: int = 50,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get analyst ratings.
        
        Args:
            symbols: List of stock symbols to filter by
            limit: Maximum number of ratings
            date_from: Start date (YYYY-MM-DD)
            date_to: End date (YYYY-MM-DD)
            
        Returns:
            List of analyst ratings
        """
        params = {
            "token": self.api_key,
            "pagesize": min(limit, 100)
        }
        
        if symbols:
            symbols = self.validate_symbols(symbols)
            params["tickers"] = ",".join(symbols)
        
        if date_from:
            params["dateFrom"] = date_from
        
        if date_to:
            params["dateTo"] = date_to
        
        try:
            response = await self.get("/api/v2.1/calendar/ratings", params=params)
            
            # Handle different response formats
            if isinstance(response, list):
                ratings = response
            elif isinstance(response, dict) and "ratings" in response:
                ratings = response["ratings"]
            else:
                return []
            
            ratings_data = []
            for rating in ratings:
                ratings_data.append({
                    "id": rating.get("id"),
                    "date": rating.get("date"),
                    "time": rating.get("time"),
                    "ticker": rating.get("ticker"),
                    "exchange": rating.get("exchange"),
                    "name": rating.get("name"),
                    "action_pt": rating.get("action_pt"),
                    "action_company": rating.get("action_company"),
                    "rating_current": rating.get("rating_current"),
                    "pt_current": rating.get("pt_current"),
                    "rating_prior": rating.get("rating_prior"),
                    "pt_prior": rating.get("pt_prior"),
                    "url": rating.get("url"),
                    "url_calendar": rating.get("url_calendar"),
                    "url_news": rating.get("url_news"),
                    "analyst": rating.get("analyst"),
                    "analyst_name": rating.get("analyst_name"),
                })
            
            return ratings_data
        
        except Exception as e:
            self.logger.error(f"Error fetching analyst ratings: {e}")
            return []
    
    async def get_earnings_calendar(
        self,
        symbols: Optional[List[str]] = None,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get earnings calendar.
        
        Args:
            symbols: List of stock symbols to filter by
            date_from: Start date (YYYY-MM-DD)
            date_to: End date (YYYY-MM-DD)
            limit: Maximum number of earnings events
            
        Returns:
            List of earnings events
        """
        params = {
            "token": self.api_key,
            "pagesize": min(limit, 100)
        }
        
        if symbols:
            symbols = self.validate_symbols(symbols)
            params["tickers"] = ",".join(symbols)
        
        if date_from:
            params["dateFrom"] = date_from
        
        if date_to:
            params["dateTo"] = date_to
        
        try:
            response = await self.get("/api/v2.1/calendar/earnings", params=params)
            
            # Handle different response formats
            if isinstance(response, list):
                earnings = response
            elif isinstance(response, dict) and "earnings" in response:
                earnings = response["earnings"]
            else:
                return []
            
            earnings_data = []
            for earning in earnings:
                earnings_data.append({
                    "id": earning.get("id"),
                    "date": earning.get("date"),
                    "date_confirmed": earning.get("date_confirmed"),
                    "time": earning.get("time"),
                    "ticker": earning.get("ticker"),
                    "exchange": earning.get("exchange"),
                    "name": earning.get("name"),
                    "period": earning.get("period"),
                    "period_year": earning.get("period_year"),
                    "eps": earning.get("eps"),
                    "eps_est": earning.get("eps_est"),
                    "eps_prior": earning.get("eps_prior"),
                    "revenue": earning.get("revenue"),
                    "revenue_est": earning.get("revenue_est"),
                    "revenue_prior": earning.get("revenue_prior"),
                    "importance": earning.get("importance"),
                    "updated": earning.get("updated"),
                })
            
            return earnings_data
        
        except Exception as e:
            self.logger.error(f"Error fetching earnings calendar: {e}")
            return []
    
    async def get_sentiment_analysis(self, symbols: List[str], days_back: int = 7) -> Dict[str, Dict[str, Any]]:
        """
        Get sentiment analysis for symbols based on recent news.
        
        Args:
            symbols: List of stock symbols
            days_back: Number of days to look back for news
            
        Returns:
            Dictionary mapping symbols to sentiment data
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        try:
            # Get recent news for sentiment analysis
            date_from = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
            date_to = datetime.now().strftime("%Y-%m-%d")
            
            news = await self.get_news(
                symbols=symbols,
                date_from=date_from,
                date_to=date_to,
                limit=200
            )
            
            # Simple sentiment analysis based on keywords
            sentiment_data = {}
            
            for symbol in symbols:
                symbol_articles = [
                    article for article in news
                    if any(stock.get("name") == symbol for stock in article.get("stocks", []))
                ]
                
                if not symbol_articles:
                    sentiment_data[symbol] = {
                        "sentiment_score": 0.0,
                        "article_count": 0,
                        "positive_count": 0,
                        "negative_count": 0,
                        "neutral_count": 0
                    }
                    continue
                
                positive_keywords = [
                    "bullish", "upgrade", "buy", "strong", "growth", "beat", "exceed",
                    "positive", "optimistic", "rally", "surge", "breakthrough", "innovation"
                ]
                
                negative_keywords = [
                    "bearish", "downgrade", "sell", "weak", "decline", "miss", "disappoint",
                    "negative", "pessimistic", "fall", "drop", "concern", "risk"
                ]
                
                positive_count = 0
                negative_count = 0
                neutral_count = 0
                
                for article in symbol_articles:
                    title = (article.get("title", "") + " " + article.get("teaser", "")).lower()
                    
                    pos_score = sum(1 for keyword in positive_keywords if keyword in title)
                    neg_score = sum(1 for keyword in negative_keywords if keyword in title)
                    
                    if pos_score > neg_score:
                        positive_count += 1
                    elif neg_score > pos_score:
                        negative_count += 1
                    else:
                        neutral_count += 1
                
                total_articles = len(symbol_articles)
                sentiment_score = (positive_count - negative_count) / total_articles if total_articles > 0 else 0.0
                
                sentiment_data[symbol] = {
                    "sentiment_score": sentiment_score,
                    "article_count": total_articles,
                    "positive_count": positive_count,
                    "negative_count": negative_count,
                    "neutral_count": neutral_count
                }
            
            return sentiment_data
        
        except Exception as e:
            self.logger.error(f"Error calculating sentiment analysis: {e}")
            return {}
