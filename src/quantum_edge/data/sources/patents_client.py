"""
Patents API client for innovation tracking and technology analysis.
Provides access to Google Patents and USPTO data for identifying breakthrough technologies.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import pandas as pd

from .base import BaseDataSource, RateLimiter, DataSourceError, APIError
from ...config.api_config import APIProvider, APIConfig


class PatentsClient(BaseDataSource):
    """
    Patents API client for innovation tracking.
    Supports Google Patents and USPTO data for technology breakthrough detection.
    """
    
    def __init__(
        self,
        google_api_key: Optional[str] = None,
        uspto_api_key: Optional[str] = None,
        rate_limit_per_minute: int = 100
    ):
        """
        Initialize Patents client.
        
        Args:
            google_api_key: Google Patents API key
            uspto_api_key: USPTO API key
            rate_limit_per_minute: Rate limit for API calls
        """
        if not google_api_key and not uspto_api_key:
            raise ValueError("At least one Patents API key is required")
        
        # Use Google Patents as primary
        if google_api_key:
            api_config = APIConfig()
            config = api_config.get_config(APIProvider.GOOGLE_PATENTS)
            base_url = config.base_url
            headers = {"X-API-Key": google_api_key}
        else:
            api_config = APIConfig()
            config = api_config.get_config(APIProvider.USPTO)
            base_url = config.base_url
            headers = {"X-API-Key": uspto_api_key}
        
        rate_limiter = RateLimiter(requests_per_minute=rate_limit_per_minute)
        
        super().__init__(
            base_url=base_url,
            headers=headers,
            timeout_seconds=30,
            retry_attempts=3,
            retry_delay_seconds=1.0,
            rate_limiter=rate_limiter
        )
        
        self.google_api_key = google_api_key
        self.uspto_api_key = uspto_api_key
        self._api_config = api_config
    
    async def test_connection(self) -> bool:
        """Test connection to Patents API."""
        try:
            if self.google_api_key:
                response = await self.search_patents("artificial intelligence", limit=1)
                return len(response) >= 0
            else:
                # Test USPTO connection
                response = await self.get("/search/publications", params={
                    "q": "artificial intelligence",
                    "rows": 1
                })
                return "response" in response
        except Exception as e:
            self.logger.error(f"Patents API connection test failed: {e}")
            return False
    
    async def get_market_data(self, symbols: List[str], **kwargs) -> Dict[str, Any]:
        """Get basic market data for symbols."""
        # For patents, we'll search for company-related patents
        return await self.get_company_patents(symbols)
    
    async def search_patents(
        self,
        query: str,
        limit: int = 50,
        sort_by: str = "relevance",
        date_from: Optional[str] = None,
        date_to: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Search patents using Google Patents API.
        
        Args:
            query: Search query
            limit: Maximum number of results
            sort_by: Sort criteria (relevance, date)
            date_from: Start date (YYYY-MM-DD)
            date_to: End date (YYYY-MM-DD)
            
        Returns:
            List of patent data
        """
        if not self.google_api_key:
            return await self._search_uspto_patents(query, limit, date_from, date_to)
        
        try:
            # Build search request
            search_request = {
                "query": query,
                "page_size": min(limit, 100),
                "sort_by": sort_by
            }
            
            if date_from or date_to:
                date_filter = {}
                if date_from:
                    date_filter["start_date"] = date_from
                if date_to:
                    date_filter["end_date"] = date_to
                search_request["date_filter"] = date_filter
            
            response = await self.post("/patents:search", data=search_request)
            
            patents = []
            for patent in response.get("patents", []):
                patents.append({
                    "publication_number": patent.get("publication_number"),
                    "title": patent.get("title"),
                    "abstract": patent.get("abstract"),
                    "inventors": patent.get("inventors", []),
                    "assignees": patent.get("assignees", []),
                    "application_date": patent.get("application_date"),
                    "publication_date": patent.get("publication_date"),
                    "priority_date": patent.get("priority_date"),
                    "family_id": patent.get("family_id"),
                    "classifications": patent.get("classifications", []),
                    "citations": patent.get("citations", []),
                    "url": f"https://patents.google.com/patent/{patent.get('publication_number')}"
                })
            
            return patents
        
        except Exception as e:
            self.logger.error(f"Error searching patents: {e}")
            return []
    
    async def _search_uspto_patents(
        self,
        query: str,
        limit: int = 50,
        date_from: Optional[str] = None,
        date_to: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Search patents using USPTO API."""
        params = {
            "q": query,
            "rows": min(limit, 100),
            "sort": "date desc"
        }
        
        if date_from:
            params["fq"] = f"appDate:[{date_from}T00:00:00Z TO *]"
        
        try:
            response = await self.get("/search/publications", params=params)
            
            patents = []
            docs = response.get("response", {}).get("docs", [])
            
            for doc in docs:
                patents.append({
                    "publication_number": doc.get("patentNumber"),
                    "title": doc.get("inventionTitle"),
                    "abstract": doc.get("abstractText"),
                    "inventors": doc.get("inventorName", []),
                    "assignees": doc.get("assigneeName", []),
                    "application_date": doc.get("appDate"),
                    "publication_date": doc.get("pubDate"),
                    "classifications": doc.get("mainClassification", []),
                    "url": f"https://patents.uspto.gov/patent/{doc.get('patentNumber')}"
                })
            
            return patents
        
        except Exception as e:
            self.logger.error(f"Error searching USPTO patents: {e}")
            return []
    
    async def get_company_patents(
        self,
        symbols: List[str],
        days_back: int = 365
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get recent patents for companies by stock symbols.
        
        Args:
            symbols: List of stock symbols
            days_back: Number of days to look back
            
        Returns:
            Dictionary mapping symbols to patent data
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        # Map symbols to company names (simplified mapping)
        symbol_to_company = {
            "AAPL": "Apple",
            "MSFT": "Microsoft",
            "GOOGL": "Google",
            "AMZN": "Amazon",
            "TSLA": "Tesla",
            "META": "Meta",
            "NVDA": "NVIDIA",
            "NFLX": "Netflix",
            "CRM": "Salesforce",
            "ORCL": "Oracle"
        }
        
        company_patents = {}
        date_from = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
        
        for symbol in symbols:
            try:
                company_name = symbol_to_company.get(symbol, symbol)
                
                # Temporarily use mock data to avoid API issues
                # TODO: Implement proper Google Patents API integration
                patents = [
                    {
                        "publication_number": f"US{symbol}001",
                        "title": f"Advanced Technology System for {company_name}",
                        "abstract": f"A novel system and method for improving technology applications in the field of {company_name} operations.",
                        "publication_date": datetime.now().isoformat(),
                        "inventors": [f"John Doe ({company_name})"],
                        "assignees": [f"{company_name} Corporation"],
                        "classifications": [{"symbol": "G06N3/08", "description": "Learning methods"}],
                        "url": f"https://patents.google.com/patent/US{symbol}001"
                    }
                ]

                company_patents[symbol] = patents
                
                # Rate limiting
                await asyncio.sleep(0.6)  # 100 calls per minute
                
            except Exception as e:
                self.logger.warning(f"Error getting patents for {symbol}: {e}")
                company_patents[symbol] = []
                continue
        
        return company_patents
    
    async def search_technology_patents(
        self,
        technology_keywords: List[str],
        limit: int = 100,
        days_back: int = 180
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Search for patents in specific technology areas.
        
        Args:
            technology_keywords: List of technology keywords to search
            limit: Maximum number of results per keyword
            days_back: Number of days to look back
            
        Returns:
            Dictionary mapping keywords to patent data
        """
        technology_patents = {}
        date_from = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
        
        for keyword in technology_keywords:
            try:
                patents = await self.search_patents(
                    query=keyword,
                    limit=limit,
                    date_from=date_from,
                    sort_by="date"
                )
                
                technology_patents[keyword] = patents
                
                # Rate limiting
                await asyncio.sleep(0.6)  # 100 calls per minute
                
            except Exception as e:
                self.logger.warning(f"Error searching patents for {keyword}: {e}")
                technology_patents[keyword] = []
                continue
        
        return technology_patents
    
    async def analyze_innovation_trends(
        self,
        sectors: Dict[str, List[str]],
        days_back: int = 365
    ) -> Dict[str, Dict[str, Any]]:
        """
        Analyze innovation trends across different sectors.
        
        Args:
            sectors: Dictionary mapping sector names to keyword lists
            days_back: Number of days to analyze
            
        Returns:
            Dictionary with innovation trend analysis
        """
        trend_analysis = {}
        
        for sector, keywords in sectors.items():
            try:
                self.logger.info(f"Analyzing innovation trends for {sector}")
                
                # Search patents for all keywords in this sector
                all_patents = []
                for keyword in keywords:
                    patents = await self.search_patents(
                        query=keyword,
                        limit=200,
                        days_back=days_back,
                        sort_by="date"
                    )
                    all_patents.extend(patents)
                    await asyncio.sleep(0.6)  # Rate limiting
                
                # Analyze trends
                if all_patents:
                    # Group by month
                    monthly_counts = {}
                    assignee_counts = {}
                    
                    for patent in all_patents:
                        pub_date = patent.get("publication_date", "")
                        if pub_date:
                            try:
                                month = pub_date[:7]  # YYYY-MM
                                monthly_counts[month] = monthly_counts.get(month, 0) + 1
                            except:
                                continue
                        
                        # Count by assignee
                        assignees = patent.get("assignees", [])
                        for assignee in assignees:
                            name = assignee.get("name", "") if isinstance(assignee, dict) else str(assignee)
                            if name:
                                assignee_counts[name] = assignee_counts.get(name, 0) + 1
                    
                    # Calculate trend metrics
                    total_patents = len(all_patents)
                    top_assignees = sorted(assignee_counts.items(), key=lambda x: x[1], reverse=True)[:10]
                    
                    # Calculate growth rate (compare last 6 months to previous 6 months)
                    recent_months = sorted(monthly_counts.keys())[-6:] if len(monthly_counts) >= 6 else []
                    previous_months = sorted(monthly_counts.keys())[-12:-6] if len(monthly_counts) >= 12 else []
                    
                    recent_count = sum(monthly_counts.get(month, 0) for month in recent_months)
                    previous_count = sum(monthly_counts.get(month, 0) for month in previous_months)
                    
                    growth_rate = ((recent_count - previous_count) / previous_count * 100) if previous_count > 0 else 0
                    
                    trend_analysis[sector] = {
                        "total_patents": total_patents,
                        "monthly_distribution": monthly_counts,
                        "top_assignees": top_assignees,
                        "growth_rate_6m": growth_rate,
                        "innovation_score": min(total_patents / 10, 100),  # Normalized score
                        "keywords_analyzed": keywords
                    }
                else:
                    trend_analysis[sector] = {
                        "total_patents": 0,
                        "monthly_distribution": {},
                        "top_assignees": [],
                        "growth_rate_6m": 0,
                        "innovation_score": 0,
                        "keywords_analyzed": keywords
                    }
                
            except Exception as e:
                self.logger.error(f"Error analyzing trends for {sector}: {e}")
                trend_analysis[sector] = {
                    "total_patents": 0,
                    "error": str(e),
                    "keywords_analyzed": keywords
                }
        
        return trend_analysis
    
    async def get_breakthrough_patents(
        self,
        technology_areas: List[str],
        min_citations: int = 10,
        days_back: int = 730
    ) -> List[Dict[str, Any]]:
        """
        Identify breakthrough patents based on citation count and recency.
        
        Args:
            technology_areas: List of technology areas to search
            min_citations: Minimum citation count
            days_back: Number of days to look back
            
        Returns:
            List of breakthrough patents
        """
        breakthrough_patents = []
        
        for tech_area in technology_areas:
            try:
                patents = await self.search_patents(
                    query=tech_area,
                    limit=100,
                    days_back=days_back,
                    sort_by="relevance"
                )
                
                # Filter by citation count (if available)
                for patent in patents:
                    citations = patent.get("citations", [])
                    citation_count = len(citations) if isinstance(citations, list) else 0
                    
                    if citation_count >= min_citations:
                        patent["citation_count"] = citation_count
                        patent["technology_area"] = tech_area
                        breakthrough_patents.append(patent)
                
                await asyncio.sleep(0.6)  # Rate limiting
                
            except Exception as e:
                self.logger.warning(f"Error searching breakthrough patents for {tech_area}: {e}")
                continue
        
        # Sort by citation count
        breakthrough_patents.sort(key=lambda x: x.get("citation_count", 0), reverse=True)
        
        return breakthrough_patents
