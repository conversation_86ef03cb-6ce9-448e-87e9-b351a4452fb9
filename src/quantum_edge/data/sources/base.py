"""
Base data source interface and common functionality.
Provides abstract base class for all data sources with rate limiting,
error handling, and caching capabilities.
"""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
import aiohttp
import logging
from datetime import datetime, timedelta

from ...config.logging_config import LoggerMixin, log_api_call


class DataSourceError(Exception):
    """Base exception for data source errors."""
    pass


class RateLimitError(DataSourceError):
    """Exception raised when rate limit is exceeded."""
    pass


class APIError(DataSourceError):
    """Exception raised for API-related errors."""
    pass


@dataclass
class RateLimiter:
    """Simple rate limiter implementation."""
    requests_per_minute: int
    requests_per_hour: Optional[int] = None
    
    def __post_init__(self):
        self.minute_requests = []
        self.hour_requests = []
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """Acquire permission to make a request."""
        async with self._lock:
            now = time.time()
            
            # Clean old requests
            self.minute_requests = [req_time for req_time in self.minute_requests if now - req_time < 60]
            if self.requests_per_hour:
                self.hour_requests = [req_time for req_time in self.hour_requests if now - req_time < 3600]
            
            # Check limits
            if len(self.minute_requests) >= self.requests_per_minute:
                sleep_time = 60 - (now - self.minute_requests[0])
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                    return await self.acquire()
            
            if self.requests_per_hour and len(self.hour_requests) >= self.requests_per_hour:
                sleep_time = 3600 - (now - self.hour_requests[0])
                if sleep_time > 0:
                    raise RateLimitError(f"Hourly rate limit exceeded. Wait {sleep_time:.0f} seconds.")
            
            # Record request
            self.minute_requests.append(now)
            if self.requests_per_hour:
                self.hour_requests.append(now)


class BaseDataSource(ABC, LoggerMixin):
    """
    Abstract base class for all data sources.
    Provides common functionality for API calls, rate limiting, and error handling.
    """
    
    def __init__(
        self,
        base_url: str,
        headers: Optional[Dict[str, str]] = None,
        timeout_seconds: int = 30,
        retry_attempts: int = 3,
        retry_delay_seconds: float = 1.0,
        rate_limiter: Optional[RateLimiter] = None
    ):
        """
        Initialize base data source.
        
        Args:
            base_url: Base URL for API endpoints
            headers: Default headers for requests
            timeout_seconds: Request timeout
            retry_attempts: Number of retry attempts
            retry_delay_seconds: Delay between retries
            rate_limiter: Rate limiter instance
        """
        self.base_url = base_url.rstrip('/')
        self.headers = headers or {}
        self.timeout_seconds = timeout_seconds
        self.retry_attempts = retry_attempts
        self.retry_delay_seconds = retry_delay_seconds
        self.rate_limiter = rate_limiter
        
        # Session will be created when needed
        self._session: Optional[aiohttp.ClientSession] = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session."""
        if self._session is None or self._session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout_seconds)
            self._session = aiohttp.ClientSession(
                headers=self.headers,
                timeout=timeout,
                connector=aiohttp.TCPConnector(limit=100, limit_per_host=30)
            )
        return self._session
    
    async def close(self) -> None:
        """Close the aiohttp session."""
        if self._session and not self._session.closed:
            await self._session.close()
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        additional_headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Make HTTP request with rate limiting, retries, and error handling.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            params: Query parameters
            data: Request body data
            additional_headers: Additional headers for this request
            
        Returns:
            Response data as dictionary
            
        Raises:
            RateLimitError: When rate limit is exceeded
            APIError: When API returns an error
            DataSourceError: For other data source errors
        """
        # Apply rate limiting
        if self.rate_limiter:
            await self.rate_limiter.acquire()
        
        url = f"{self.base_url}{endpoint}"
        headers = self.headers.copy()
        if additional_headers:
            headers.update(additional_headers)
        
        session = await self._get_session()
        
        for attempt in range(self.retry_attempts + 1):
            try:
                start_time = time.time()
                
                async with session.request(
                    method=method,
                    url=url,
                    params=params,
                    json=data,
                    headers=headers
                ) as response:
                    response_time = time.time() - start_time
                    
                    # Log API call performance
                    log_api_call(
                        api_name=self.__class__.__name__,
                        endpoint=endpoint,
                        response_time=response_time,
                        status_code=response.status
                    )
                    
                    # Handle rate limiting
                    if response.status == 429:
                        retry_after = int(response.headers.get('Retry-After', 60))
                        self.logger.warning(f"Rate limited. Waiting {retry_after} seconds.")
                        await asyncio.sleep(retry_after)
                        continue
                    
                    # Handle client errors
                    if 400 <= response.status < 500:
                        error_text = await response.text()
                        raise APIError(f"Client error {response.status}: {error_text}")
                    
                    # Handle server errors with retry
                    if response.status >= 500:
                        if attempt < self.retry_attempts:
                            self.logger.warning(f"Server error {response.status}. Retrying in {self.retry_delay_seconds}s...")
                            await asyncio.sleep(self.retry_delay_seconds * (2 ** attempt))
                            continue
                        else:
                            error_text = await response.text()
                            raise APIError(f"Server error {response.status}: {error_text}")
                    
                    # Success - parse response
                    try:
                        return await response.json()
                    except Exception as e:
                        self.logger.error(f"Failed to parse JSON response: {e}")
                        raise DataSourceError(f"Invalid JSON response: {e}")
            
            except aiohttp.ClientError as e:
                if attempt < self.retry_attempts:
                    self.logger.warning(f"Request failed: {e}. Retrying in {self.retry_delay_seconds}s...")
                    await asyncio.sleep(self.retry_delay_seconds * (2 ** attempt))
                    continue
                else:
                    raise DataSourceError(f"Request failed after {self.retry_attempts} attempts: {e}")
            
            except asyncio.TimeoutError:
                if attempt < self.retry_attempts:
                    self.logger.warning(f"Request timeout. Retrying in {self.retry_delay_seconds}s...")
                    await asyncio.sleep(self.retry_delay_seconds * (2 ** attempt))
                    continue
                else:
                    raise DataSourceError(f"Request timeout after {self.retry_attempts} attempts")
        
        raise DataSourceError("Unexpected error in request handling")
    
    async def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """Make GET request."""
        return await self._make_request("GET", endpoint, params=params, **kwargs)
    
    async def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """Make POST request."""
        return await self._make_request("POST", endpoint, data=data, **kwargs)
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """Test connection to the data source."""
        pass
    
    @abstractmethod
    async def get_market_data(self, symbols: List[str], **kwargs) -> Dict[str, Any]:
        """Get market data for specified symbols."""
        pass
    
    def validate_symbols(self, symbols: Union[str, List[str]]) -> List[str]:
        """Validate and normalize symbol list."""
        if isinstance(symbols, str):
            symbols = [symbols]
        
        # Basic validation
        validated_symbols = []
        for symbol in symbols:
            symbol = symbol.strip().upper()
            if symbol and len(symbol) <= 10:  # Basic length check
                validated_symbols.append(symbol)
            else:
                self.logger.warning(f"Invalid symbol: {symbol}")
        
        return validated_symbols
    
    def format_datetime(self, dt: datetime) -> str:
        """Format datetime for API requests."""
        return dt.strftime("%Y-%m-%d")
    
    def parse_datetime(self, date_str: str) -> datetime:
        """Parse datetime from API response."""
        try:
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except ValueError:
            # Try alternative formats
            for fmt in ["%Y-%m-%d", "%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%S"]:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue
            raise ValueError(f"Unable to parse datetime: {date_str}")
