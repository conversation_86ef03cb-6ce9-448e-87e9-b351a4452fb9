"""
Alpaca Markets API client for market data and asset information.
Provides historical data, real-time quotes, and asset fundamentals.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import pandas as pd

from .base import BaseDataSource, RateLimiter, DataSourceError, APIError
from ...config.api_config import APIProvider, APIConfig


class AlpacaClient(BaseDataSource):
    """
    Alpaca Markets API client for market data.
    Supports historical bars, quotes, trades, and asset information.
    """
    
    def __init__(self, api_key: str, secret_key: str, rate_limit_per_minute: int = 200):
        """
        Initialize Alpaca client.
        
        Args:
            api_key: Alpaca API key
            secret_key: Alpaca secret key
            rate_limit_per_minute: Rate limit for API calls
        """
        if not api_key or not secret_key:
            raise ValueError("Alpaca API key and secret key are required")
        
        api_config = APIConfig()
        config = api_config.get_config(APIProvider.ALPACA)
        
        headers = {
            "APCA-API-KEY-ID": api_key,
            "APCA-API-SECRET-KEY": secret_key,
        }
        
        rate_limiter = RateLimiter(requests_per_minute=rate_limit_per_minute)
        
        super().__init__(
            base_url=config.base_url,
            headers=headers,
            timeout_seconds=config.timeout_seconds,
            retry_attempts=config.retry_attempts,
            retry_delay_seconds=config.retry_delay_seconds,
            rate_limiter=rate_limiter
        )
        
        self.api_key = api_key
        self.secret_key = secret_key
        self._api_config = api_config
    
    async def test_connection(self) -> bool:
        """Test connection to Alpaca API."""
        try:
            response = await self.get("/v2/assets", params={"limit": 1})
            return isinstance(response, list) or "assets" in response
        except Exception as e:
            self.logger.error(f"Alpaca connection test failed: {e}")
            return False
    
    async def get_market_data(self, symbols: List[str], **kwargs) -> Dict[str, Any]:
        """Get basic market data for symbols."""
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        # Use latest quotes endpoint
        return await self.get_latest_quotes(symbols)
    
    async def get_assets(self, symbols: Optional[List[str]] = None, asset_class: str = "us_equity") -> List[Dict[str, Any]]:
        """
        Get asset information.
        
        Args:
            symbols: List of symbols to filter (optional)
            asset_class: Asset class filter
            
        Returns:
            List of asset information
        """
        params = {"asset_class": asset_class}
        
        if symbols:
            symbols = self.validate_symbols(symbols)
            params["symbols"] = ",".join(symbols)
        
        try:
            response = await self.get("/v2/assets", params=params)
            
            # Handle both list and dict responses
            if isinstance(response, list):
                assets = response
            else:
                assets = response.get("assets", [])
            
            asset_data = []
            for asset in assets:
                asset_data.append({
                    "symbol": asset.get("symbol"),
                    "name": asset.get("name"),
                    "exchange": asset.get("exchange"),
                    "asset_class": asset.get("class"),
                    "status": asset.get("status"),
                    "tradable": asset.get("tradable"),
                    "marginable": asset.get("marginable"),
                    "shortable": asset.get("shortable"),
                    "easy_to_borrow": asset.get("easy_to_borrow"),
                    "fractionable": asset.get("fractionable"),
                    "attributes": asset.get("attributes", []),
                })
            
            return asset_data
        
        except Exception as e:
            self.logger.error(f"Error fetching assets: {e}")
            return []
    
    async def get_latest_quotes(self, symbols: List[str]) -> Dict[str, Any]:
        """
        Get latest quotes for symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary with quote data
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        try:
            params = {"symbols": ",".join(symbols)}
            response = await self.get("/v2/stocks/quotes/latest", params=params)
            
            quotes = response.get("quotes", {})
            
            quote_data = {}
            for symbol, quote in quotes.items():
                quote_data[symbol] = {
                    "bid_price": quote.get("bp"),
                    "bid_size": quote.get("bs"),
                    "ask_price": quote.get("ap"),
                    "ask_size": quote.get("as"),
                    "timestamp": quote.get("t"),
                    "timeframe": quote.get("tf"),
                    "conditions": quote.get("c", []),
                }
            
            return quote_data
        
        except Exception as e:
            self.logger.error(f"Error fetching latest quotes: {e}")
            return {}
    
    async def get_latest_trades(self, symbols: List[str]) -> Dict[str, Any]:
        """
        Get latest trades for symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary with trade data
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        try:
            params = {"symbols": ",".join(symbols)}
            response = await self.get("/v2/stocks/trades/latest", params=params)
            
            trades = response.get("trades", {})
            
            trade_data = {}
            for symbol, trade in trades.items():
                trade_data[symbol] = {
                    "price": trade.get("p"),
                    "size": trade.get("s"),
                    "timestamp": trade.get("t"),
                    "conditions": trade.get("c", []),
                    "id": trade.get("i"),
                    "exchange": trade.get("x"),
                }
            
            return trade_data
        
        except Exception as e:
            self.logger.error(f"Error fetching latest trades: {e}")
            return {}
    
    async def get_bars(
        self,
        symbols: List[str],
        timeframe: str = "1Day",
        start: Optional[str] = None,
        end: Optional[str] = None,
        limit: int = 1000,
        page_token: Optional[str] = None
    ) -> Dict[str, pd.DataFrame]:
        """
        Get historical bars for symbols.
        
        Args:
            symbols: List of stock symbols
            timeframe: Bar timeframe (1Min, 5Min, 15Min, 1Hour, 1Day)
            start: Start time (RFC3339 format)
            end: End time (RFC3339 format)
            limit: Maximum number of bars per symbol
            page_token: Pagination token
            
        Returns:
            Dictionary mapping symbols to DataFrames with OHLCV data
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        # Default date range if not provided
        if not start:
            start = (datetime.now() - timedelta(days=30)).isoformat() + "Z"
        if not end:
            end = datetime.now().isoformat() + "Z"
        
        params = {
            "symbols": ",".join(symbols),
            "timeframe": timeframe,
            "start": start,
            "end": end,
            "limit": limit,
        }
        
        if page_token:
            params["page_token"] = page_token
        
        try:
            response = await self.get("/v2/stocks/bars", params=params)
            
            bars_data = response.get("bars", {})
            
            result = {}
            for symbol, bars in bars_data.items():
                if bars:
                    df_data = []
                    for bar in bars:
                        df_data.append({
                            "timestamp": pd.to_datetime(bar["t"]),
                            "open": bar["o"],
                            "high": bar["h"],
                            "low": bar["l"],
                            "close": bar["c"],
                            "volume": bar["v"],
                            "trade_count": bar.get("n"),
                            "vwap": bar.get("vw"),
                        })
                    
                    df = pd.DataFrame(df_data)
                    if not df.empty:
                        df.set_index("timestamp", inplace=True)
                    result[symbol] = df
                else:
                    result[symbol] = pd.DataFrame()
            
            return result
        
        except Exception as e:
            self.logger.error(f"Error fetching bars: {e}")
            return {}
    
    async def get_snapshots(self, symbols: List[str]) -> Dict[str, Any]:
        """
        Get market snapshots for symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary with snapshot data
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        try:
            params = {"symbols": ",".join(symbols)}
            response = await self.get("/v2/stocks/snapshots", params=params)
            
            snapshots = response.get("snapshots", {})
            
            snapshot_data = {}
            for symbol, snapshot in snapshots.items():
                latest_quote = snapshot.get("latestQuote", {})
                latest_trade = snapshot.get("latestTrade", {})
                daily_bar = snapshot.get("dailyBar", {})
                prev_daily_bar = snapshot.get("prevDailyBar", {})
                
                snapshot_data[symbol] = {
                    "latest_quote": {
                        "bid_price": latest_quote.get("bp"),
                        "bid_size": latest_quote.get("bs"),
                        "ask_price": latest_quote.get("ap"),
                        "ask_size": latest_quote.get("as"),
                        "timestamp": latest_quote.get("t"),
                    },
                    "latest_trade": {
                        "price": latest_trade.get("p"),
                        "size": latest_trade.get("s"),
                        "timestamp": latest_trade.get("t"),
                    },
                    "daily_bar": {
                        "open": daily_bar.get("o"),
                        "high": daily_bar.get("h"),
                        "low": daily_bar.get("l"),
                        "close": daily_bar.get("c"),
                        "volume": daily_bar.get("v"),
                        "timestamp": daily_bar.get("t"),
                    },
                    "prev_daily_bar": {
                        "open": prev_daily_bar.get("o"),
                        "high": prev_daily_bar.get("h"),
                        "low": prev_daily_bar.get("l"),
                        "close": prev_daily_bar.get("c"),
                        "volume": prev_daily_bar.get("v"),
                        "timestamp": prev_daily_bar.get("t"),
                    },
                }
            
            return snapshot_data
        
        except Exception as e:
            self.logger.error(f"Error fetching snapshots: {e}")
            return {}
    
    async def get_corporate_actions(
        self,
        symbols: Optional[List[str]] = None,
        types: Optional[str] = None,
        start: Optional[str] = None,
        end: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get corporate actions data.
        
        Args:
            symbols: List of symbols to filter
            types: Types of corporate actions (dividend, split, etc.)
            start: Start date
            end: End date
            
        Returns:
            List of corporate actions
        """
        params = {}
        
        if symbols:
            symbols = self.validate_symbols(symbols)
            params["symbols"] = ",".join(symbols)
        
        if types:
            params["types"] = types
        
        if start:
            params["start"] = start
        
        if end:
            params["end"] = end
        
        try:
            response = await self.get("/v1beta1/corporate-actions", params=params)
            
            return response.get("corporate_actions", [])
        
        except Exception as e:
            self.logger.error(f"Error fetching corporate actions: {e}")
            return []
