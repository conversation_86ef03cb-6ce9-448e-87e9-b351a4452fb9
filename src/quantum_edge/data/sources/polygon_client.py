"""
Polygon.io API client for real-time market data.
Provides comprehensive market data including quotes, trades, aggregates, and news.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import pandas as pd

from .base import BaseDataSource, RateLimiter, DataSourceError, APIError
from ...config.api_config import APIProvider, APIConfig


class PolygonClient(BaseDataSource):
    """
    Polygon.io API client for market data.
    Supports real-time quotes, historical data, and market snapshots.
    """
    
    def __init__(self, api_key: str, rate_limit_per_minute: int = 5):
        """
        Initialize Polygon client.
        
        Args:
            api_key: Polygon.io API key
            rate_limit_per_minute: Rate limit for API calls
        """
        if not api_key:
            raise ValueError("Polygon API key is required")
        
        api_config = APIConfig()
        config = api_config.get_config(APIProvider.POLYGON)
        
        headers = {"Authorization": f"Bearer {api_key}"}
        rate_limiter = RateLimiter(
            requests_per_minute=rate_limit_per_minute,
            requests_per_hour=config.rate_limit.requests_per_day // 24 if config.rate_limit else None
        )
        
        super().__init__(
            base_url=config.base_url,
            headers=headers,
            timeout_seconds=config.timeout_seconds,
            retry_attempts=config.retry_attempts,
            retry_delay_seconds=config.retry_delay_seconds,
            rate_limiter=rate_limiter
        )
        
        self.api_key = api_key
        self._api_config = api_config
    
    async def test_connection(self) -> bool:
        """Test connection to Polygon API."""
        try:
            response = await self.get("/v3/reference/tickers", params={"limit": 1})
            return "results" in response
        except Exception as e:
            self.logger.error(f"Polygon connection test failed: {e}")
            return False
    
    async def get_market_data(self, symbols: List[str], **kwargs) -> Dict[str, Any]:
        """Get basic market data for symbols."""
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        # Use snapshots endpoint for multiple symbols
        return await self.get_snapshots(symbols)
    
    async def get_gainers(self, limit: int = 50) -> pd.DataFrame:
        """
        Get top gaining stocks.
        
        Args:
            limit: Maximum number of results
            
        Returns:
            DataFrame with gainer data
        """
        try:
            response = await self.get(
                "/v2/snapshot/locale/us/markets/stocks/gainers",
                params={"limit": limit}
            )
            
            if "results" not in response:
                self.logger.warning("No results in gainers response")
                return pd.DataFrame()
            
            gainers_data = []
            for ticker_data in response["results"]:
                try:
                    ticker_info = ticker_data.get("value", {})
                    last_quote = ticker_data.get("lastQuote", {})
                    last_trade = ticker_data.get("lastTrade", {})
                    prev_day = ticker_data.get("prevDay", {})
                    
                    # Calculate gap percentage
                    current_price = last_trade.get("p") or last_quote.get("askPrice") or ticker_info.get("c")
                    prev_close = prev_day.get("c")
                    
                    if current_price and prev_close:
                        gap_pct = ((current_price - prev_close) / prev_close) * 100
                    else:
                        gap_pct = 0
                    
                    gainers_data.append({
                        "symbol": ticker_data.get("ticker", ""),
                        "price": current_price,
                        "prev_close": prev_close,
                        "gap_pct": gap_pct,
                        "volume": ticker_info.get("v", 0),
                        "change": ticker_info.get("c", 0),
                        "change_pct": ticker_info.get("cp", 0),
                        "high": ticker_info.get("h", 0),
                        "low": ticker_info.get("l", 0),
                        "open": ticker_info.get("o", 0),
                        "timestamp": ticker_info.get("t", 0),
                    })
                
                except Exception as e:
                    self.logger.warning(f"Error processing ticker data: {e}")
                    continue
            
            return pd.DataFrame(gainers_data)
        
        except Exception as e:
            self.logger.error(f"Error fetching gainers: {e}")
            return pd.DataFrame()
    
    async def get_losers(self, limit: int = 50) -> pd.DataFrame:
        """Get top losing stocks."""
        try:
            response = await self.get(
                "/v2/snapshot/locale/us/markets/stocks/losers",
                params={"limit": limit}
            )
            
            if "results" not in response:
                return pd.DataFrame()
            
            losers_data = []
            for ticker_data in response["results"]:
                try:
                    ticker_info = ticker_data.get("value", {})
                    losers_data.append({
                        "symbol": ticker_data.get("ticker", ""),
                        "price": ticker_info.get("c", 0),
                        "change": ticker_info.get("c", 0),
                        "change_pct": ticker_info.get("cp", 0),
                        "volume": ticker_info.get("v", 0),
                        "high": ticker_info.get("h", 0),
                        "low": ticker_info.get("l", 0),
                        "open": ticker_info.get("o", 0),
                    })
                except Exception as e:
                    self.logger.warning(f"Error processing loser data: {e}")
                    continue
            
            return pd.DataFrame(losers_data)
        
        except Exception as e:
            self.logger.error(f"Error fetching losers: {e}")
            return pd.DataFrame()
    
    async def get_snapshots(self, symbols: List[str]) -> Dict[str, Any]:
        """
        Get market snapshots for multiple symbols.
        
        Args:
            symbols: List of stock symbols
            
        Returns:
            Dictionary with snapshot data
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        try:
            # Use individual ticker snapshots for better reliability
            snapshots = {}
            for symbol in symbols[:5]:  # Limit to 5 symbols to respect rate limits
                try:
                    response = await self.get(f"/v2/snapshot/locale/us/markets/stocks/tickers/{symbol}")
                    if "results" in response and response["results"]:
                        result = response["results"][0] if isinstance(response["results"], list) else response["results"]
                        snapshots[symbol] = {
                            "price": result.get("lastTrade", {}).get("p"),
                            "volume": result.get("day", {}).get("v", 0),
                            "change": result.get("todaysChange", 0),
                            "change_pct": result.get("todaysChangePerc", 0),
                            "high": result.get("day", {}).get("h", 0),
                            "low": result.get("day", {}).get("l", 0),
                            "open": result.get("day", {}).get("o", 0),
                            "prev_close": result.get("prevDay", {}).get("c", 0),
                        }
                    await asyncio.sleep(0.2)  # Rate limiting
                except Exception as e:
                    self.logger.warning(f"Error getting snapshot for {symbol}: {e}")
                    continue

            return snapshots

        except Exception as e:
            self.logger.error(f"Error fetching snapshots: {e}")
            return {}
    
    async def get_tickers(
        self,
        market: str = "stocks",
        locale: str = "us",
        active: bool = True,
        limit: int = 1000
    ) -> List[Dict[str, Any]]:
        """
        Get list of tickers from Polygon.

        Args:
            market: Market type (stocks, crypto, fx)
            locale: Locale (us, global)
            active: Only active tickers
            limit: Maximum number of results

        Returns:
            List of ticker information
        """
        params = {
            "market": market,
            "locale": locale,
            "active": str(active).lower(),
            "limit": str(min(limit, 1000))  # API limit
        }

        try:
            response = await self.get("/v3/reference/tickers", params=params)
            return response.get("results", [])

        except Exception as e:
            self.logger.error(f"Error fetching tickers: {e}")
            return []

    async def get_ticker_details(self, symbol: str) -> Dict[str, Any]:
        """
        Get detailed information about a ticker.
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with ticker details
        """
        symbol = symbol.upper().strip()
        
        try:
            response = await self.get(f"/v3/reference/tickers/{symbol}")
            
            if "results" not in response:
                return {}
            
            result = response["results"]
            return {
                "symbol": result.get("ticker"),
                "name": result.get("name"),
                "market": result.get("market"),
                "locale": result.get("locale"),
                "primary_exchange": result.get("primary_exchange"),
                "type": result.get("type"),
                "active": result.get("active"),
                "currency_name": result.get("currency_name"),
                "cik": result.get("cik"),
                "composite_figi": result.get("composite_figi"),
                "share_class_figi": result.get("share_class_figi"),
                "market_cap": result.get("market_cap"),
                "phone_number": result.get("phone_number"),
                "address": result.get("address", {}),
                "description": result.get("description"),
                "sic_code": result.get("sic_code"),
                "sic_description": result.get("sic_description"),
                "ticker_root": result.get("ticker_root"),
                "homepage_url": result.get("homepage_url"),
                "total_employees": result.get("total_employees"),
                "list_date": result.get("list_date"),
                "branding": result.get("branding", {}),
                "share_class_shares_outstanding": result.get("share_class_shares_outstanding"),
                "weighted_shares_outstanding": result.get("weighted_shares_outstanding"),
            }
        
        except Exception as e:
            self.logger.error(f"Error fetching ticker details for {symbol}: {e}")
            return {}
    
    async def get_aggregates(
        self,
        symbol: str,
        multiplier: int = 1,
        timespan: str = "day",
        from_date: str = None,
        to_date: str = None,
        limit: int = 5000
    ) -> pd.DataFrame:
        """
        Get aggregate bars for a ticker.
        
        Args:
            symbol: Stock symbol
            multiplier: Size of the timespan multiplier
            timespan: Size of the time window (minute, hour, day, week, month, quarter, year)
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            limit: Maximum number of results
            
        Returns:
            DataFrame with OHLCV data
        """
        symbol = symbol.upper().strip()
        
        # Default date range if not provided
        if not from_date:
            from_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        if not to_date:
            to_date = datetime.now().strftime("%Y-%m-%d")
        
        try:
            response = await self.get(
                f"/v2/aggs/ticker/{symbol}/range/{multiplier}/{timespan}/{from_date}/{to_date}",
                params={"limit": limit, "sort": "asc"}
            )
            
            if "results" not in response:
                return pd.DataFrame()
            
            bars_data = []
            for bar in response["results"]:
                bars_data.append({
                    "timestamp": pd.to_datetime(bar["t"], unit="ms"),
                    "open": bar["o"],
                    "high": bar["h"],
                    "low": bar["l"],
                    "close": bar["c"],
                    "volume": bar["v"],
                    "vwap": bar.get("vw"),
                    "transactions": bar.get("n"),
                })
            
            df = pd.DataFrame(bars_data)
            if not df.empty:
                df.set_index("timestamp", inplace=True)
            
            return df
        
        except Exception as e:
            self.logger.error(f"Error fetching aggregates for {symbol}: {e}")
            return pd.DataFrame()
    
    async def get_news(
        self,
        ticker: Optional[str] = None,
        limit: int = 10,
        order: str = "desc"
    ) -> List[Dict[str, Any]]:
        """
        Get news articles.
        
        Args:
            ticker: Stock symbol to filter news (optional)
            limit: Maximum number of articles
            order: Sort order (asc or desc)
            
        Returns:
            List of news articles
        """
        params = {
            "limit": limit,
            "order": order,
        }
        
        if ticker:
            params["ticker"] = ticker.upper().strip()
        
        try:
            response = await self.get("/v2/reference/news", params=params)
            
            if "results" not in response:
                return []
            
            news_articles = []
            for article in response["results"]:
                news_articles.append({
                    "id": article.get("id"),
                    "publisher": article.get("publisher", {}).get("name"),
                    "title": article.get("title"),
                    "author": article.get("author"),
                    "published_utc": article.get("published_utc"),
                    "article_url": article.get("article_url"),
                    "tickers": article.get("tickers", []),
                    "amp_url": article.get("amp_url"),
                    "image_url": article.get("image_url"),
                    "description": article.get("description"),
                    "keywords": article.get("keywords", []),
                })
            
            return news_articles
        
        except Exception as e:
            self.logger.error(f"Error fetching news: {e}")
            return []
