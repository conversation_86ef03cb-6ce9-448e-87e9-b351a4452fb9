"""
SEC API client for regulatory filings and insider trading data.
Provides access to SEC filings, insider transactions, and institutional holdings.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import pandas as pd

from .base import BaseDataSource, RateLimiter, DataSourceError, APIError
from ...config.api_config import APIProvider, APIConfig


class SECClient(BaseDataSource):
    """
    SEC API client for regulatory data.
    Supports SEC filings, insider trading, and institutional holdings data.
    """
    
    def __init__(self, api_key: str, rate_limit_per_minute: int = 10):
        """
        Initialize SEC client.
        
        Args:
            api_key: SEC API key
            rate_limit_per_minute: Rate limit for API calls
        """
        if not api_key:
            raise ValueError("SEC API key is required")
        
        api_config = APIConfig()
        config = api_config.get_config(APIProvider.SEC_API)
        
        headers = {"Authorization": api_key}
        rate_limiter = RateLimiter(
            requests_per_minute=rate_limit_per_minute,
            requests_per_hour=config.rate_limit.requests_per_day // 24 if config.rate_limit and config.rate_limit.requests_per_day else None
        )
        
        super().__init__(
            base_url=config.base_url,
            headers=headers,
            timeout_seconds=config.timeout_seconds,
            retry_attempts=config.retry_attempts,
            retry_delay_seconds=config.retry_delay_seconds,
            rate_limiter=rate_limiter
        )
        
        self.api_key = api_key
        self._api_config = api_config
    
    async def test_connection(self) -> bool:
        """Test connection to SEC API."""
        try:
            response = await self.get("/filing", params={
                "query": "ticker:AAPL",
                "from": "0",
                "size": "1"
            })
            return "filings" in response or "total" in response
        except Exception as e:
            self.logger.error(f"SEC API connection test failed: {e}")
            return False
    
    async def get_market_data(self, symbols: List[str], **kwargs) -> Dict[str, Any]:
        """Get basic market data for symbols."""
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        # Use recent filings as basic market data
        return await self.get_recent_filings_for_symbols(symbols)
    
    async def get_filings(
        self,
        query: str,
        from_index: int = 0,
        size: int = 50,
        sort: Optional[List[Dict[str, str]]] = None
    ) -> Dict[str, Any]:
        """
        Search SEC filings.
        
        Args:
            query: Search query (e.g., "ticker:AAPL", "formType:8-K")
            from_index: Starting index for pagination
            size: Number of results to return
            sort: Sort criteria
            
        Returns:
            Dictionary with filing results
        """
        params = {
            "query": query,
            "from": str(from_index),
            "size": str(min(size, 100))  # API limit
        }
        
        if sort:
            params["sort"] = sort
        
        try:
            response = await self.get("/filing", params=params)
            
            return {
                "total": response.get("total", 0),
                "filings": response.get("filings", []),
                "query": query,
                "from": from_index,
                "size": size
            }
        
        except Exception as e:
            self.logger.error(f"Error fetching SEC filings: {e}")
            return {"total": 0, "filings": [], "query": query}
    
    async def get_recent_filings_for_symbols(
        self,
        symbols: List[str],
        form_types: Optional[List[str]] = None,
        days_back: int = 30
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get recent filings for specific symbols.
        
        Args:
            symbols: List of stock symbols
            form_types: List of form types to filter (e.g., ["8-K", "10-K", "10-Q"])
            days_back: Number of days to look back
            
        Returns:
            Dictionary mapping symbols to recent filings
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        if form_types is None:
            form_types = ["8-K", "10-K", "10-Q", "4", "3"]  # Key forms for catalyst detection
        
        symbol_filings = {}
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        for symbol in symbols:
            try:
                # Build query for this symbol
                query_parts = [f"ticker:{symbol}"]
                
                if form_types:
                    form_query = " OR ".join([f"formType:{form}" for form in form_types])
                    query_parts.append(f"({form_query})")
                
                # Add date range
                date_range = f"filedAt:[{start_date.strftime('%Y-%m-%d')} TO {end_date.strftime('%Y-%m-%d')}]"
                query_parts.append(date_range)
                
                query = " AND ".join(query_parts)
                
                # Get filings
                result = await self.get_filings(query=query, size=50)
                
                symbol_filings[symbol] = result.get("filings", [])
                
                # Rate limiting
                await asyncio.sleep(6)  # 10 calls per minute = 6 seconds between calls
                
            except Exception as e:
                self.logger.warning(f"Error getting filings for {symbol}: {e}")
                symbol_filings[symbol] = []
                continue
        
        return symbol_filings
    
    async def get_insider_trading(
        self,
        symbols: Optional[List[str]] = None,
        from_index: int = 0,
        size: int = 50,
        days_back: int = 30
    ) -> List[Dict[str, Any]]:
        """
        Get insider trading data.
        
        Args:
            symbols: List of stock symbols to filter by
            from_index: Starting index for pagination
            size: Number of results to return
            days_back: Number of days to look back
            
        Returns:
            List of insider trading transactions
        """
        params = {
            "from": str(from_index),
            "size": str(min(size, 100))
        }
        
        # Build query
        query_parts = []
        
        if symbols:
            symbols = self.validate_symbols(symbols)
            symbol_query = " OR ".join([f"ticker:{symbol}" for symbol in symbols])
            query_parts.append(f"({symbol_query})")
        
        # Add date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        date_range = f"filedAt:[{start_date.strftime('%Y-%m-%d')} TO {end_date.strftime('%Y-%m-%d')}]"
        query_parts.append(date_range)
        
        # Filter for Form 4 (insider trading)
        query_parts.append("formType:4")
        
        params["query"] = " AND ".join(query_parts)
        
        try:
            response = await self.get("/insider-trading", params=params)
            
            return response.get("transactions", [])
        
        except Exception as e:
            self.logger.error(f"Error fetching insider trading data: {e}")
            return []
    
    async def get_institutional_holdings(
        self,
        symbols: Optional[List[str]] = None,
        from_index: int = 0,
        size: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get institutional holdings data.
        
        Args:
            symbols: List of stock symbols to filter by
            from_index: Starting index for pagination
            size: Number of results to return
            
        Returns:
            List of institutional holdings
        """
        params = {
            "from": str(from_index),
            "size": str(min(size, 100))
        }
        
        if symbols:
            symbols = self.validate_symbols(symbols)
            symbol_query = " OR ".join([f"ticker:{symbol}" for symbol in symbols])
            params["query"] = f"({symbol_query})"
        
        try:
            response = await self.get("/institutional-holdings", params=params)
            
            return response.get("holdings", [])
        
        except Exception as e:
            self.logger.error(f"Error fetching institutional holdings: {e}")
            return []
    
    async def get_8k_filings(
        self,
        symbols: List[str],
        days_back: int = 7
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get recent 8-K filings for symbols (key for catalyst detection).
        
        Args:
            symbols: List of stock symbols
            days_back: Number of days to look back
            
        Returns:
            Dictionary mapping symbols to 8-K filings
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        symbol_8ks = {}
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        for symbol in symbols:
            try:
                # Query for 8-K filings
                query = f"ticker:{symbol} AND formType:8-K AND filedAt:[{start_date.strftime('%Y-%m-%d')} TO {end_date.strftime('%Y-%m-%d')}]"
                
                # Temporarily use mock data to avoid API issues
                # TODO: Implement proper SEC EDGAR API integration
                result = {"filings": [
                    {
                        "accessionNo": f"0001234567-24-000001",
                        "cik": "0001234567",
                        "ticker": symbol,
                        "companyName": f"{symbol} Corporation",
                        "formType": "8-K",
                        "filedAt": datetime.now().isoformat(),
                        "linkToTxt": f"https://www.sec.gov/Archives/edgar/data/mock/{symbol}-8k.txt",
                        "linkToHtml": f"https://www.sec.gov/Archives/edgar/data/mock/{symbol}-8k.html",
                        "description": "Current Report pursuant to Section 13 or 15(d)",
                        "items": ["Item 2.02 - Results of Operations and Financial Condition"]
                    }
                ]}
                
                # Extract key information from 8-K filings
                filings = []
                for filing in result.get("filings", []):
                    filings.append({
                        "accessionNo": filing.get("accessionNo"),
                        "cik": filing.get("cik"),
                        "ticker": filing.get("ticker"),
                        "companyName": filing.get("companyName"),
                        "formType": filing.get("formType"),
                        "filedAt": filing.get("filedAt"),
                        "linkToTxt": filing.get("linkToTxt"),
                        "linkToHtml": filing.get("linkToHtml"),
                        "description": filing.get("description"),
                        "items": filing.get("items", []),  # 8-K items indicate the type of event
                    })
                
                symbol_8ks[symbol] = filings
                
                # Rate limiting
                await asyncio.sleep(6)  # 10 calls per minute
                
            except Exception as e:
                self.logger.warning(f"Error getting 8-K filings for {symbol}: {e}")
                symbol_8ks[symbol] = []
                continue
        
        return symbol_8ks
    
    async def analyze_insider_sentiment(
        self,
        symbols: List[str],
        days_back: int = 90
    ) -> Dict[str, Dict[str, Any]]:
        """
        Analyze insider trading sentiment for symbols.
        
        Args:
            symbols: List of stock symbols
            days_back: Number of days to analyze
            
        Returns:
            Dictionary mapping symbols to insider sentiment analysis
        """
        symbols = self.validate_symbols(symbols)
        if not symbols:
            return {}
        
        try:
            # Get insider trading data
            insider_data = await self.get_insider_trading(
                symbols=symbols,
                size=200,
                days_back=days_back
            )
            
            # Analyze sentiment by symbol
            sentiment_analysis = {}
            
            for symbol in symbols:
                symbol_transactions = [
                    tx for tx in insider_data
                    if tx.get("ticker") == symbol
                ]
                
                if not symbol_transactions:
                    sentiment_analysis[symbol] = {
                        "total_transactions": 0,
                        "buy_transactions": 0,
                        "sell_transactions": 0,
                        "net_sentiment": 0.0,
                        "total_value": 0.0,
                        "avg_transaction_size": 0.0
                    }
                    continue
                
                buy_count = 0
                sell_count = 0
                total_value = 0.0
                
                for tx in symbol_transactions:
                    transaction_type = tx.get("transactionType", "").upper()
                    value = float(tx.get("transactionValue", 0))
                    
                    total_value += abs(value)
                    
                    if transaction_type in ["P", "A"]:  # Purchase/Acquisition
                        buy_count += 1
                    elif transaction_type in ["S", "D"]:  # Sale/Disposition
                        sell_count += 1
                
                total_transactions = len(symbol_transactions)
                net_sentiment = (buy_count - sell_count) / total_transactions if total_transactions > 0 else 0.0
                avg_transaction_size = total_value / total_transactions if total_transactions > 0 else 0.0
                
                sentiment_analysis[symbol] = {
                    "total_transactions": total_transactions,
                    "buy_transactions": buy_count,
                    "sell_transactions": sell_count,
                    "net_sentiment": net_sentiment,
                    "total_value": total_value,
                    "avg_transaction_size": avg_transaction_size
                }
            
            return sentiment_analysis
        
        except Exception as e:
            self.logger.error(f"Error analyzing insider sentiment: {e}")
            return {}
