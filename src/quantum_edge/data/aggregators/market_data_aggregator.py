"""
Market Data Aggregator for QuantumEdge system.
Combines data from multiple sources into unified datasets.
"""

import asyncio
from typing import Dict, List, Optional, Any, Union
import pandas as pd
from datetime import datetime, timedelta
import logging

from ..sources.polygon_client import PolygonClient
from ..sources.alpaca_client import AlpacaClient
from ..sources.alpha_vantage_client import AlphaVantageClient
from ..sources.benzinga_client import BenzingaClient
from ..sources.sec_client import SECClient
from ..sources.patents_client import PatentsClient
from ...config.logging_config import LoggerMixin


class MarketDataAggregator(LoggerMixin):
    """
    Aggregates market data from multiple sources.
    Provides unified interface for accessing market data across different APIs.
    """
    
    def __init__(
        self,
        polygon_client: Optional[PolygonClient] = None,
        alpaca_client: Optional[AlpacaClient] = None,
        alpha_vantage_client: Optional[AlphaVantageClient] = None,
        benzinga_client: Optional[BenzingaClient] = None,
        sec_client: Optional[SECClient] = None,
        patents_client: Optional[PatentsClient] = None
    ):
        """
        Initialize market data aggregator.

        Args:
            polygon_client: Polygon.io client instance
            alpaca_client: Alpaca Markets client instance
            alpha_vantage_client: Alpha Vantage client instance
            benzinga_client: Benzinga client instance
            sec_client: SEC API client instance
            patents_client: Patents API client instance
        """
        self.polygon_client = polygon_client
        self.alpaca_client = alpaca_client
        self.alpha_vantage_client = alpha_vantage_client
        self.benzinga_client = benzinga_client
        self.sec_client = sec_client
        self.patents_client = patents_client

        # Track which sources are available
        self.available_sources = []
        if self.polygon_client:
            self.available_sources.append("polygon")
        if self.alpaca_client:
            self.available_sources.append("alpaca")
        if self.alpha_vantage_client:
            self.available_sources.append("alpha_vantage")
        if self.benzinga_client:
            self.available_sources.append("benzinga")
        if self.sec_client:
            self.available_sources.append("sec")
        if self.patents_client:
            self.available_sources.append("patents")
        
        self.logger.info(f"MarketDataAggregator initialized with sources: {self.available_sources}")
    
    async def get_premarket_gappers(
        self,
        gap_threshold: float = 5.0,
        limit: int = 50,
        max_price: float = 10.0
    ) -> pd.DataFrame:
        """
        Get pre-market gapping stocks from available sources.
        
        Args:
            gap_threshold: Minimum gap percentage
            limit: Maximum number of results
            max_price: Maximum stock price
            
        Returns:
            DataFrame with gapper data
        """
        if not self.polygon_client:
            self.logger.warning("No Polygon client available for gappers data")
            return pd.DataFrame()
        
        try:
            # Get gainers from Polygon
            gainers = await self.polygon_client.get_gainers(limit=limit * 2)  # Get more to filter
            
            if gainers.empty:
                return pd.DataFrame()
            
            # Filter by gap threshold and price
            filtered_gainers = gainers[
                (gainers['gap_pct'] >= gap_threshold) & 
                (gainers['price'] <= max_price) &
                (gainers['price'] > 0)
            ].head(limit)
            
            self.logger.info(f"Found {len(filtered_gainers)} gappers meeting criteria")
            return filtered_gainers
            
        except Exception as e:
            self.logger.error(f"Error getting premarket gappers: {e}")
            return pd.DataFrame()
    
    async def enrich_with_fundamentals(
        self,
        symbols: List[str],
        include_assets: bool = True,
        include_details: bool = True
    ) -> Dict[str, Dict[str, Any]]:
        """
        Enrich symbols with fundamental data from multiple sources.
        
        Args:
            symbols: List of stock symbols
            include_assets: Include asset information from Alpaca
            include_details: Include detailed ticker information from Polygon
            
        Returns:
            Dictionary mapping symbols to fundamental data
        """
        if not symbols:
            return {}
        
        enriched_data = {}
        
        # Initialize data structure for each symbol
        for symbol in symbols:
            enriched_data[symbol] = {
                'symbol': symbol,
                'polygon_details': {},
                'alpaca_asset': {},
                'data_sources': []
            }
        
        # Get asset information from Alpaca
        if include_assets and self.alpaca_client:
            try:
                assets = await self.alpaca_client.get_assets(symbols)
                for asset in assets:
                    symbol = asset.get('symbol')
                    if symbol in enriched_data:
                        enriched_data[symbol]['alpaca_asset'] = asset
                        enriched_data[symbol]['data_sources'].append('alpaca_assets')
                        
                        # Extract key fundamental metrics
                        enriched_data[symbol].update({
                            'name': asset.get('name'),
                            'exchange': asset.get('exchange'),
                            'tradable': asset.get('tradable'),
                            'marginable': asset.get('marginable'),
                            'shortable': asset.get('shortable'),
                            'easy_to_borrow': asset.get('easy_to_borrow'),
                            'fractionable': asset.get('fractionable'),
                        })
                
                self.logger.info(f"Enriched {len(assets)} symbols with Alpaca asset data")
                
            except Exception as e:
                self.logger.error(f"Error getting Alpaca assets: {e}")
        
        # Get detailed ticker information from Polygon
        if include_details and self.polygon_client:
            try:
                # Process symbols in batches to respect rate limits
                batch_size = 5
                for i in range(0, len(symbols), batch_size):
                    batch = symbols[i:i + batch_size]
                    
                    # Get details for each symbol in batch
                    tasks = [
                        self.polygon_client.get_ticker_details(symbol)
                        for symbol in batch
                    ]
                    
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    
                    for symbol, result in zip(batch, results):
                        if isinstance(result, Exception):
                            self.logger.warning(f"Error getting details for {symbol}: {result}")
                            continue
                        
                        if result and symbol in enriched_data:
                            enriched_data[symbol]['polygon_details'] = result
                            enriched_data[symbol]['data_sources'].append('polygon_details')
                            
                            # Extract key fundamental metrics
                            enriched_data[symbol].update({
                                'market_cap': result.get('market_cap'),
                                'shares_outstanding': result.get('weighted_shares_outstanding'),
                                'description': result.get('description'),
                                'homepage_url': result.get('homepage_url'),
                                'total_employees': result.get('total_employees'),
                                'sic_code': result.get('sic_code'),
                                'sic_description': result.get('sic_description'),
                            })
                    
                    # Small delay between batches
                    if i + batch_size < len(symbols):
                        await asyncio.sleep(0.2)
                
                self.logger.info(f"Enriched symbols with Polygon ticker details")
                
            except Exception as e:
                self.logger.error(f"Error getting Polygon ticker details: {e}")
        
        return enriched_data
    
    async def get_market_snapshots(
        self,
        symbols: List[str],
        include_quotes: bool = True,
        include_trades: bool = True
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get market snapshots from multiple sources.
        
        Args:
            symbols: List of stock symbols
            include_quotes: Include quote data
            include_trades: Include trade data
            
        Returns:
            Dictionary mapping symbols to snapshot data
        """
        if not symbols:
            return {}
        
        snapshots = {}
        
        # Initialize data structure
        for symbol in symbols:
            snapshots[symbol] = {
                'symbol': symbol,
                'polygon_snapshot': {},
                'alpaca_snapshot': {},
                'data_sources': []
            }
        
        # Get snapshots from Polygon
        if self.polygon_client:
            try:
                polygon_snapshots = await self.polygon_client.get_snapshots(symbols)
                for symbol, snapshot in polygon_snapshots.items():
                    if symbol in snapshots:
                        snapshots[symbol]['polygon_snapshot'] = snapshot
                        snapshots[symbol]['data_sources'].append('polygon_snapshot')
                        
                        # Extract unified snapshot data
                        snapshots[symbol].update({
                            'price': snapshot.get('price'),
                            'volume': snapshot.get('volume'),
                            'change': snapshot.get('change'),
                            'change_pct': snapshot.get('change_pct'),
                            'high': snapshot.get('high'),
                            'low': snapshot.get('low'),
                            'open': snapshot.get('open'),
                            'prev_close': snapshot.get('prev_close'),
                        })
                
                self.logger.info(f"Got Polygon snapshots for {len(polygon_snapshots)} symbols")
                
            except Exception as e:
                self.logger.error(f"Error getting Polygon snapshots: {e}")
        
        # Get snapshots from Alpaca
        if self.alpaca_client:
            try:
                alpaca_snapshots = await self.alpaca_client.get_snapshots(symbols)
                for symbol, snapshot in alpaca_snapshots.items():
                    if symbol in snapshots:
                        snapshots[symbol]['alpaca_snapshot'] = snapshot
                        snapshots[symbol]['data_sources'].append('alpaca_snapshot')
                        
                        # Merge with existing data (prefer Polygon for price data)
                        if not snapshots[symbol].get('price'):
                            latest_trade = snapshot.get('latest_trade', {})
                            daily_bar = snapshot.get('daily_bar', {})
                            
                            snapshots[symbol].update({
                                'price': latest_trade.get('price') or daily_bar.get('close'),
                                'volume': daily_bar.get('volume'),
                                'high': daily_bar.get('high'),
                                'low': daily_bar.get('low'),
                                'open': daily_bar.get('open'),
                            })
                
                self.logger.info(f"Got Alpaca snapshots for {len(alpaca_snapshots)} symbols")
                
            except Exception as e:
                self.logger.error(f"Error getting Alpaca snapshots: {e}")
        
        return snapshots
    
    async def get_historical_data(
        self,
        symbols: List[str],
        timeframe: str = "1Day",
        days_back: int = 30
    ) -> Dict[str, pd.DataFrame]:
        """
        Get historical data from available sources.
        
        Args:
            symbols: List of stock symbols
            timeframe: Data timeframe (1Min, 5Min, 15Min, 1Hour, 1Day)
            days_back: Number of days of historical data
            
        Returns:
            Dictionary mapping symbols to historical DataFrames
        """
        if not symbols:
            return {}
        
        historical_data = {}
        
        # Use Alpaca for historical bars (more reliable for historical data)
        if self.alpaca_client:
            try:
                start_date = (datetime.now() - timedelta(days=days_back)).isoformat() + "Z"
                end_date = datetime.now().isoformat() + "Z"
                
                bars_data = await self.alpaca_client.get_bars(
                    symbols=symbols,
                    timeframe=timeframe,
                    start=start_date,
                    end=end_date
                )
                
                historical_data.update(bars_data)
                self.logger.info(f"Got historical data for {len(bars_data)} symbols from Alpaca")
                
            except Exception as e:
                self.logger.error(f"Error getting historical data from Alpaca: {e}")
        
        # Fallback to Polygon if Alpaca not available or failed
        if not historical_data and self.polygon_client:
            try:
                from_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
                to_date = datetime.now().strftime("%Y-%m-%d")
                
                # Convert timeframe to Polygon format
                polygon_timeframe = "day"  # Default
                multiplier = 1
                
                if timeframe == "1Min":
                    polygon_timeframe = "minute"
                    multiplier = 1
                elif timeframe == "5Min":
                    polygon_timeframe = "minute"
                    multiplier = 5
                elif timeframe == "15Min":
                    polygon_timeframe = "minute"
                    multiplier = 15
                elif timeframe == "1Hour":
                    polygon_timeframe = "hour"
                    multiplier = 1
                
                # Process symbols individually for Polygon
                for symbol in symbols:
                    try:
                        bars = await self.polygon_client.get_aggregates(
                            symbol=symbol,
                            multiplier=multiplier,
                            timespan=polygon_timeframe,
                            from_date=from_date,
                            to_date=to_date
                        )
                        
                        if not bars.empty:
                            historical_data[symbol] = bars
                        
                        # Small delay between requests
                        await asyncio.sleep(0.1)
                        
                    except Exception as e:
                        self.logger.warning(f"Error getting historical data for {symbol}: {e}")
                
                self.logger.info(f"Got historical data for {len(historical_data)} symbols from Polygon")
                
            except Exception as e:
                self.logger.error(f"Error getting historical data from Polygon: {e}")
        
        return historical_data

    async def get_comprehensive_analysis(
        self,
        symbols: List[str],
        include_fundamentals: bool = True,
        include_sentiment: bool = True,
        include_insider_data: bool = True,
        include_patents: bool = True
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get comprehensive analysis combining all data sources.

        Args:
            symbols: List of stock symbols
            include_fundamentals: Include Alpha Vantage fundamental data
            include_sentiment: Include Benzinga sentiment analysis
            include_insider_data: Include SEC insider trading data
            include_patents: Include patent innovation data

        Returns:
            Dictionary with comprehensive analysis for each symbol
        """
        if not symbols:
            return {}

        comprehensive_data = {}

        # Initialize data structure
        for symbol in symbols:
            comprehensive_data[symbol] = {
                'symbol': symbol,
                'fundamental_data': {},
                'sentiment_data': {},
                'insider_data': {},
                'patent_data': {},
                'data_sources': [],
                'analysis_timestamp': datetime.now().isoformat()
            }

        # Get fundamental data from Alpha Vantage
        if include_fundamentals and self.alpha_vantage_client:
            try:
                self.logger.info("Fetching fundamental data from Alpha Vantage...")
                fundamentals = await self.alpha_vantage_client.get_company_overviews(symbols)

                for symbol, data in fundamentals.items():
                    if symbol in comprehensive_data:
                        comprehensive_data[symbol]['fundamental_data'] = data
                        comprehensive_data[symbol]['data_sources'].append('alpha_vantage')

                self.logger.info(f"Retrieved fundamental data for {len(fundamentals)} symbols")

            except Exception as e:
                self.logger.error(f"Error fetching fundamental data: {e}")

        # Get sentiment data from Benzinga
        if include_sentiment and self.benzinga_client:
            try:
                self.logger.info("Fetching sentiment data from Benzinga...")
                sentiment_data = await self.benzinga_client.get_sentiment_analysis(symbols)

                for symbol, data in sentiment_data.items():
                    if symbol in comprehensive_data:
                        comprehensive_data[symbol]['sentiment_data'] = data
                        comprehensive_data[symbol]['data_sources'].append('benzinga')

                self.logger.info(f"Retrieved sentiment data for {len(sentiment_data)} symbols")

            except Exception as e:
                self.logger.error(f"Error fetching sentiment data: {e}")

        # Get insider trading data from SEC
        if include_insider_data and self.sec_client:
            try:
                self.logger.info("Fetching insider trading data from SEC...")
                insider_sentiment = await self.sec_client.analyze_insider_sentiment(symbols)

                for symbol, data in insider_sentiment.items():
                    if symbol in comprehensive_data:
                        comprehensive_data[symbol]['insider_data'] = data
                        comprehensive_data[symbol]['data_sources'].append('sec')

                self.logger.info(f"Retrieved insider data for {len(insider_sentiment)} symbols")

            except Exception as e:
                self.logger.error(f"Error fetching insider data: {e}")

        # Get patent data
        if include_patents and self.patents_client:
            try:
                self.logger.info("Fetching patent data...")
                patent_data = await self.patents_client.get_company_patents(symbols)

                for symbol, data in patent_data.items():
                    if symbol in comprehensive_data:
                        comprehensive_data[symbol]['patent_data'] = {
                            'recent_patents': data,
                            'patent_count': len(data),
                            'innovation_score': min(len(data) * 10, 100)  # Normalized score
                        }
                        comprehensive_data[symbol]['data_sources'].append('patents')

                self.logger.info(f"Retrieved patent data for {len(patent_data)} symbols")

            except Exception as e:
                self.logger.error(f"Error fetching patent data: {e}")

        return comprehensive_data

    async def get_catalyst_analysis(
        self,
        symbols: List[str],
        days_back: int = 30
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get catalyst analysis combining news, SEC filings, and earnings.

        Args:
            symbols: List of stock symbols
            days_back: Number of days to analyze

        Returns:
            Dictionary with catalyst analysis for each symbol
        """
        if not symbols:
            return {}

        catalyst_data = {}

        # Initialize data structure
        for symbol in symbols:
            catalyst_data[symbol] = {
                'symbol': symbol,
                'recent_news': [],
                'sec_filings': [],
                'earnings_events': [],
                'analyst_ratings': [],
                'catalyst_score': 0.0,
                'data_sources': []
            }

        # Get recent news from Benzinga
        if self.benzinga_client:
            try:
                date_from = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
                date_to = datetime.now().strftime("%Y-%m-%d")

                news_data = await self.benzinga_client.get_news(
                    symbols=symbols,
                    date_from=date_from,
                    date_to=date_to,
                    limit=100
                )

                # Group news by symbol
                for article in news_data:
                    article_symbols = [stock.get("name") for stock in article.get("stocks", [])]
                    for symbol in symbols:
                        if symbol in article_symbols and symbol in catalyst_data:
                            catalyst_data[symbol]['recent_news'].append(article)
                            if 'benzinga_news' not in catalyst_data[symbol]['data_sources']:
                                catalyst_data[symbol]['data_sources'].append('benzinga_news')

                # Get analyst ratings
                ratings_data = await self.benzinga_client.get_analyst_ratings(
                    symbols=symbols,
                    date_from=date_from,
                    date_to=date_to
                )

                for rating in ratings_data:
                    symbol = rating.get("ticker")
                    if symbol in catalyst_data:
                        catalyst_data[symbol]['analyst_ratings'].append(rating)
                        if 'benzinga_ratings' not in catalyst_data[symbol]['data_sources']:
                            catalyst_data[symbol]['data_sources'].append('benzinga_ratings')

                # Get earnings events
                earnings_data = await self.benzinga_client.get_earnings_calendar(
                    symbols=symbols,
                    date_from=date_from,
                    date_to=date_to
                )

                for earning in earnings_data:
                    symbol = earning.get("ticker")
                    if symbol in catalyst_data:
                        catalyst_data[symbol]['earnings_events'].append(earning)
                        if 'benzinga_earnings' not in catalyst_data[symbol]['data_sources']:
                            catalyst_data[symbol]['data_sources'].append('benzinga_earnings')

            except Exception as e:
                self.logger.error(f"Error fetching Benzinga catalyst data: {e}")

        # Get SEC filings
        if self.sec_client:
            try:
                sec_filings = await self.sec_client.get_8k_filings(symbols, days_back=days_back)

                for symbol, filings in sec_filings.items():
                    if symbol in catalyst_data:
                        catalyst_data[symbol]['sec_filings'] = filings
                        if filings:
                            catalyst_data[symbol]['data_sources'].append('sec_filings')

            except Exception as e:
                self.logger.error(f"Error fetching SEC filings: {e}")

        # Calculate catalyst scores
        for symbol, data in catalyst_data.items():
            score = 0.0

            # News impact (0-30 points)
            news_count = len(data['recent_news'])
            score += min(news_count * 3, 30)

            # SEC filings impact (0-25 points)
            filings_count = len(data['sec_filings'])
            score += min(filings_count * 5, 25)

            # Analyst ratings impact (0-25 points)
            ratings_count = len(data['analyst_ratings'])
            score += min(ratings_count * 5, 25)

            # Earnings events impact (0-20 points)
            earnings_count = len(data['earnings_events'])
            score += min(earnings_count * 10, 20)

            data['catalyst_score'] = score

        return catalyst_data

    async def get_innovation_analysis(
        self,
        sectors: Optional[Dict[str, List[str]]] = None,
        days_back: int = 365
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get innovation analysis using patent data.

        Args:
            sectors: Dictionary mapping sector names to keyword lists
            days_back: Number of days to analyze

        Returns:
            Dictionary with innovation analysis by sector
        """
        if not self.patents_client:
            self.logger.warning("Patents client not available for innovation analysis")
            return {}

        if sectors is None:
            sectors = {
                "AI/ML": ["artificial intelligence", "machine learning", "neural network", "deep learning"],
                "Quantum": ["quantum computing", "quantum algorithm", "qubit", "quantum supremacy"],
                "Biotech": ["gene therapy", "CRISPR", "immunotherapy", "biomarker"],
                "Clean Energy": ["solar cell", "battery technology", "energy storage", "renewable energy"],
                "Space Tech": ["satellite", "space technology", "rocket propulsion", "orbital"],
                "Cybersecurity": ["cybersecurity", "encryption", "blockchain", "zero trust"]
            }

        try:
            innovation_data = await self.patents_client.analyze_innovation_trends(
                sectors=sectors,
                days_back=days_back
            )

            return innovation_data

        except Exception as e:
            self.logger.error(f"Error performing innovation analysis: {e}")
            return {}

    async def close(self):
        """Close all client connections."""
        tasks = []

        if self.polygon_client:
            tasks.append(self.polygon_client.close())

        if self.alpaca_client:
            tasks.append(self.alpaca_client.close())

        if self.alpha_vantage_client:
            tasks.append(self.alpha_vantage_client.close())

        if self.benzinga_client:
            tasks.append(self.benzinga_client.close())

        if self.sec_client:
            tasks.append(self.sec_client.close())

        if self.patents_client:
            tasks.append(self.patents_client.close())

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)

        self.logger.info("MarketDataAggregator connections closed")

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
