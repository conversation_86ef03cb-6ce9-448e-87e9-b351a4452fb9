"""
Base model interface for QuantumEdge prediction system.
Provides abstract base class for all machine learning models.
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Tuple
import numpy as np
import pandas as pd
from datetime import datetime
import pickle
import joblib
from pathlib import Path

from ..config.logging_config import LoggerMixin


class ModelError(Exception):
    """Base exception for model-related errors."""
    pass


class TrainingError(ModelError):
    """Exception raised during model training."""
    pass


class PredictionError(ModelError):
    """Exception raised during model prediction."""
    pass


class BaseModel(ABC, LoggerMixin):
    """
    Abstract base class for all prediction models.
    Provides common functionality for training, prediction, and model management.
    """
    
    def __init__(
        self,
        model_name: str,
        model_type: str = "classifier",
        feature_columns: Optional[List[str]] = None,
        target_column: str = "target",
        model_params: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize base model.
        
        Args:
            model_name: Name of the model
            model_type: Type of model (classifier, regressor, quantum)
            feature_columns: List of feature column names
            target_column: Name of target column
            model_params: Model-specific parameters
        """
        self.model_name = model_name
        self.model_type = model_type
        self.feature_columns = feature_columns or []
        self.target_column = target_column
        self.model_params = model_params or {}
        
        # Model state
        self.model = None
        self.is_trained = False
        self.training_history = []
        self.feature_importance = {}
        self.model_metrics = {}
        
        # Metadata
        self.created_at = datetime.now()
        self.last_trained_at = None
        self.version = "1.0.0"
        
        self.logger.info(f"Initialized {model_name} model ({model_type})")
    
    @abstractmethod
    async def train(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        validation_data: Optional[Tuple[pd.DataFrame, pd.Series]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Train the model.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Optional validation data
            **kwargs: Additional training parameters
            
        Returns:
            Dictionary with training results and metrics
        """
        pass
    
    @abstractmethod
    async def predict(
        self,
        X: pd.DataFrame,
        return_probabilities: bool = False,
        **kwargs
    ) -> Union[np.ndarray, Tuple[np.ndarray, np.ndarray]]:
        """
        Make predictions.
        
        Args:
            X: Features for prediction
            return_probabilities: Whether to return prediction probabilities
            **kwargs: Additional prediction parameters
            
        Returns:
            Predictions, optionally with probabilities
        """
        pass
    
    @abstractmethod
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance scores.
        
        Returns:
            Dictionary mapping feature names to importance scores
        """
        pass
    
    def validate_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Validate and prepare features for model input.
        
        Args:
            X: Input features
            
        Returns:
            Validated and prepared features
            
        Raises:
            ModelError: If features are invalid
        """
        if self.feature_columns:
            missing_features = set(self.feature_columns) - set(X.columns)
            if missing_features:
                raise ModelError(f"Missing required features: {missing_features}")
            
            # Select and reorder features
            X = X[self.feature_columns].copy()
        
        # Check for missing values
        if X.isnull().any().any():
            self.logger.warning("Found missing values in features, filling with median")
            X = X.fillna(X.median())
        
        # Check for infinite values
        if np.isinf(X.select_dtypes(include=[np.number])).any().any():
            self.logger.warning("Found infinite values in features, replacing with median")
            numeric_cols = X.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                X.loc[np.isinf(X[col]), col] = X[col].median()
        
        return X
    
    def validate_targets(self, y: pd.Series) -> pd.Series:
        """
        Validate and prepare targets for model training.
        
        Args:
            y: Input targets
            
        Returns:
            Validated targets
            
        Raises:
            ModelError: If targets are invalid
        """
        if y.isnull().any():
            raise ModelError("Target values cannot contain missing values")
        
        if self.model_type == "classifier":
            # Ensure targets are categorical
            if not pd.api.types.is_categorical_dtype(y) and not pd.api.types.is_integer_dtype(y):
                self.logger.warning("Converting targets to categorical for classification")
                y = pd.Categorical(y)
        
        return y
    
    async def evaluate(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        metrics: Optional[List[str]] = None
    ) -> Dict[str, float]:
        """
        Evaluate model performance.
        
        Args:
            X: Features for evaluation
            y: True targets
            metrics: List of metrics to compute
            
        Returns:
            Dictionary with evaluation metrics
        """
        if not self.is_trained:
            raise ModelError("Model must be trained before evaluation")
        
        # Make predictions
        predictions = await self.predict(X)
        
        # Compute metrics
        if metrics is None:
            if self.model_type == "classifier":
                metrics = ["accuracy", "precision", "recall", "f1"]
            else:
                metrics = ["mse", "mae", "r2"]
        
        results = {}
        
        try:
            from sklearn.metrics import (
                accuracy_score, precision_score, recall_score, f1_score,
                mean_squared_error, mean_absolute_error, r2_score
            )
            
            if self.model_type == "classifier":
                if "accuracy" in metrics:
                    results["accuracy"] = accuracy_score(y, predictions)
                if "precision" in metrics:
                    results["precision"] = precision_score(y, predictions, average="weighted", zero_division=0)
                if "recall" in metrics:
                    results["recall"] = recall_score(y, predictions, average="weighted", zero_division=0)
                if "f1" in metrics:
                    results["f1"] = f1_score(y, predictions, average="weighted", zero_division=0)
            else:
                if "mse" in metrics:
                    results["mse"] = mean_squared_error(y, predictions)
                if "mae" in metrics:
                    results["mae"] = mean_absolute_error(y, predictions)
                if "r2" in metrics:
                    results["r2"] = r2_score(y, predictions)
        
        except ImportError:
            self.logger.warning("scikit-learn not available for metric computation")
        
        return results
    
    def save_model(self, filepath: str) -> None:
        """
        Save model to disk.
        
        Args:
            filepath: Path to save the model
        """
        if not self.is_trained:
            raise ModelError("Cannot save untrained model")
        
        # Create directory if it doesn't exist
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        # Prepare model data
        model_data = {
            "model": self.model,
            "model_name": self.model_name,
            "model_type": self.model_type,
            "feature_columns": self.feature_columns,
            "target_column": self.target_column,
            "model_params": self.model_params,
            "is_trained": self.is_trained,
            "training_history": self.training_history,
            "feature_importance": self.feature_importance,
            "model_metrics": self.model_metrics,
            "created_at": self.created_at,
            "last_trained_at": self.last_trained_at,
            "version": self.version
        }
        
        # Save using joblib for better sklearn compatibility
        try:
            joblib.dump(model_data, filepath)
            self.logger.info(f"Model saved to {filepath}")
        except Exception as e:
            # Fallback to pickle
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            self.logger.info(f"Model saved to {filepath} (using pickle)")
    
    def load_model(self, filepath: str) -> None:
        """
        Load model from disk.
        
        Args:
            filepath: Path to load the model from
        """
        try:
            # Try joblib first
            model_data = joblib.load(filepath)
        except:
            # Fallback to pickle
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
        
        # Restore model state
        self.model = model_data["model"]
        self.model_name = model_data["model_name"]
        self.model_type = model_data["model_type"]
        self.feature_columns = model_data["feature_columns"]
        self.target_column = model_data["target_column"]
        self.model_params = model_data["model_params"]
        self.is_trained = model_data["is_trained"]
        self.training_history = model_data["training_history"]
        self.feature_importance = model_data["feature_importance"]
        self.model_metrics = model_data["model_metrics"]
        self.created_at = model_data["created_at"]
        self.last_trained_at = model_data["last_trained_at"]
        self.version = model_data["version"]
        
        self.logger.info(f"Model loaded from {filepath}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get comprehensive model information.
        
        Returns:
            Dictionary with model metadata and performance
        """
        return {
            "model_name": self.model_name,
            "model_type": self.model_type,
            "is_trained": self.is_trained,
            "feature_count": len(self.feature_columns),
            "feature_columns": self.feature_columns,
            "target_column": self.target_column,
            "model_params": self.model_params,
            "training_history": self.training_history,
            "feature_importance": self.feature_importance,
            "model_metrics": self.model_metrics,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_trained_at": self.last_trained_at.isoformat() if self.last_trained_at else None,
            "version": self.version
        }
    
    def __repr__(self) -> str:
        """String representation of the model."""
        status = "trained" if self.is_trained else "untrained"
        return f"{self.__class__.__name__}(name='{self.model_name}', type='{self.model_type}', status='{status}')"
