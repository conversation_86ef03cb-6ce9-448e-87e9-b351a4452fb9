"""
Quantum Feature Engineering for QuantumEdge system.
Uses quantum computing principles to create enhanced features for stock prediction.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union, Tuple
import numpy as np
import pandas as pd
from datetime import datetime
import warnings

from ...config.logging_config import LoggerMixin


class QuantumFeatureEngineer(LoggerMixin):
    """
    Quantum-inspired feature engineering for financial data.
    Creates enhanced features using quantum computing principles.
    """
    
    def __init__(
        self,
        n_qubits: int = 4,
        quantum_device: str = "lightning.qubit",
        use_quantum_hardware: bool = False
    ):
        """
        Initialize quantum feature engineer.
        
        Args:
            n_qubits: Number of qubits for quantum circuits
            quantum_device: Quantum device to use
            use_quantum_hardware: Whether to use real quantum hardware
        """
        self.n_qubits = n_qubits
        self.quantum_device = quantum_device
        self.use_quantum_hardware = use_quantum_hardware
        
        # Quantum state
        self.quantum_available = False
        self.dev = None
        
        # Initialize quantum device
        self._initialize_quantum_device()
        
        self.logger.info(f"Quantum feature engineer initialized with {n_qubits} qubits")
    
    def _initialize_quantum_device(self):
        """Initialize quantum computing device."""
        try:
            import pennylane as qml
            
            # Create quantum device
            self.dev = qml.device(self.quantum_device, wires=self.n_qubits)
            self.quantum_available = True
            
            self.logger.info(f"Quantum device initialized: {self.quantum_device}")
            
        except ImportError:
            self.logger.warning("PennyLane not available. Quantum features will use classical approximations.")
            self.quantum_available = False
        except Exception as e:
            self.logger.warning(f"Error initializing quantum device: {e}. Using classical approximations.")
            self.quantum_available = False
    
    async def create_quantum_features(
        self,
        data: pd.DataFrame,
        price_columns: List[str] = ["open", "high", "low", "close"],
        volume_column: str = "volume"
    ) -> pd.DataFrame:
        """
        Create quantum-enhanced features from market data.
        
        Args:
            data: Market data DataFrame
            price_columns: List of price column names
            volume_column: Volume column name
            
        Returns:
            DataFrame with quantum features
        """
        self.logger.info("Creating quantum-enhanced features")
        
        quantum_features = pd.DataFrame(index=data.index)
        
        # 1. Quantum Entanglement Features
        entanglement_features = await self._create_entanglement_features(data, price_columns)
        quantum_features = pd.concat([quantum_features, entanglement_features], axis=1)
        
        # 2. Quantum Superposition Features
        superposition_features = await self._create_superposition_features(data, price_columns)
        quantum_features = pd.concat([quantum_features, superposition_features], axis=1)
        
        # 3. Quantum Interference Features
        interference_features = await self._create_interference_features(data, price_columns)
        quantum_features = pd.concat([quantum_features, interference_features], axis=1)
        
        # 4. Quantum Phase Features
        phase_features = await self._create_phase_features(data, price_columns, volume_column)
        quantum_features = pd.concat([quantum_features, phase_features], axis=1)
        
        # 5. Quantum Amplitude Features
        amplitude_features = await self._create_amplitude_features(data, price_columns)
        quantum_features = pd.concat([quantum_features, amplitude_features], axis=1)
        
        self.logger.info(f"Created {quantum_features.shape[1]} quantum features")
        return quantum_features
    
    async def _create_entanglement_features(
        self,
        data: pd.DataFrame,
        price_columns: List[str]
    ) -> pd.DataFrame:
        """Create features based on quantum entanglement principles."""
        features = pd.DataFrame(index=data.index)
        
        if self.quantum_available:
            # Use actual quantum circuits
            features = await self._quantum_entanglement_circuit(data, price_columns)
        else:
            # Classical approximation of entanglement
            features = self._classical_entanglement_approximation(data, price_columns)
        
        return features
    
    async def _quantum_entanglement_circuit(
        self,
        data: pd.DataFrame,
        price_columns: List[str]
    ) -> pd.DataFrame:
        """Create entanglement features using quantum circuits."""
        try:
            import pennylane as qml
            
            @qml.qnode(self.dev)
            def entanglement_circuit(prices):
                # Normalize prices to [0, π]
                normalized_prices = np.pi * (prices - prices.min()) / (prices.max() - prices.min() + 1e-8)
                
                # Create entangled state
                for i in range(min(len(normalized_prices), self.n_qubits)):
                    qml.RY(normalized_prices[i], wires=i)
                
                # Create entanglement
                for i in range(self.n_qubits - 1):
                    qml.CNOT(wires=[i, i + 1])
                
                # Measure entanglement
                return [qml.expval(qml.PauliZ(i)) for i in range(self.n_qubits)]
            
            features = pd.DataFrame(index=data.index)
            
            for idx, row in data.iterrows():
                prices = row[price_columns].values
                if len(prices) >= 2 and not np.isnan(prices).any():
                    entanglement_values = entanglement_circuit(prices)
                    
                    for i, val in enumerate(entanglement_values):
                        features.loc[idx, f"quantum_entanglement_{i}"] = val
                else:
                    # Fill with zeros if insufficient data
                    for i in range(self.n_qubits):
                        features.loc[idx, f"quantum_entanglement_{i}"] = 0.0
            
            return features
            
        except Exception as e:
            self.logger.warning(f"Error in quantum entanglement circuit: {e}")
            return self._classical_entanglement_approximation(data, price_columns)
    
    def _classical_entanglement_approximation(
        self,
        data: pd.DataFrame,
        price_columns: List[str]
    ) -> pd.DataFrame:
        """Classical approximation of quantum entanglement."""
        features = pd.DataFrame(index=data.index)
        
        # Calculate correlations between price components
        for i, col1 in enumerate(price_columns):
            for j, col2 in enumerate(price_columns[i+1:], i+1):
                if col1 in data.columns and col2 in data.columns:
                    # Rolling correlation as entanglement proxy
                    correlation = data[col1].rolling(window=20).corr(data[col2])
                    features[f"entanglement_{col1}_{col2}"] = correlation
                    
                    # Phase relationship
                    phase_diff = np.angle(np.exp(1j * (data[col1] - data[col2])))
                    features[f"phase_entanglement_{col1}_{col2}"] = phase_diff
        
        return features.fillna(0)
    
    async def _create_superposition_features(
        self,
        data: pd.DataFrame,
        price_columns: List[str]
    ) -> pd.DataFrame:
        """Create features based on quantum superposition principles."""
        features = pd.DataFrame(index=data.index)
        
        # Superposition of price states
        for col in price_columns:
            if col in data.columns:
                # Normalize prices
                normalized = (data[col] - data[col].rolling(20).mean()) / (data[col].rolling(20).std() + 1e-8)
                
                # Create superposition-like features
                features[f"superposition_sin_{col}"] = np.sin(normalized)
                features[f"superposition_cos_{col}"] = np.cos(normalized)
                features[f"superposition_magnitude_{col}"] = np.sqrt(np.sin(normalized)**2 + np.cos(normalized)**2)
                
                # Quantum-inspired probability amplitudes
                features[f"amplitude_squared_{col}"] = normalized**2
                features[f"probability_{col}"] = np.abs(normalized) / (np.abs(normalized).rolling(20).sum() + 1e-8)
        
        return features.fillna(0)
    
    async def _create_interference_features(
        self,
        data: pd.DataFrame,
        price_columns: List[str]
    ) -> pd.DataFrame:
        """Create features based on quantum interference principles."""
        features = pd.DataFrame(index=data.index)
        
        # Interference patterns between different price components
        for i, col1 in enumerate(price_columns):
            for j, col2 in enumerate(price_columns[i+1:], i+1):
                if col1 in data.columns and col2 in data.columns:
                    # Normalize prices
                    norm1 = (data[col1] - data[col1].mean()) / (data[col1].std() + 1e-8)
                    norm2 = (data[col2] - data[col2].mean()) / (data[col2].std() + 1e-8)
                    
                    # Constructive interference
                    constructive = np.cos(norm1 - norm2)
                    features[f"constructive_interference_{col1}_{col2}"] = constructive
                    
                    # Destructive interference
                    destructive = np.sin(norm1 - norm2)
                    features[f"destructive_interference_{col1}_{col2}"] = destructive
                    
                    # Interference magnitude
                    magnitude = np.sqrt(constructive**2 + destructive**2)
                    features[f"interference_magnitude_{col1}_{col2}"] = magnitude
        
        return features.fillna(0)
    
    async def _create_phase_features(
        self,
        data: pd.DataFrame,
        price_columns: List[str],
        volume_column: str
    ) -> pd.DataFrame:
        """Create features based on quantum phase relationships."""
        features = pd.DataFrame(index=data.index)
        
        # Phase relationships between price and volume
        if volume_column in data.columns:
            for col in price_columns:
                if col in data.columns:
                    # Normalize both series
                    price_norm = (data[col] - data[col].rolling(20).mean()) / (data[col].rolling(20).std() + 1e-8)
                    volume_norm = (data[volume_column] - data[volume_column].rolling(20).mean()) / (data[volume_column].rolling(20).std() + 1e-8)
                    
                    # Phase difference
                    phase_diff = np.angle(np.exp(1j * (price_norm - volume_norm)))
                    features[f"phase_price_volume_{col}"] = phase_diff

                    # Phase velocity (rate of phase change)
                    phase_velocity = pd.Series(phase_diff).diff()
                    features[f"phase_velocity_{col}"] = phase_velocity

                    # Phase acceleration
                    phase_acceleration = phase_velocity.diff()
                    features[f"phase_acceleration_{col}"] = phase_acceleration
        
        # Phase relationships between consecutive time periods
        for col in price_columns:
            if col in data.columns:
                # Phase evolution over time
                returns = data[col].pct_change()
                cumulative_phase = np.cumsum(returns.fillna(0))
                features[f"cumulative_phase_{col}"] = np.sin(cumulative_phase)
                features[f"cumulative_phase_cos_{col}"] = np.cos(cumulative_phase)
        
        return features.fillna(0)
    
    async def _create_amplitude_features(
        self,
        data: pd.DataFrame,
        price_columns: List[str]
    ) -> pd.DataFrame:
        """Create features based on quantum amplitude principles."""
        features = pd.DataFrame(index=data.index)
        
        # Quantum amplitude-inspired features
        for col in price_columns:
            if col in data.columns:
                # Probability amplitude (normalized price changes)
                returns = data[col].pct_change()
                rolling_std = returns.rolling(20).std()
                amplitude = returns / (rolling_std + 1e-8)
                features[f"quantum_amplitude_{col}"] = amplitude
                
                # Amplitude squared (probability)
                features[f"quantum_probability_{col}"] = amplitude**2
                
                # Amplitude modulation
                amplitude_envelope = np.abs(amplitude).rolling(10).mean()
                features[f"amplitude_envelope_{col}"] = amplitude_envelope
                
                # Amplitude frequency
                amplitude_fft = np.abs(np.fft.fft(amplitude.fillna(0).values[-50:]))
                if len(amplitude_fft) > 0:
                    dominant_freq = np.argmax(amplitude_fft)
                    features[f"amplitude_dominant_freq_{col}"] = dominant_freq
                else:
                    features[f"amplitude_dominant_freq_{col}"] = 0
        
        return features.fillna(0)
    
    def create_quantum_momentum_features(
        self,
        data: pd.DataFrame,
        price_column: str = "close",
        periods: List[int] = [5, 10, 20, 50]
    ) -> pd.DataFrame:
        """Create quantum-inspired momentum features."""
        features = pd.DataFrame(index=data.index)
        
        if price_column not in data.columns:
            return features
        
        prices = data[price_column]
        
        for period in periods:
            # Quantum momentum (inspired by quantum mechanical momentum)
            momentum = prices.diff(period)
            features[f"quantum_momentum_{period}"] = momentum
            
            # Uncertainty principle inspired feature
            position_uncertainty = prices.rolling(period).std()
            momentum_uncertainty = momentum.rolling(period).std()
            uncertainty_product = position_uncertainty * momentum_uncertainty
            features[f"uncertainty_product_{period}"] = uncertainty_product
            
            # Quantum tunneling probability (breakthrough resistance/support)
            resistance = prices.rolling(period).max()
            support = prices.rolling(period).min()
            current_position = (prices - support) / (resistance - support + 1e-8)
            tunneling_prob = np.exp(-current_position)  # Exponential decay
            features[f"tunneling_probability_{period}"] = tunneling_prob
        
        return features.fillna(0)
    
    def get_feature_info(self) -> Dict[str, Any]:
        """Get information about quantum feature engineering capabilities."""
        return {
            "quantum_available": self.quantum_available,
            "n_qubits": self.n_qubits,
            "quantum_device": self.quantum_device,
            "use_quantum_hardware": self.use_quantum_hardware,
            "feature_types": [
                "entanglement",
                "superposition", 
                "interference",
                "phase",
                "amplitude",
                "momentum"
            ]
        }
