"""
Quantum Classifier for QuantumEdge system.
Uses quantum computing principles for enhanced stock classification.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union, Tuple
import numpy as np
import pandas as pd
from datetime import datetime

from ..base_model import BaseModel, ModelError, TrainingError, PredictionError


class QuantumClassifier(BaseModel):
    """
    Quantum-inspired classifier for stock prediction.
    Uses quantum computing principles for enhanced pattern recognition.
    """
    
    def __init__(
        self,
        n_qubits: int = 4,
        quantum_device: str = "lightning.qubit",
        feature_columns: Optional[List[str]] = None,
        target_column: str = "target",
        use_quantum_hardware: bool = False,
        classical_fallback: bool = True
    ):
        """
        Initialize quantum classifier.
        
        Args:
            n_qubits: Number of qubits for quantum circuits
            quantum_device: Quantum device to use
            feature_columns: List of feature column names
            target_column: Name of target column
            use_quantum_hardware: Whether to use real quantum hardware
            classical_fallback: Whether to use classical fallback if quantum fails
        """
        super().__init__(
            model_name="QuantumClassifier",
            model_type="quantum_classifier",
            feature_columns=feature_columns,
            target_column=target_column
        )
        
        self.n_qubits = n_qubits
        self.quantum_device = quantum_device
        self.use_quantum_hardware = use_quantum_hardware
        self.classical_fallback = classical_fallback
        
        # Quantum state
        self.quantum_available = False
        self.dev = None
        self.quantum_circuit = None
        self.classical_model = None
        
        # Model parameters
        self.learning_rate = 0.1
        self.max_iterations = 100
        self.convergence_threshold = 1e-6
        
        # Training state
        self.weights = None
        self.training_losses = []
        
        # Initialize quantum device
        self._initialize_quantum_device()
        
        self.logger.info(f"Quantum classifier initialized with {n_qubits} qubits")
    
    def _initialize_quantum_device(self):
        """Initialize quantum computing device."""
        try:
            import pennylane as qml
            
            # Create quantum device
            self.dev = qml.device(self.quantum_device, wires=self.n_qubits)
            self.quantum_available = True
            
            # Create quantum circuit
            self._create_quantum_circuit()
            
            self.logger.info(f"Quantum device initialized: {self.quantum_device}")
            
        except ImportError:
            self.logger.warning("PennyLane not available. Using classical fallback.")
            self.quantum_available = False
        except Exception as e:
            self.logger.warning(f"Error initializing quantum device: {e}. Using classical fallback.")
            self.quantum_available = False
        
        # Initialize classical fallback if needed
        if not self.quantum_available and self.classical_fallback:
            self._initialize_classical_fallback()
    
    def _create_quantum_circuit(self):
        """Create the quantum circuit for classification."""
        if not self.quantum_available:
            return
        
        try:
            import pennylane as qml
            
            @qml.qnode(self.dev, diff_method="parameter-shift")
            def quantum_circuit(features, weights):
                # Feature encoding
                for i, feature in enumerate(features[:self.n_qubits]):
                    qml.RY(feature, wires=i)
                
                # Variational layers
                for layer in range(len(weights)):
                    # Rotation gates
                    for i in range(self.n_qubits):
                        qml.RY(weights[layer][i][0], wires=i)
                        qml.RZ(weights[layer][i][1], wires=i)
                    
                    # Entangling gates
                    for i in range(self.n_qubits - 1):
                        qml.CNOT(wires=[i, i + 1])
                    
                    # Ring connectivity
                    if self.n_qubits > 2:
                        qml.CNOT(wires=[self.n_qubits - 1, 0])
                
                # Measurement
                return qml.expval(qml.PauliZ(0))
            
            self.quantum_circuit = quantum_circuit
            
        except Exception as e:
            self.logger.error(f"Error creating quantum circuit: {e}")
            self.quantum_available = False
    
    def _initialize_classical_fallback(self):
        """Initialize classical model as fallback."""
        try:
            from sklearn.ensemble import RandomForestClassifier
            
            self.classical_model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
            
            self.logger.info("Classical fallback model initialized")
            
        except ImportError:
            self.logger.warning("scikit-learn not available for classical fallback")
    
    async def train(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        validation_data: Optional[Tuple[pd.DataFrame, pd.Series]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Train the quantum classifier.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Optional validation data
            **kwargs: Additional training parameters
            
        Returns:
            Dictionary with training results and metrics
        """
        try:
            self.logger.info("Starting quantum classifier training")
            
            # Validate inputs
            X = self.validate_features(X)
            y = self.validate_targets(y)
            
            # Store feature columns if not set
            if not self.feature_columns:
                self.feature_columns = X.columns.tolist()
            
            # Limit features to number of qubits
            if len(X.columns) > self.n_qubits:
                # Select most important features (using variance for now)
                feature_variance = X.var().sort_values(ascending=False)
                selected_features = feature_variance.head(self.n_qubits).index.tolist()
                X = X[selected_features]
                self.feature_columns = selected_features
                self.logger.info(f"Selected {len(selected_features)} features for quantum training")
            
            # Normalize features to [0, π] for quantum encoding
            X_normalized = self._normalize_features(X)
            
            # Convert targets to binary if needed
            y_binary = self._prepare_targets(y)
            
            training_results = {}
            
            if self.quantum_available:
                # Train quantum model
                quantum_results = await self._train_quantum_model(X_normalized, y_binary, validation_data)
                training_results.update(quantum_results)
            
            if self.classical_fallback and self.classical_model:
                # Train classical fallback
                classical_results = await self._train_classical_model(X, y, validation_data)
                training_results["classical_fallback"] = classical_results
            
            # Mark as trained
            self.is_trained = True
            self.last_trained_at = datetime.now()
            
            # Store training history
            training_record = {
                "timestamp": datetime.now().isoformat(),
                "training_samples": len(X),
                "validation_samples": len(validation_data[0]) if validation_data else 0,
                "quantum_available": self.quantum_available,
                "features_used": len(self.feature_columns),
                "results": training_results
            }
            
            self.training_history.append(training_record)
            
            self.logger.info("Quantum classifier training completed")
            return training_results
            
        except Exception as e:
            self.logger.error(f"Error training quantum classifier: {e}")
            raise TrainingError(f"Quantum classifier training failed: {e}")
    
    async def _train_quantum_model(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        validation_data: Optional[Tuple[pd.DataFrame, pd.Series]] = None
    ) -> Dict[str, Any]:
        """Train the quantum model component."""
        try:
            import pennylane as qml
            
            # Initialize weights
            n_layers = 3
            self.weights = np.random.normal(0, 0.1, (n_layers, self.n_qubits, 2))
            
            # Optimizer
            optimizer = qml.AdamOptimizer(stepsize=self.learning_rate)
            
            # Training loop
            self.training_losses = []
            
            for iteration in range(self.max_iterations):
                # Calculate cost
                cost = self._quantum_cost_function(X.values, y.values, self.weights)
                self.training_losses.append(cost)
                
                # Update weights
                self.weights = optimizer.step(
                    lambda w: self._quantum_cost_function(X.values, y.values, w),
                    self.weights
                )
                
                # Check convergence
                if iteration > 10 and abs(self.training_losses[-1] - self.training_losses[-2]) < self.convergence_threshold:
                    self.logger.info(f"Quantum training converged at iteration {iteration}")
                    break
                
                if iteration % 20 == 0:
                    self.logger.info(f"Quantum training iteration {iteration}, cost: {cost:.6f}")
            
            # Evaluate on training data
            train_predictions = await self._quantum_predict(X.values)
            train_accuracy = np.mean((train_predictions > 0.5) == y.values)
            
            # Evaluate on validation data if available
            val_accuracy = None
            if validation_data:
                X_val, y_val = validation_data
                X_val_norm = self._normalize_features(X_val[self.feature_columns])
                y_val_binary = self._prepare_targets(y_val)
                
                val_predictions = await self._quantum_predict(X_val_norm.values)
                val_accuracy = np.mean((val_predictions > 0.5) == y_val_binary.values)
            
            return {
                "quantum_training_completed": True,
                "iterations": len(self.training_losses),
                "final_cost": self.training_losses[-1],
                "train_accuracy": train_accuracy,
                "val_accuracy": val_accuracy,
                "training_losses": self.training_losses
            }
            
        except Exception as e:
            self.logger.error(f"Error in quantum model training: {e}")
            return {"quantum_training_completed": False, "error": str(e)}
    
    async def _train_classical_model(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        validation_data: Optional[Tuple[pd.DataFrame, pd.Series]] = None
    ) -> Dict[str, Any]:
        """Train the classical fallback model."""
        try:
            # Train classical model
            self.classical_model.fit(X, y)
            
            # Evaluate
            train_accuracy = self.classical_model.score(X, y)
            
            val_accuracy = None
            if validation_data:
                X_val, y_val = validation_data
                val_accuracy = self.classical_model.score(X_val, y_val)
            
            return {
                "classical_training_completed": True,
                "train_accuracy": train_accuracy,
                "val_accuracy": val_accuracy
            }
            
        except Exception as e:
            self.logger.error(f"Error in classical model training: {e}")
            return {"classical_training_completed": False, "error": str(e)}
    
    def _quantum_cost_function(self, X: np.ndarray, y: np.ndarray, weights: np.ndarray) -> float:
        """Calculate cost function for quantum model."""
        predictions = []
        
        for i in range(len(X)):
            pred = self.quantum_circuit(X[i], weights)
            predictions.append(pred)
        
        predictions = np.array(predictions)
        
        # Convert quantum output [-1, 1] to probability [0, 1]
        probabilities = (predictions + 1) / 2
        
        # Binary cross-entropy loss
        epsilon = 1e-15
        probabilities = np.clip(probabilities, epsilon, 1 - epsilon)
        cost = -np.mean(y * np.log(probabilities) + (1 - y) * np.log(1 - probabilities))
        
        return cost
    
    async def _quantum_predict(self, X: np.ndarray) -> np.ndarray:
        """Make predictions using quantum model."""
        predictions = []
        
        for i in range(len(X)):
            pred = self.quantum_circuit(X[i], self.weights)
            # Convert from [-1, 1] to [0, 1]
            prob = (pred + 1) / 2
            predictions.append(prob)
        
        return np.array(predictions)
    
    async def predict(
        self,
        X: pd.DataFrame,
        return_probabilities: bool = False,
        **kwargs
    ) -> Union[np.ndarray, Tuple[np.ndarray, np.ndarray]]:
        """
        Make predictions using the trained quantum classifier.
        
        Args:
            X: Features for prediction
            return_probabilities: Whether to return prediction probabilities
            **kwargs: Additional prediction parameters
            
        Returns:
            Predictions, optionally with probabilities
        """
        if not self.is_trained:
            raise PredictionError("Model must be trained before making predictions")
        
        try:
            # Validate features
            X = self.validate_features(X)
            
            # Use quantum model if available
            if self.quantum_available and self.weights is not None:
                # Select features used in training
                X_selected = X[self.feature_columns]
                X_normalized = self._normalize_features(X_selected)
                
                probabilities = await self._quantum_predict(X_normalized.values)
                predictions = (probabilities > 0.5).astype(int)
                
                if return_probabilities:
                    return predictions, probabilities
                else:
                    return predictions
            
            # Fallback to classical model
            elif self.classical_model:
                if return_probabilities and hasattr(self.classical_model, 'predict_proba'):
                    predictions = self.classical_model.predict(X)
                    probabilities = self.classical_model.predict_proba(X)[:, 1]
                    return predictions, probabilities
                else:
                    predictions = self.classical_model.predict(X)
                    return predictions
            
            else:
                raise PredictionError("No trained model available for prediction")
                
        except Exception as e:
            self.logger.error(f"Error making quantum predictions: {e}")
            raise PredictionError(f"Quantum prediction failed: {e}")
    
    def _normalize_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Normalize features to [0, π] for quantum encoding."""
        X_norm = X.copy()
        
        for col in X_norm.columns:
            col_min = X_norm[col].min()
            col_max = X_norm[col].max()
            
            if col_max > col_min:
                X_norm[col] = np.pi * (X_norm[col] - col_min) / (col_max - col_min)
            else:
                X_norm[col] = 0
        
        return X_norm
    
    def _prepare_targets(self, y: pd.Series) -> pd.Series:
        """Prepare targets for quantum training."""
        # Convert to binary if needed
        unique_values = y.unique()
        
        if len(unique_values) == 2:
            # Already binary, ensure 0/1 encoding
            y_binary = y.copy()
            y_binary = (y_binary == unique_values[1]).astype(int)
        else:
            # Multi-class: use one-vs-rest for first class
            y_binary = (y == unique_values[0]).astype(int)
        
        return y_binary
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance scores.
        
        Returns:
            Dictionary mapping feature names to importance scores
        """
        if not self.is_trained:
            return {}
        
        # For quantum model, use weight magnitudes as importance proxy
        if self.quantum_available and self.weights is not None:
            importance = {}
            
            for i, feature in enumerate(self.feature_columns[:self.n_qubits]):
                # Calculate average weight magnitude for this feature
                feature_weights = self.weights[:, i, :].flatten()
                importance[feature] = np.mean(np.abs(feature_weights))
            
            return importance
        
        # For classical fallback
        elif self.classical_model and hasattr(self.classical_model, 'feature_importances_'):
            return dict(zip(self.feature_columns, self.classical_model.feature_importances_))
        
        return {}
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive quantum classifier information."""
        base_info = super().get_model_info()
        
        quantum_info = {
            "n_qubits": self.n_qubits,
            "quantum_device": self.quantum_device,
            "quantum_available": self.quantum_available,
            "use_quantum_hardware": self.use_quantum_hardware,
            "classical_fallback": self.classical_fallback,
            "learning_rate": self.learning_rate,
            "max_iterations": self.max_iterations,
            "training_losses": self.training_losses[-10:] if self.training_losses else [],
            "weights_shape": self.weights.shape if self.weights is not None else None
        }
        
        base_info.update(quantum_info)
        return base_info
