"""
LightGBM model implementation for QuantumEdge system.
Provides fast and efficient gradient boosting for large datasets.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union, Tuple
import numpy as np
import pandas as pd
from datetime import datetime

from ..base_model import BaseModel, ModelError, TrainingError, PredictionError


class LightGBMModel(BaseModel):
    """
    LightGBM model for stock prediction.
    Optimized for speed and memory efficiency with large financial datasets.
    """
    
    def __init__(
        self,
        feature_columns: Optional[List[str]] = None,
        target_column: str = "target",
        model_params: Optional[Dict[str, Any]] = None,
        early_stopping_rounds: int = 50,
        categorical_features: Optional[List[str]] = None
    ):
        """
        Initialize LightGBM model.
        
        Args:
            feature_columns: List of feature column names
            target_column: Name of target column
            model_params: LightGBM-specific parameters
            early_stopping_rounds: Early stopping rounds for training
            categorical_features: List of categorical feature names
        """
        # Default LightGBM parameters optimized for financial data
        default_params = {
            "objective": "binary",
            "metric": "binary_logloss",
            "boosting_type": "gbdt",
            "num_leaves": 31,
            "learning_rate": 0.1,
            "feature_fraction": 0.8,
            "bagging_fraction": 0.8,
            "bagging_freq": 5,
            "min_child_samples": 20,
            "random_state": 42,
            "n_jobs": -1,
            "verbosity": -1,
            "force_col_wise": True
        }
        
        if model_params:
            default_params.update(model_params)
        
        super().__init__(
            model_name="LightGBM",
            model_type="classifier",
            feature_columns=feature_columns,
            target_column=target_column,
            model_params=default_params
        )
        
        self.early_stopping_rounds = early_stopping_rounds
        self.categorical_features = categorical_features or []
        
        # LightGBM-specific attributes
        self.best_iteration = None
        self.training_log = []
        self.evals_result = {}
    
    async def train(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        validation_data: Optional[Tuple[pd.DataFrame, pd.Series]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Train the LightGBM model.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Optional validation data for early stopping
            **kwargs: Additional training parameters
            
        Returns:
            Dictionary with training results and metrics
        """
        try:
            import lightgbm as lgb
        except ImportError:
            raise ModelError("LightGBM not installed. Install with: pip install lightgbm")
        
        try:
            self.logger.info("Starting LightGBM training")
            
            # Validate inputs
            X = self.validate_features(X)
            y = self.validate_targets(y)
            
            # Store feature columns if not set
            if not self.feature_columns:
                self.feature_columns = X.columns.tolist()
            
            # Determine model type from target
            unique_targets = y.nunique()
            if unique_targets == 2:
                self.model_type = "classifier"
                self.model_params["objective"] = "binary"
                self.model_params["metric"] = "binary_logloss"
            elif unique_targets > 2 and unique_targets < 20:
                self.model_type = "classifier"
                self.model_params["objective"] = "multiclass"
                self.model_params["metric"] = "multi_logloss"
                self.model_params["num_class"] = unique_targets
            else:
                self.model_type = "regressor"
                self.model_params["objective"] = "regression"
                self.model_params["metric"] = "rmse"
            
            # Prepare categorical features
            categorical_feature_indices = []
            if self.categorical_features:
                for cat_feature in self.categorical_features:
                    if cat_feature in X.columns:
                        categorical_feature_indices.append(X.columns.get_loc(cat_feature))
            
            # Create LightGBM datasets
            train_data = lgb.Dataset(
                X, label=y,
                categorical_feature=categorical_feature_indices,
                feature_name=list(X.columns)
            )
            
            valid_sets = [train_data]
            valid_names = ["train"]
            
            if validation_data:
                X_val, y_val = validation_data
                X_val = self.validate_features(X_val)
                y_val = self.validate_targets(y_val)
                
                valid_data = lgb.Dataset(
                    X_val, label=y_val,
                    reference=train_data,
                    categorical_feature=categorical_feature_indices,
                    feature_name=list(X_val.columns)
                )
                valid_sets.append(valid_data)
                valid_names.append("valid")
            
            # Prepare training parameters
            train_params = self.model_params.copy()
            train_params.update(kwargs)
            
            # Remove parameters that are not for lgb.train
            lgb_train_params = {k: v for k, v in train_params.items() 
                               if k not in ['n_estimators', 'random_state']}
            
            num_boost_round = train_params.get('n_estimators', 1000)
            
            # Train the model
            start_time = datetime.now()
            
            callbacks = []
            if validation_data:
                callbacks.append(lgb.early_stopping(self.early_stopping_rounds))
                callbacks.append(lgb.record_evaluation(self.evals_result))
            
            self.model = lgb.train(
                lgb_train_params,
                train_data,
                num_boost_round=num_boost_round,
                valid_sets=valid_sets,
                valid_names=valid_names,
                callbacks=callbacks
            )
            
            training_time = (datetime.now() - start_time).total_seconds()
            
            # Get best iteration
            if validation_data and hasattr(self.model, 'best_iteration'):
                self.best_iteration = self.model.best_iteration
            
            # Mark as trained
            self.is_trained = True
            self.last_trained_at = datetime.now()
            
            # Get feature importance
            self.feature_importance = self.get_feature_importance()
            
            # Evaluate on training data
            train_predictions = self.model.predict(X, num_iteration=self.best_iteration)
            if self.model_type == "classifier" and unique_targets == 2:
                train_predictions = (train_predictions > 0.5).astype(int)
            elif self.model_type == "classifier":
                train_predictions = np.argmax(train_predictions, axis=1)
            
            train_metrics = await self._calculate_metrics(y, train_predictions)
            
            # Evaluate on validation data if available
            val_metrics = {}
            if validation_data:
                val_predictions = self.model.predict(X_val, num_iteration=self.best_iteration)
                if self.model_type == "classifier" and unique_targets == 2:
                    val_predictions = (val_predictions > 0.5).astype(int)
                elif self.model_type == "classifier":
                    val_predictions = np.argmax(val_predictions, axis=1)
                
                val_metrics = await self._calculate_metrics(y_val, val_predictions)
            
            # Store metrics
            self.model_metrics = {
                "train_metrics": train_metrics,
                "val_metrics": val_metrics,
                "training_time_seconds": training_time,
                "best_iteration": self.best_iteration,
                "feature_count": len(self.feature_columns),
                "evals_result": self.evals_result
            }
            
            # Store training history
            training_record = {
                "timestamp": datetime.now().isoformat(),
                "training_samples": len(X),
                "validation_samples": len(X_val) if validation_data else 0,
                "training_time_seconds": training_time,
                "best_iteration": self.best_iteration,
                "train_metrics": train_metrics,
                "val_metrics": val_metrics,
                "model_params": self.model_params
            }
            
            self.training_history.append(training_record)
            
            self.logger.info(f"LightGBM training completed in {training_time:.2f} seconds")
            
            return {
                "success": True,
                "training_time_seconds": training_time,
                "best_iteration": self.best_iteration,
                "train_metrics": train_metrics,
                "val_metrics": val_metrics,
                "feature_importance": self.feature_importance,
                "evals_result": self.evals_result
            }
            
        except Exception as e:
            self.logger.error(f"Error training LightGBM model: {e}")
            raise TrainingError(f"LightGBM training failed: {e}")
    
    async def predict(
        self,
        X: pd.DataFrame,
        return_probabilities: bool = False,
        **kwargs
    ) -> Union[np.ndarray, Tuple[np.ndarray, np.ndarray]]:
        """
        Make predictions using the trained LightGBM model.
        
        Args:
            X: Features for prediction
            return_probabilities: Whether to return prediction probabilities
            **kwargs: Additional prediction parameters
            
        Returns:
            Predictions, optionally with probabilities
        """
        if not self.is_trained:
            raise PredictionError("Model must be trained before making predictions")
        
        try:
            # Validate features
            X = self.validate_features(X)
            
            # Make predictions
            raw_predictions = self.model.predict(X, num_iteration=self.best_iteration)
            
            if self.model_type == "classifier":
                if len(raw_predictions.shape) == 1:  # Binary classification
                    probabilities = np.column_stack([1 - raw_predictions, raw_predictions])
                    predictions = (raw_predictions > 0.5).astype(int)
                else:  # Multi-class classification
                    probabilities = raw_predictions
                    predictions = np.argmax(raw_predictions, axis=1)
                
                if return_probabilities:
                    return predictions, probabilities
                else:
                    return predictions
            else:
                # Regression
                return raw_predictions
                
        except Exception as e:
            self.logger.error(f"Error making LightGBM predictions: {e}")
            raise PredictionError(f"LightGBM prediction failed: {e}")
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance scores from the trained model.
        
        Returns:
            Dictionary mapping feature names to importance scores
        """
        if not self.is_trained:
            return {}
        
        importance_scores = self.model.feature_importance(importance_type='gain')
        feature_names = self.feature_columns or [f"feature_{i}" for i in range(len(importance_scores))]
        
        return dict(zip(feature_names, importance_scores))
    
    def get_shap_values(self, X: pd.DataFrame, max_samples: int = 1000) -> Optional[np.ndarray]:
        """
        Get SHAP values for model interpretability.
        
        Args:
            X: Features to explain
            max_samples: Maximum number of samples to use for SHAP
            
        Returns:
            SHAP values array or None if SHAP not available
        """
        if not self.is_trained:
            return None
        
        try:
            import shap
            
            # Validate features
            X = self.validate_features(X)
            
            # Limit samples for performance
            if len(X) > max_samples:
                X = X.sample(n=max_samples, random_state=42)
            
            # Create SHAP explainer
            explainer = shap.TreeExplainer(self.model)
            shap_values = explainer.shap_values(X)
            
            return shap_values
            
        except ImportError:
            self.logger.warning("SHAP not available for model interpretability")
            return None
        except Exception as e:
            self.logger.warning(f"Error calculating SHAP values: {e}")
            return None
    
    async def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate performance metrics."""
        metrics = {}
        
        try:
            from sklearn.metrics import (
                accuracy_score, precision_score, recall_score, f1_score,
                roc_auc_score, mean_squared_error, mean_absolute_error, r2_score
            )
            
            if self.model_type == "classifier":
                metrics["accuracy"] = accuracy_score(y_true, y_pred)
                metrics["precision"] = precision_score(y_true, y_pred, average="weighted", zero_division=0)
                metrics["recall"] = recall_score(y_true, y_pred, average="weighted", zero_division=0)
                metrics["f1"] = f1_score(y_true, y_pred, average="weighted", zero_division=0)
                
                # ROC AUC for binary classification
                if len(np.unique(y_true)) == 2:
                    try:
                        y_prob = self.model.predict(self.validate_features(pd.DataFrame(index=y_true.index, columns=self.feature_columns)))
                        metrics["roc_auc"] = roc_auc_score(y_true, y_prob)
                    except:
                        pass
            else:
                metrics["mse"] = mean_squared_error(y_true, y_pred)
                metrics["mae"] = mean_absolute_error(y_true, y_pred)
                metrics["r2"] = r2_score(y_true, y_pred)
                metrics["rmse"] = np.sqrt(metrics["mse"])
        
        except ImportError:
            self.logger.warning("scikit-learn not available for metric calculation")
        except Exception as e:
            self.logger.warning(f"Error calculating metrics: {e}")
        
        return metrics
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive LightGBM model information."""
        base_info = super().get_model_info()
        
        lgb_info = {
            "early_stopping_rounds": self.early_stopping_rounds,
            "categorical_features": self.categorical_features,
            "best_iteration": self.best_iteration,
            "training_log": self.training_log[-5:] if self.training_log else [],  # Last 5 entries
            "evals_result": self.evals_result
        }
        
        base_info.update(lgb_info)
        return base_info
