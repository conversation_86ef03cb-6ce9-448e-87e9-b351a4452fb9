"""
XGBoost model implementation for QuantumEdge system.
Provides high-performance gradient boosting for stock prediction.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union, Tuple
import numpy as np
import pandas as pd
from datetime import datetime

from ..base_model import BaseModel, ModelError, TrainingError, PredictionError


class XGBoostModel(BaseModel):
    """
    XGBoost model for stock prediction.
    Optimized for financial time series and feature importance analysis.
    """
    
    def __init__(
        self,
        feature_columns: Optional[List[str]] = None,
        target_column: str = "target",
        model_params: Optional[Dict[str, Any]] = None,
        early_stopping_rounds: int = 50,
        eval_metric: str = "auto"
    ):
        """
        Initialize XGBoost model.
        
        Args:
            feature_columns: List of feature column names
            target_column: Name of target column
            model_params: XGBoost-specific parameters
            early_stopping_rounds: Early stopping rounds for training
            eval_metric: Evaluation metric for training
        """
        # Default XGBoost parameters optimized for financial data
        default_params = {
            "objective": "binary:logistic",
            "eval_metric": "logloss",
            "max_depth": 6,
            "learning_rate": 0.1,
            "n_estimators": 1000,
            "subsample": 0.8,
            "colsample_bytree": 0.8,
            "random_state": 42,
            "n_jobs": -1,
            "verbosity": 0
        }
        
        if model_params:
            default_params.update(model_params)
        
        super().__init__(
            model_name="XGBoost",
            model_type="classifier",
            feature_columns=feature_columns,
            target_column=target_column,
            model_params=default_params
        )
        
        self.early_stopping_rounds = early_stopping_rounds
        self.eval_metric = eval_metric
        
        # XGBoost-specific attributes
        self.best_iteration = None
        self.training_log = []
    
    async def train(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        validation_data: Optional[Tuple[pd.DataFrame, pd.Series]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Train the XGBoost model.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Optional validation data for early stopping
            **kwargs: Additional training parameters
            
        Returns:
            Dictionary with training results and metrics
        """
        try:
            import xgboost as xgb
        except ImportError:
            raise ModelError("XGBoost not installed. Install with: pip install xgboost")
        
        try:
            self.logger.info("Starting XGBoost training")
            
            # Validate inputs
            X = self.validate_features(X)
            y = self.validate_targets(y)
            
            # Store feature columns if not set
            if not self.feature_columns:
                self.feature_columns = X.columns.tolist()
            
            # Determine model type from target
            unique_targets = y.nunique()
            if unique_targets == 2:
                self.model_type = "classifier"
                self.model_params["objective"] = "binary:logistic"
                self.model_params["eval_metric"] = "logloss"
            elif unique_targets > 2 and unique_targets < 20:
                self.model_type = "classifier"
                self.model_params["objective"] = "multi:softprob"
                self.model_params["eval_metric"] = "mlogloss"
                self.model_params["num_class"] = unique_targets
            else:
                self.model_type = "regressor"
                self.model_params["objective"] = "reg:squarederror"
                self.model_params["eval_metric"] = "rmse"
            
            # Prepare training parameters
            train_params = self.model_params.copy()
            train_params.update(kwargs)
            
            # Create XGBoost model
            if self.model_type == "classifier":
                self.model = xgb.XGBClassifier(**train_params)
            else:
                self.model = xgb.XGBRegressor(**train_params)
            
            # Prepare validation data for early stopping
            eval_set = None
            if validation_data:
                X_val, y_val = validation_data
                X_val = self.validate_features(X_val)
                y_val = self.validate_targets(y_val)
                eval_set = [(X, y), (X_val, y_val)]
            
            # Train the model
            start_time = datetime.now()

            if eval_set:
                try:
                    # Try new XGBoost API
                    import xgboost as xgb
                    self.model.fit(
                        X, y,
                        eval_set=eval_set,
                        callbacks=[xgb.callback.EarlyStopping(rounds=self.early_stopping_rounds)],
                        verbose=False
                    )
                    self.best_iteration = self.model.best_iteration
                except:
                    # Fallback for older XGBoost versions
                    self.model.fit(X, y)
            else:
                self.model.fit(X, y)
            
            training_time = (datetime.now() - start_time).total_seconds()
            
            # Mark as trained
            self.is_trained = True
            self.last_trained_at = datetime.now()
            
            # Get feature importance
            self.feature_importance = self.get_feature_importance()
            
            # Evaluate on training data
            train_predictions = self.model.predict(X)
            train_metrics = await self._calculate_metrics(y, train_predictions)
            
            # Evaluate on validation data if available
            val_metrics = {}
            if validation_data:
                val_predictions = self.model.predict(X_val)
                val_metrics = await self._calculate_metrics(y_val, val_predictions)
            
            # Store metrics
            self.model_metrics = {
                "train_metrics": train_metrics,
                "val_metrics": val_metrics,
                "training_time_seconds": training_time,
                "best_iteration": self.best_iteration,
                "feature_count": len(self.feature_columns)
            }
            
            # Store training history
            training_record = {
                "timestamp": datetime.now().isoformat(),
                "training_samples": len(X),
                "validation_samples": len(X_val) if validation_data else 0,
                "training_time_seconds": training_time,
                "best_iteration": self.best_iteration,
                "train_metrics": train_metrics,
                "val_metrics": val_metrics,
                "model_params": self.model_params
            }
            
            self.training_history.append(training_record)
            
            self.logger.info(f"XGBoost training completed in {training_time:.2f} seconds")
            
            return {
                "success": True,
                "training_time_seconds": training_time,
                "best_iteration": self.best_iteration,
                "train_metrics": train_metrics,
                "val_metrics": val_metrics,
                "feature_importance": self.feature_importance
            }
            
        except Exception as e:
            self.logger.error(f"Error training XGBoost model: {e}")
            raise TrainingError(f"XGBoost training failed: {e}")
    
    async def predict(
        self,
        X: pd.DataFrame,
        return_probabilities: bool = False,
        **kwargs
    ) -> Union[np.ndarray, Tuple[np.ndarray, np.ndarray]]:
        """
        Make predictions using the trained XGBoost model.
        
        Args:
            X: Features for prediction
            return_probabilities: Whether to return prediction probabilities
            **kwargs: Additional prediction parameters
            
        Returns:
            Predictions, optionally with probabilities
        """
        if not self.is_trained:
            raise PredictionError("Model must be trained before making predictions")
        
        try:
            # Validate features
            X = self.validate_features(X)
            
            # Make predictions
            if self.model_type == "classifier" and return_probabilities:
                predictions = self.model.predict(X)
                probabilities = self.model.predict_proba(X)
                return predictions, probabilities
            else:
                predictions = self.model.predict(X)
                return predictions
                
        except Exception as e:
            self.logger.error(f"Error making XGBoost predictions: {e}")
            raise PredictionError(f"XGBoost prediction failed: {e}")
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance scores from the trained model.
        
        Returns:
            Dictionary mapping feature names to importance scores
        """
        if not self.is_trained or not hasattr(self.model, 'feature_importances_'):
            return {}
        
        importance_scores = self.model.feature_importances_
        feature_names = self.feature_columns or [f"feature_{i}" for i in range(len(importance_scores))]
        
        return dict(zip(feature_names, importance_scores))
    
    def get_shap_values(self, X: pd.DataFrame, max_samples: int = 1000) -> Optional[np.ndarray]:
        """
        Get SHAP values for model interpretability.
        
        Args:
            X: Features to explain
            max_samples: Maximum number of samples to use for SHAP
            
        Returns:
            SHAP values array or None if SHAP not available
        """
        if not self.is_trained:
            return None
        
        try:
            import shap
            
            # Validate features
            X = self.validate_features(X)
            
            # Limit samples for performance
            if len(X) > max_samples:
                X = X.sample(n=max_samples, random_state=42)
            
            # Create SHAP explainer
            explainer = shap.TreeExplainer(self.model)
            shap_values = explainer.shap_values(X)
            
            return shap_values
            
        except ImportError:
            self.logger.warning("SHAP not available for model interpretability")
            return None
        except Exception as e:
            self.logger.warning(f"Error calculating SHAP values: {e}")
            return None
    
    async def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray) -> Dict[str, float]:
        """Calculate performance metrics."""
        metrics = {}
        
        try:
            from sklearn.metrics import (
                accuracy_score, precision_score, recall_score, f1_score,
                roc_auc_score, mean_squared_error, mean_absolute_error, r2_score
            )
            
            if self.model_type == "classifier":
                metrics["accuracy"] = accuracy_score(y_true, y_pred)
                metrics["precision"] = precision_score(y_true, y_pred, average="weighted", zero_division=0)
                metrics["recall"] = recall_score(y_true, y_pred, average="weighted", zero_division=0)
                metrics["f1"] = f1_score(y_true, y_pred, average="weighted", zero_division=0)
                
                # ROC AUC for binary classification
                if len(np.unique(y_true)) == 2:
                    try:
                        y_prob = self.model.predict_proba(self.validate_features(pd.DataFrame(index=y_true.index, columns=self.feature_columns)))[:, 1]
                        metrics["roc_auc"] = roc_auc_score(y_true, y_prob)
                    except:
                        pass
            else:
                metrics["mse"] = mean_squared_error(y_true, y_pred)
                metrics["mae"] = mean_absolute_error(y_true, y_pred)
                metrics["r2"] = r2_score(y_true, y_pred)
                metrics["rmse"] = np.sqrt(metrics["mse"])
        
        except ImportError:
            self.logger.warning("scikit-learn not available for metric calculation")
        except Exception as e:
            self.logger.warning(f"Error calculating metrics: {e}")
        
        return metrics
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive XGBoost model information."""
        base_info = super().get_model_info()
        
        xgb_info = {
            "early_stopping_rounds": self.early_stopping_rounds,
            "eval_metric": self.eval_metric,
            "best_iteration": self.best_iteration,
            "training_log": self.training_log[-5:] if self.training_log else [],  # Last 5 entries
        }
        
        base_info.update(xgb_info)
        return base_info
