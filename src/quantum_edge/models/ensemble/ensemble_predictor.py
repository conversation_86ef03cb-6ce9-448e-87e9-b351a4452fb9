"""
Ensemble Predictor for QuantumEdge system.
Combines multiple models using sophisticated ensemble techniques.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union, Tuple
import numpy as np
import pandas as pd
from datetime import datetime
import warnings

from ..base_model import BaseModel, ModelError, TrainingError, PredictionError
from ...config.logging_config import LoggerMixin


class EnsemblePredictor(BaseModel):
    """
    Advanced ensemble predictor that combines multiple models.
    Supports various ensemble methods including voting, stacking, and blending.
    """
    
    def __init__(
        self,
        models: List[BaseModel],
        ensemble_method: str = "weighted_voting",
        model_weights: Optional[Dict[str, float]] = None,
        meta_model: Optional[BaseModel] = None,
        feature_columns: Optional[List[str]] = None,
        target_column: str = "target"
    ):
        """
        Initialize ensemble predictor.
        
        Args:
            models: List of base models to ensemble
            ensemble_method: Method for combining predictions (voting, stacking, blending)
            model_weights: Weights for each model in voting
            meta_model: Meta-model for stacking
            feature_columns: List of feature column names
            target_column: Name of target column
        """
        super().__init__(
            model_name="EnsemblePredictor",
            model_type="ensemble",
            feature_columns=feature_columns,
            target_column=target_column
        )
        
        self.models = {model.model_name: model for model in models}
        self.ensemble_method = ensemble_method
        self.model_weights = model_weights or {}
        self.meta_model = meta_model
        
        # Ensemble state
        self.model_performance = {}
        self.ensemble_metrics = {}
        
        # Validate ensemble method
        valid_methods = ["simple_voting", "weighted_voting", "stacking", "blending"]
        if ensemble_method not in valid_methods:
            raise ModelError(f"Invalid ensemble method. Must be one of: {valid_methods}")
        
        # For stacking, require meta-model
        if ensemble_method == "stacking" and meta_model is None:
            raise ModelError("Stacking ensemble method requires a meta-model")
        
        self.logger.info(f"Initialized ensemble with {len(models)} models using {ensemble_method}")
    
    async def train(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        validation_data: Optional[Tuple[pd.DataFrame, pd.Series]] = None,
        cross_validation_folds: int = 5,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Train the ensemble model.
        
        Args:
            X: Training features
            y: Training targets
            validation_data: Optional validation data
            cross_validation_folds: Number of CV folds for stacking/blending
            **kwargs: Additional training parameters
            
        Returns:
            Dictionary with training results and metrics
        """
        try:
            self.logger.info(f"Training ensemble with {len(self.models)} models")
            
            # Validate inputs
            X = self.validate_features(X)
            y = self.validate_targets(y)
            
            # Store feature columns if not set
            if not self.feature_columns:
                self.feature_columns = X.columns.tolist()
            
            training_results = {}
            
            # Train base models
            for model_name, model in self.models.items():
                try:
                    self.logger.info(f"Training base model: {model_name}")
                    
                    # Train the model
                    model_results = await model.train(X, y, validation_data, **kwargs)
                    training_results[model_name] = model_results
                    
                    # Evaluate model performance
                    if validation_data:
                        X_val, y_val = validation_data
                        model_metrics = await model.evaluate(X_val, y_val)
                        self.model_performance[model_name] = model_metrics
                    
                    self.logger.info(f"Completed training: {model_name}")
                    
                except Exception as e:
                    self.logger.error(f"Error training model {model_name}: {e}")
                    training_results[model_name] = {"error": str(e)}
            
            # Calculate model weights based on performance
            if self.ensemble_method == "weighted_voting" and not self.model_weights:
                self._calculate_model_weights()
            
            # Train meta-model for stacking
            if self.ensemble_method == "stacking":
                await self._train_meta_model(X, y, cross_validation_folds)
            
            # Mark as trained
            self.is_trained = True
            self.last_trained_at = datetime.now()
            
            # Calculate ensemble metrics
            if validation_data:
                ensemble_metrics = await self.evaluate(validation_data[0], validation_data[1])
                self.ensemble_metrics = ensemble_metrics
                training_results["ensemble_metrics"] = ensemble_metrics
            
            # Store training history
            self.training_history.append({
                "timestamp": datetime.now().isoformat(),
                "training_samples": len(X),
                "validation_samples": len(validation_data[0]) if validation_data else 0,
                "models_trained": len([r for r in training_results.values() if "error" not in r]),
                "ensemble_method": self.ensemble_method,
                "results": training_results
            })
            
            self.logger.info("Ensemble training completed successfully")
            return training_results
            
        except Exception as e:
            self.logger.error(f"Error training ensemble: {e}")
            raise TrainingError(f"Ensemble training failed: {e}")
    
    async def predict(
        self,
        X: pd.DataFrame,
        return_probabilities: bool = False,
        return_individual_predictions: bool = False,
        **kwargs
    ) -> Union[np.ndarray, Tuple[np.ndarray, np.ndarray], Dict[str, Any]]:
        """
        Make ensemble predictions.
        
        Args:
            X: Features for prediction
            return_probabilities: Whether to return prediction probabilities
            return_individual_predictions: Whether to return individual model predictions
            **kwargs: Additional prediction parameters
            
        Returns:
            Ensemble predictions, optionally with probabilities and individual predictions
        """
        if not self.is_trained:
            raise PredictionError("Ensemble must be trained before making predictions")
        
        try:
            # Validate features
            X = self.validate_features(X)
            
            # Get predictions from all models
            individual_predictions = {}
            individual_probabilities = {}
            
            for model_name, model in self.models.items():
                if not model.is_trained:
                    self.logger.warning(f"Model {model_name} is not trained, skipping")
                    continue
                
                try:
                    if return_probabilities and hasattr(model, 'predict_proba'):
                        pred_result = await model.predict(X, return_probabilities=True)
                        if isinstance(pred_result, tuple):
                            predictions, probabilities = pred_result
                            individual_predictions[model_name] = predictions
                            individual_probabilities[model_name] = probabilities
                        else:
                            individual_predictions[model_name] = pred_result
                    else:
                        predictions = await model.predict(X, **kwargs)
                        individual_predictions[model_name] = predictions
                
                except Exception as e:
                    self.logger.warning(f"Error getting predictions from {model_name}: {e}")
                    continue
            
            if not individual_predictions:
                raise PredictionError("No models available for prediction")
            
            # Combine predictions using ensemble method
            ensemble_predictions = self._combine_predictions(individual_predictions, X)
            
            # Combine probabilities if requested
            ensemble_probabilities = None
            if return_probabilities and individual_probabilities:
                ensemble_probabilities = self._combine_probabilities(individual_probabilities)
            
            # Prepare return value
            if return_individual_predictions:
                result = {
                    "ensemble_predictions": ensemble_predictions,
                    "individual_predictions": individual_predictions
                }
                if ensemble_probabilities is not None:
                    result["ensemble_probabilities"] = ensemble_probabilities
                    result["individual_probabilities"] = individual_probabilities
                return result
            
            elif return_probabilities and ensemble_probabilities is not None:
                return ensemble_predictions, ensemble_probabilities
            
            else:
                return ensemble_predictions
                
        except Exception as e:
            self.logger.error(f"Error making ensemble predictions: {e}")
            raise PredictionError(f"Ensemble prediction failed: {e}")
    
    def _combine_predictions(self, predictions: Dict[str, np.ndarray], X: pd.DataFrame) -> np.ndarray:
        """Combine individual model predictions using the specified ensemble method."""
        
        if self.ensemble_method == "simple_voting":
            # Simple majority voting
            pred_array = np.array(list(predictions.values()))
            if self.model_type == "classifier":
                # Mode for classification
                from scipy import stats
                ensemble_pred = stats.mode(pred_array, axis=0)[0].flatten()
            else:
                # Mean for regression
                ensemble_pred = np.mean(pred_array, axis=0)
        
        elif self.ensemble_method == "weighted_voting":
            # Weighted voting based on model performance
            weighted_sum = np.zeros(len(list(predictions.values())[0]))
            total_weight = 0
            
            for model_name, pred in predictions.items():
                weight = self.model_weights.get(model_name, 1.0)
                weighted_sum += weight * pred
                total_weight += weight
            
            ensemble_pred = weighted_sum / total_weight if total_weight > 0 else weighted_sum
        
        elif self.ensemble_method == "stacking":
            # Use meta-model predictions
            if self.meta_model and self.meta_model.is_trained:
                # Create meta-features from base model predictions
                meta_features = pd.DataFrame(predictions)
                # Note: For now, use synchronous prediction for meta-model
                # TODO: Make this async-compatible
                if hasattr(self.meta_model, 'model') and hasattr(self.meta_model.model, 'predict'):
                    ensemble_pred = self.meta_model.model.predict(meta_features)
                else:
                    # Fallback to weighted voting
                    self.logger.warning("Meta-model predict method not available, falling back to weighted voting")
                    return self._combine_predictions(predictions, X)
            else:
                # Fallback to weighted voting
                self.logger.warning("Meta-model not available, falling back to weighted voting")
                return self._combine_predictions(predictions, X)
        
        elif self.ensemble_method == "blending":
            # Simple blending (average of predictions)
            pred_array = np.array(list(predictions.values()))
            ensemble_pred = np.mean(pred_array, axis=0)
        
        else:
            raise ModelError(f"Unknown ensemble method: {self.ensemble_method}")
        
        return ensemble_pred
    
    def _combine_probabilities(self, probabilities: Dict[str, np.ndarray]) -> np.ndarray:
        """Combine prediction probabilities from individual models."""
        if self.ensemble_method == "weighted_voting":
            # Weighted average of probabilities
            weighted_sum = None
            total_weight = 0
            
            for model_name, prob in probabilities.items():
                weight = self.model_weights.get(model_name, 1.0)
                if weighted_sum is None:
                    weighted_sum = weight * prob
                else:
                    weighted_sum += weight * prob
                total_weight += weight
            
            return weighted_sum / total_weight if total_weight > 0 else weighted_sum
        
        else:
            # Simple average for other methods
            prob_array = np.array(list(probabilities.values()))
            return np.mean(prob_array, axis=0)
    
    def _calculate_model_weights(self):
        """Calculate model weights based on validation performance."""
        if not self.model_performance:
            # Equal weights if no performance data
            self.model_weights = {name: 1.0 for name in self.models.keys()}
            return
        
        # Use primary metric for weighting
        primary_metric = "f1" if self.model_type == "classifier" else "r2"
        
        weights = {}
        for model_name, metrics in self.model_performance.items():
            if primary_metric in metrics:
                # Higher performance = higher weight
                weights[model_name] = max(metrics[primary_metric], 0.1)  # Minimum weight
            else:
                weights[model_name] = 1.0
        
        # Normalize weights
        total_weight = sum(weights.values())
        self.model_weights = {name: weight / total_weight for name, weight in weights.items()}
        
        self.logger.info(f"Calculated model weights: {self.model_weights}")
    
    async def _train_meta_model(self, X: pd.DataFrame, y: pd.Series, cv_folds: int = 5):
        """Train meta-model for stacking ensemble."""
        if not self.meta_model:
            return
        
        try:
            from sklearn.model_selection import KFold
            
            # Generate meta-features using cross-validation
            kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
            meta_features = []
            meta_targets = []
            
            for train_idx, val_idx in kf.split(X):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
                
                # Train base models on fold training data
                fold_predictions = {}
                for model_name, model in self.models.items():
                    try:
                        # Create a copy of the model for this fold
                        fold_model = type(model)(**model.model_params)
                        await fold_model.train(X_train, y_train)
                        
                        # Get predictions on validation fold
                        val_predictions = await fold_model.predict(X_val)
                        fold_predictions[model_name] = val_predictions
                        
                    except Exception as e:
                        self.logger.warning(f"Error in fold training for {model_name}: {e}")
                        continue
                
                if fold_predictions:
                    # Create meta-features for this fold
                    fold_meta_features = pd.DataFrame(fold_predictions)
                    meta_features.append(fold_meta_features)
                    meta_targets.append(y_val)
            
            if meta_features:
                # Combine all folds
                X_meta = pd.concat(meta_features, ignore_index=True)
                y_meta = pd.concat(meta_targets, ignore_index=True)
                
                # Train meta-model
                await self.meta_model.train(X_meta, y_meta)
                self.logger.info("Meta-model training completed")
            
        except ImportError:
            self.logger.warning("scikit-learn not available for cross-validation")
        except Exception as e:
            self.logger.error(f"Error training meta-model: {e}")
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get aggregated feature importance from all models.
        
        Returns:
            Dictionary mapping feature names to aggregated importance scores
        """
        if not self.is_trained:
            return {}
        
        # Aggregate feature importance from all models
        aggregated_importance = {}
        model_count = 0
        
        for model_name, model in self.models.items():
            if model.is_trained:
                try:
                    importance = model.get_feature_importance()
                    weight = self.model_weights.get(model_name, 1.0)
                    
                    for feature, score in importance.items():
                        if feature not in aggregated_importance:
                            aggregated_importance[feature] = 0
                        aggregated_importance[feature] += weight * score
                    
                    model_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"Error getting feature importance from {model_name}: {e}")
        
        # Normalize by number of models
        if model_count > 0:
            aggregated_importance = {
                feature: score / model_count 
                for feature, score in aggregated_importance.items()
            }
        
        return aggregated_importance
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive ensemble information."""
        base_info = super().get_model_info()
        
        ensemble_info = {
            "ensemble_method": self.ensemble_method,
            "base_models": {name: model.get_model_info() for name, model in self.models.items()},
            "model_weights": self.model_weights,
            "model_performance": self.model_performance,
            "ensemble_metrics": self.ensemble_metrics,
            "meta_model": self.meta_model.get_model_info() if self.meta_model else None
        }
        
        base_info.update(ensemble_info)
        return base_info
