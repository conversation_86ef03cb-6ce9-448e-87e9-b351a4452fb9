"""
Technical Indicators for QuantumEdge system.
Comprehensive technical analysis indicators for stock prediction.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Union
from ..config.logging_config import LoggerMixin


class TechnicalIndicators(LoggerMixin):
    """
    Comprehensive technical indicators for financial analysis.
    Includes momentum, trend, volatility, and volume indicators.
    """
    
    def __init__(self):
        """Initialize technical indicators calculator."""
        self.logger.info("Technical indicators calculator initialized")
    
    def calculate_all_indicators(
        self,
        data: pd.DataFrame,
        price_columns: Dict[str, str] = None,
        volume_column: str = "volume"
    ) -> pd.DataFrame:
        """
        Calculate all technical indicators.
        
        Args:
            data: OHLCV data
            price_columns: Mapping of price column names
            volume_column: Volume column name
            
        Returns:
            DataFrame with all technical indicators
        """
        if price_columns is None:
            price_columns = {
                "open": "open",
                "high": "high", 
                "low": "low",
                "close": "close"
            }
        
        indicators = pd.DataFrame(index=data.index)
        
        # Price data
        open_price = data[price_columns["open"]]
        high_price = data[price_columns["high"]]
        low_price = data[price_columns["low"]]
        close_price = data[price_columns["close"]]
        volume = data[volume_column] if volume_column in data.columns else None
        
        # 1. Momentum Indicators
        momentum_indicators = self.calculate_momentum_indicators(
            close_price, high_price, low_price, volume
        )
        indicators = pd.concat([indicators, momentum_indicators], axis=1)
        
        # 2. Trend Indicators
        trend_indicators = self.calculate_trend_indicators(
            close_price, high_price, low_price
        )
        indicators = pd.concat([indicators, trend_indicators], axis=1)
        
        # 3. Volatility Indicators
        volatility_indicators = self.calculate_volatility_indicators(
            close_price, high_price, low_price
        )
        indicators = pd.concat([indicators, volatility_indicators], axis=1)
        
        # 4. Volume Indicators
        if volume is not None:
            volume_indicators = self.calculate_volume_indicators(
                close_price, volume
            )
            indicators = pd.concat([indicators, volume_indicators], axis=1)
        
        # 5. Price Pattern Indicators
        pattern_indicators = self.calculate_pattern_indicators(
            open_price, high_price, low_price, close_price
        )
        indicators = pd.concat([indicators, pattern_indicators], axis=1)
        
        return indicators.ffill().fillna(0)
    
    def calculate_momentum_indicators(
        self,
        close: pd.Series,
        high: pd.Series,
        low: pd.Series,
        volume: Optional[pd.Series] = None
    ) -> pd.DataFrame:
        """Calculate momentum indicators."""
        indicators = pd.DataFrame(index=close.index)
        
        # RSI (Relative Strength Index)
        for period in [14, 21, 50]:
            indicators[f"rsi_{period}"] = self._calculate_rsi(close, period)
        
        # Stochastic Oscillator
        for period in [14, 21]:
            stoch_k, stoch_d = self._calculate_stochastic(high, low, close, period)
            indicators[f"stoch_k_{period}"] = stoch_k
            indicators[f"stoch_d_{period}"] = stoch_d
        
        # Williams %R
        for period in [14, 21]:
            indicators[f"williams_r_{period}"] = self._calculate_williams_r(high, low, close, period)
        
        # Rate of Change (ROC)
        for period in [5, 10, 20]:
            indicators[f"roc_{period}"] = close.pct_change(period) * 100
        
        # Momentum
        for period in [5, 10, 20]:
            indicators[f"momentum_{period}"] = close / close.shift(period) - 1
        
        # Money Flow Index (if volume available)
        if volume is not None:
            indicators["mfi_14"] = self._calculate_mfi(high, low, close, volume, 14)
        
        return indicators
    
    def calculate_trend_indicators(
        self,
        close: pd.Series,
        high: pd.Series,
        low: pd.Series
    ) -> pd.DataFrame:
        """Calculate trend indicators."""
        indicators = pd.DataFrame(index=close.index)
        
        # Moving Averages
        for period in [5, 10, 20, 50, 100, 200]:
            indicators[f"sma_{period}"] = close.rolling(period).mean()
            indicators[f"ema_{period}"] = close.ewm(span=period).mean()
        
        # Moving Average Convergence Divergence (MACD)
        macd_line, macd_signal, macd_histogram = self._calculate_macd(close)
        indicators["macd_line"] = macd_line
        indicators["macd_signal"] = macd_signal
        indicators["macd_histogram"] = macd_histogram
        
        # Average Directional Index (ADX)
        indicators["adx_14"] = self._calculate_adx(high, low, close, 14)
        
        # Parabolic SAR
        indicators["parabolic_sar"] = self._calculate_parabolic_sar(high, low, close)
        
        # Ichimoku Cloud components
        ichimoku = self._calculate_ichimoku(high, low, close)
        indicators.update(ichimoku)
        
        return indicators
    
    def calculate_volatility_indicators(
        self,
        close: pd.Series,
        high: pd.Series,
        low: pd.Series
    ) -> pd.DataFrame:
        """Calculate volatility indicators."""
        indicators = pd.DataFrame(index=close.index)
        
        # Bollinger Bands
        for period in [20, 50]:
            bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(close, period)
            indicators[f"bb_upper_{period}"] = bb_upper
            indicators[f"bb_middle_{period}"] = bb_middle
            indicators[f"bb_lower_{period}"] = bb_lower
            indicators[f"bb_width_{period}"] = (bb_upper - bb_lower) / bb_middle
            indicators[f"bb_position_{period}"] = (close - bb_lower) / (bb_upper - bb_lower)
        
        # Average True Range (ATR)
        for period in [14, 21]:
            indicators[f"atr_{period}"] = self._calculate_atr(high, low, close, period)
        
        # Historical Volatility
        for period in [10, 20, 50]:
            returns = close.pct_change()
            indicators[f"volatility_{period}"] = returns.rolling(period).std() * np.sqrt(252)
        
        # Keltner Channels
        kc_upper, kc_middle, kc_lower = self._calculate_keltner_channels(high, low, close)
        indicators["kc_upper"] = kc_upper
        indicators["kc_middle"] = kc_middle
        indicators["kc_lower"] = kc_lower
        
        return indicators
    
    def calculate_volume_indicators(
        self,
        close: pd.Series,
        volume: pd.Series
    ) -> pd.DataFrame:
        """Calculate volume indicators."""
        indicators = pd.DataFrame(index=close.index)
        
        # On-Balance Volume (OBV)
        indicators["obv"] = self._calculate_obv(close, volume)
        
        # Volume Price Trend (VPT)
        indicators["vpt"] = self._calculate_vpt(close, volume)
        
        # Accumulation/Distribution Line
        indicators["ad_line"] = self._calculate_ad_line(close, volume)
        
        # Volume Moving Averages
        for period in [10, 20, 50]:
            indicators[f"volume_sma_{period}"] = volume.rolling(period).mean()
            indicators[f"volume_ratio_{period}"] = volume / indicators[f"volume_sma_{period}"]
        
        # Volume Rate of Change
        for period in [5, 10]:
            indicators[f"volume_roc_{period}"] = volume.pct_change(period)
        
        return indicators
    
    def calculate_pattern_indicators(
        self,
        open_price: pd.Series,
        high: pd.Series,
        low: pd.Series,
        close: pd.Series
    ) -> pd.DataFrame:
        """Calculate price pattern indicators."""
        indicators = pd.DataFrame(index=close.index)
        
        # Candlestick patterns
        indicators["doji"] = self._is_doji(open_price, close, high, low)
        indicators["hammer"] = self._is_hammer(open_price, close, high, low)
        indicators["shooting_star"] = self._is_shooting_star(open_price, close, high, low)
        indicators["engulfing_bullish"] = self._is_engulfing_bullish(open_price, close)
        indicators["engulfing_bearish"] = self._is_engulfing_bearish(open_price, close)
        
        # Gap analysis
        indicators["gap_up"] = (open_price > close.shift(1)).astype(int)
        indicators["gap_down"] = (open_price < close.shift(1)).astype(int)
        indicators["gap_size"] = (open_price - close.shift(1)) / close.shift(1)
        
        # Price position within range
        indicators["price_position"] = (close - low) / (high - low + 1e-8)
        
        # Daily range
        indicators["daily_range"] = (high - low) / close
        
        return indicators
    
    # Helper methods for indicator calculations
    def _calculate_rsi(self, close: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI."""
        delta = close.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_stochastic(
        self,
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        period: int = 14
    ) -> tuple:
        """Calculate Stochastic Oscillator."""
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=3).mean()
        return k_percent, d_percent
    
    def _calculate_williams_r(
        self,
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        period: int = 14
    ) -> pd.Series:
        """Calculate Williams %R."""
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        return -100 * ((highest_high - close) / (highest_high - lowest_low))
    
    def _calculate_mfi(
        self,
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        volume: pd.Series,
        period: int = 14
    ) -> pd.Series:
        """Calculate Money Flow Index."""
        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume
        
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0)
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0)
        
        positive_mf = positive_flow.rolling(window=period).sum()
        negative_mf = negative_flow.rolling(window=period).sum()
        
        mfr = positive_mf / negative_mf
        return 100 - (100 / (1 + mfr))
    
    def _calculate_macd(
        self,
        close: pd.Series,
        fast: int = 12,
        slow: int = 26,
        signal: int = 9
    ) -> tuple:
        """Calculate MACD."""
        ema_fast = close.ewm(span=fast).mean()
        ema_slow = close.ewm(span=slow).mean()
        macd_line = ema_fast - ema_slow
        macd_signal = macd_line.ewm(span=signal).mean()
        macd_histogram = macd_line - macd_signal
        return macd_line, macd_signal, macd_histogram
    
    def _calculate_adx(
        self,
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        period: int = 14
    ) -> pd.Series:
        """Calculate Average Directional Index."""
        # Simplified ADX calculation
        tr = np.maximum(
            high - low,
            np.maximum(
                np.abs(high - close.shift(1)),
                np.abs(low - close.shift(1))
            )
        )
        
        plus_dm = np.where(
            (high - high.shift(1)) > (low.shift(1) - low),
            np.maximum(high - high.shift(1), 0),
            0
        )
        
        minus_dm = np.where(
            (low.shift(1) - low) > (high - high.shift(1)),
            np.maximum(low.shift(1) - low, 0),
            0
        )
        
        tr_smooth = pd.Series(tr).rolling(window=period).mean()
        plus_dm_smooth = pd.Series(plus_dm).rolling(window=period).mean()
        minus_dm_smooth = pd.Series(minus_dm).rolling(window=period).mean()
        
        plus_di = 100 * (plus_dm_smooth / tr_smooth)
        minus_di = 100 * (minus_dm_smooth / tr_smooth)
        
        dx = 100 * np.abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(window=period).mean()
        
        return adx
    
    def _calculate_parabolic_sar(
        self,
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        af_start: float = 0.02,
        af_increment: float = 0.02,
        af_max: float = 0.2
    ) -> pd.Series:
        """Calculate Parabolic SAR (simplified version)."""
        # Simplified implementation
        sar = close.copy()
        trend = 1  # 1 for uptrend, -1 for downtrend
        af = af_start
        ep = high.iloc[0] if trend == 1 else low.iloc[0]
        
        for i in range(1, len(close)):
            if trend == 1:  # Uptrend
                sar.iloc[i] = sar.iloc[i-1] + af * (ep - sar.iloc[i-1])
                if low.iloc[i] <= sar.iloc[i]:
                    trend = -1
                    sar.iloc[i] = ep
                    af = af_start
                    ep = low.iloc[i]
                elif high.iloc[i] > ep:
                    ep = high.iloc[i]
                    af = min(af + af_increment, af_max)
            else:  # Downtrend
                sar.iloc[i] = sar.iloc[i-1] + af * (ep - sar.iloc[i-1])
                if high.iloc[i] >= sar.iloc[i]:
                    trend = 1
                    sar.iloc[i] = ep
                    af = af_start
                    ep = high.iloc[i]
                elif low.iloc[i] < ep:
                    ep = low.iloc[i]
                    af = min(af + af_increment, af_max)
        
        return sar
    
    def _calculate_ichimoku(
        self,
        high: pd.Series,
        low: pd.Series,
        close: pd.Series
    ) -> Dict[str, pd.Series]:
        """Calculate Ichimoku Cloud components."""
        # Tenkan-sen (Conversion Line)
        tenkan_sen = (high.rolling(9).max() + low.rolling(9).min()) / 2
        
        # Kijun-sen (Base Line)
        kijun_sen = (high.rolling(26).max() + low.rolling(26).min()) / 2
        
        # Senkou Span A (Leading Span A)
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(26)
        
        # Senkou Span B (Leading Span B)
        senkou_span_b = ((high.rolling(52).max() + low.rolling(52).min()) / 2).shift(26)
        
        # Chikou Span (Lagging Span)
        chikou_span = close.shift(-26)
        
        return {
            "ichimoku_tenkan": tenkan_sen,
            "ichimoku_kijun": kijun_sen,
            "ichimoku_senkou_a": senkou_span_a,
            "ichimoku_senkou_b": senkou_span_b,
            "ichimoku_chikou": chikou_span
        }
    
    def _calculate_bollinger_bands(
        self,
        close: pd.Series,
        period: int = 20,
        std_dev: float = 2
    ) -> tuple:
        """Calculate Bollinger Bands."""
        middle = close.rolling(window=period).mean()
        std = close.rolling(window=period).std()
        upper = middle + (std * std_dev)
        lower = middle - (std * std_dev)
        return upper, middle, lower
    
    def _calculate_atr(
        self,
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        period: int = 14
    ) -> pd.Series:
        """Calculate Average True Range."""
        tr1 = high - low
        tr2 = np.abs(high - close.shift(1))
        tr3 = np.abs(low - close.shift(1))
        tr = np.maximum(tr1, np.maximum(tr2, tr3))
        return pd.Series(tr).rolling(window=period).mean()
    
    def _calculate_keltner_channels(
        self,
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        period: int = 20,
        multiplier: float = 2
    ) -> tuple:
        """Calculate Keltner Channels."""
        middle = close.ewm(span=period).mean()
        atr = self._calculate_atr(high, low, close, period)
        upper = middle + (multiplier * atr)
        lower = middle - (multiplier * atr)
        return upper, middle, lower
    
    def _calculate_obv(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """Calculate On-Balance Volume."""
        obv = volume.copy()
        obv.iloc[0] = volume.iloc[0]
        
        for i in range(1, len(close)):
            if close.iloc[i] > close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif close.iloc[i] < close.iloc[i-1]:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]
        
        return obv
    
    def _calculate_vpt(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """Calculate Volume Price Trend."""
        vpt = volume * close.pct_change()
        return vpt.cumsum()
    
    def _calculate_ad_line(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """Calculate Accumulation/Distribution Line (simplified)."""
        # Simplified version using close and volume
        clv = close.pct_change()
        ad_line = (clv * volume).cumsum()
        return ad_line
    
    # Candlestick pattern detection methods
    def _is_doji(
        self,
        open_price: pd.Series,
        close: pd.Series,
        high: pd.Series,
        low: pd.Series,
        threshold: float = 0.1
    ) -> pd.Series:
        """Detect Doji candlestick pattern."""
        body_size = np.abs(close - open_price) / (high - low + 1e-8)
        return (body_size < threshold).astype(int)
    
    def _is_hammer(
        self,
        open_price: pd.Series,
        close: pd.Series,
        high: pd.Series,
        low: pd.Series
    ) -> pd.Series:
        """Detect Hammer candlestick pattern."""
        body_size = np.abs(close - open_price)
        lower_shadow = np.minimum(open_price, close) - low
        upper_shadow = high - np.maximum(open_price, close)
        
        is_hammer = (
            (lower_shadow > 2 * body_size) &
            (upper_shadow < 0.5 * body_size)
        )
        return is_hammer.astype(int)
    
    def _is_shooting_star(
        self,
        open_price: pd.Series,
        close: pd.Series,
        high: pd.Series,
        low: pd.Series
    ) -> pd.Series:
        """Detect Shooting Star candlestick pattern."""
        body_size = np.abs(close - open_price)
        lower_shadow = np.minimum(open_price, close) - low
        upper_shadow = high - np.maximum(open_price, close)
        
        is_shooting_star = (
            (upper_shadow > 2 * body_size) &
            (lower_shadow < 0.5 * body_size)
        )
        return is_shooting_star.astype(int)
    
    def _is_engulfing_bullish(
        self,
        open_price: pd.Series,
        close: pd.Series
    ) -> pd.Series:
        """Detect Bullish Engulfing pattern."""
        prev_bearish = close.shift(1) < open_price.shift(1)
        current_bullish = close > open_price
        engulfing = (
            prev_bearish &
            current_bullish &
            (open_price < close.shift(1)) &
            (close > open_price.shift(1))
        )
        return engulfing.astype(int)
    
    def _is_engulfing_bearish(
        self,
        open_price: pd.Series,
        close: pd.Series
    ) -> pd.Series:
        """Detect Bearish Engulfing pattern."""
        prev_bullish = close.shift(1) > open_price.shift(1)
        current_bearish = close < open_price
        engulfing = (
            prev_bullish &
            current_bearish &
            (open_price > close.shift(1)) &
            (close < open_price.shift(1))
        )
        return engulfing.astype(int)
