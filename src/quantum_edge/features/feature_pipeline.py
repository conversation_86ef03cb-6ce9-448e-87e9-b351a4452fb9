"""
Comprehensive Feature Pipeline for QuantumEdge system.
Combines technical indicators, quantum features, and fundamental data.
"""

import asyncio
from typing import Dict, Any, List, Optional, Union, Tuple
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings

from .technical_indicators import TechnicalIndicators
from ..models.quantum.quantum_feature_engineer import QuantumFeatureEngineer
from ..config.logging_config import LoggerMixin


class FeaturePipeline(LoggerMixin):
    """
    Comprehensive feature engineering pipeline.
    Combines technical analysis, quantum features, and fundamental data.
    """
    
    def __init__(
        self,
        include_technical: bool = True,
        include_quantum: bool = True,
        include_fundamental: bool = True,
        include_sentiment: bool = True,
        quantum_qubits: int = 4,
        feature_selection: bool = True,
        max_features: Optional[int] = None
    ):
        """
        Initialize feature pipeline.
        
        Args:
            include_technical: Include technical indicators
            include_quantum: Include quantum features
            include_fundamental: Include fundamental data features
            include_sentiment: Include sentiment features
            quantum_qubits: Number of qubits for quantum features
            feature_selection: Whether to perform feature selection
            max_features: Maximum number of features to select
        """
        self.include_technical = include_technical
        self.include_quantum = include_quantum
        self.include_fundamental = include_fundamental
        self.include_sentiment = include_sentiment
        self.feature_selection = feature_selection
        self.max_features = max_features
        
        # Initialize components
        if self.include_technical:
            self.technical_indicators = TechnicalIndicators()
        
        if self.include_quantum:
            self.quantum_engineer = QuantumFeatureEngineer(n_qubits=quantum_qubits)
        
        # Feature metadata
        self.feature_columns = []
        self.feature_importance = {}
        self.selected_features = []
        
        self.logger.info("Feature pipeline initialized")
    
    async def create_features(
        self,
        market_data: pd.DataFrame,
        fundamental_data: Optional[Dict[str, Any]] = None,
        sentiment_data: Optional[Dict[str, Any]] = None,
        symbol: str = "UNKNOWN"
    ) -> pd.DataFrame:
        """
        Create comprehensive feature set.
        
        Args:
            market_data: OHLCV market data
            fundamental_data: Fundamental analysis data
            sentiment_data: Sentiment analysis data
            symbol: Stock symbol for logging
            
        Returns:
            DataFrame with all engineered features
        """
        self.logger.info(f"Creating features for {symbol}")
        
        features = pd.DataFrame(index=market_data.index)
        
        # 1. Technical Indicators
        if self.include_technical:
            technical_features = await self._create_technical_features(market_data)
            features = pd.concat([features, technical_features], axis=1)
            self.logger.info(f"Added {technical_features.shape[1]} technical features")
        
        # 2. Quantum Features
        if self.include_quantum:
            quantum_features = await self._create_quantum_features(market_data)
            features = pd.concat([features, quantum_features], axis=1)
            self.logger.info(f"Added {quantum_features.shape[1]} quantum features")
        
        # 3. Fundamental Features
        if self.include_fundamental and fundamental_data:
            fundamental_features = await self._create_fundamental_features(
                market_data, fundamental_data
            )
            features = pd.concat([features, fundamental_features], axis=1)
            self.logger.info(f"Added {fundamental_features.shape[1]} fundamental features")
        
        # 4. Sentiment Features
        if self.include_sentiment and sentiment_data:
            sentiment_features = await self._create_sentiment_features(
                market_data, sentiment_data
            )
            features = pd.concat([features, sentiment_features], axis=1)
            self.logger.info(f"Added {sentiment_features.shape[1]} sentiment features")
        
        # 5. Time-based Features
        time_features = await self._create_time_features(market_data)
        features = pd.concat([features, time_features], axis=1)
        self.logger.info(f"Added {time_features.shape[1]} time features")
        
        # 6. Statistical Features
        statistical_features = await self._create_statistical_features(market_data)
        features = pd.concat([features, statistical_features], axis=1)
        self.logger.info(f"Added {statistical_features.shape[1]} statistical features")
        
        # Clean features
        features = self._clean_features(features)
        
        # Feature selection
        if self.feature_selection:
            features = await self._select_features(features, market_data)
        
        self.feature_columns = features.columns.tolist()
        self.logger.info(f"Final feature set: {len(self.feature_columns)} features")
        
        return features
    
    async def _create_technical_features(self, market_data: pd.DataFrame) -> pd.DataFrame:
        """Create technical indicator features."""
        try:
            technical_features = self.technical_indicators.calculate_all_indicators(market_data)
            
            # Add additional technical features
            close = market_data["close"]
            
            # Price ratios
            for period in [5, 10, 20, 50]:
                if f"sma_{period}" in technical_features.columns:
                    technical_features[f"price_to_sma_{period}"] = close / technical_features[f"sma_{period}"]
                if f"ema_{period}" in technical_features.columns:
                    technical_features[f"price_to_ema_{period}"] = close / technical_features[f"ema_{period}"]
            
            # Bollinger Band position
            for period in [20, 50]:
                if all(col in technical_features.columns for col in [f"bb_upper_{period}", f"bb_lower_{period}"]):
                    bb_range = technical_features[f"bb_upper_{period}"] - technical_features[f"bb_lower_{period}"]
                    technical_features[f"bb_squeeze_{period}"] = bb_range / close
            
            # RSI divergence
            if "rsi_14" in technical_features.columns:
                rsi_change = technical_features["rsi_14"].diff()
                price_change = close.pct_change()
                technical_features["rsi_divergence"] = (rsi_change * price_change < 0).astype(int)
            
            return technical_features
            
        except Exception as e:
            self.logger.error(f"Error creating technical features: {e}")
            return pd.DataFrame(index=market_data.index)
    
    async def _create_quantum_features(self, market_data: pd.DataFrame) -> pd.DataFrame:
        """Create quantum-enhanced features."""
        try:
            # Basic quantum features
            quantum_features = await self.quantum_engineer.create_quantum_features(market_data)
            
            # Quantum momentum features
            momentum_features = self.quantum_engineer.create_quantum_momentum_features(market_data)
            quantum_features = pd.concat([quantum_features, momentum_features], axis=1)
            
            return quantum_features
            
        except Exception as e:
            self.logger.error(f"Error creating quantum features: {e}")
            return pd.DataFrame(index=market_data.index)
    
    async def _create_fundamental_features(
        self,
        market_data: pd.DataFrame,
        fundamental_data: Dict[str, Any]
    ) -> pd.DataFrame:
        """Create fundamental analysis features."""
        features = pd.DataFrame(index=market_data.index)
        
        try:
            # Extract key fundamental metrics
            market_cap = fundamental_data.get("market_cap")
            pe_ratio = fundamental_data.get("pe_ratio")
            book_value = fundamental_data.get("book_value")
            eps = fundamental_data.get("eps")
            revenue_ttm = fundamental_data.get("revenue_ttm")
            
            # Create fundamental features (broadcast to all time periods)
            if market_cap:
                features["market_cap_log"] = np.log(market_cap + 1)
                features["market_cap_category"] = self._categorize_market_cap(market_cap)
            
            if pe_ratio and pe_ratio > 0:
                features["pe_ratio"] = pe_ratio
                features["pe_ratio_log"] = np.log(pe_ratio)
                features["pe_ratio_category"] = self._categorize_pe_ratio(pe_ratio)
            
            if book_value and book_value > 0:
                close = market_data["close"]
                features["price_to_book"] = close / book_value
            
            if eps and eps != 0:
                close = market_data["close"]
                features["price_to_earnings"] = close / eps
            
            # Fundamental strength score
            strength_score = 0
            if pe_ratio and 5 < pe_ratio < 25:
                strength_score += 1
            if book_value and book_value > 0:
                strength_score += 1
            if eps and eps > 0:
                strength_score += 1
            if revenue_ttm and revenue_ttm > 0:
                strength_score += 1
            
            features["fundamental_strength"] = strength_score
            
            return features
            
        except Exception as e:
            self.logger.error(f"Error creating fundamental features: {e}")
            return pd.DataFrame(index=market_data.index)
    
    async def _create_sentiment_features(
        self,
        market_data: pd.DataFrame,
        sentiment_data: Dict[str, Any]
    ) -> pd.DataFrame:
        """Create sentiment analysis features."""
        features = pd.DataFrame(index=market_data.index)
        
        try:
            # Extract sentiment metrics
            sentiment_score = sentiment_data.get("sentiment_score", 0)
            article_count = sentiment_data.get("article_count", 0)
            positive_count = sentiment_data.get("positive_count", 0)
            negative_count = sentiment_data.get("negative_count", 0)
            
            # Create sentiment features
            features["sentiment_score"] = sentiment_score
            features["sentiment_magnitude"] = abs(sentiment_score)
            features["sentiment_direction"] = np.sign(sentiment_score)
            
            if article_count > 0:
                features["news_volume"] = article_count
                features["news_volume_log"] = np.log(article_count + 1)
                features["positive_ratio"] = positive_count / article_count
                features["negative_ratio"] = negative_count / article_count
            else:
                features["news_volume"] = 0
                features["news_volume_log"] = 0
                features["positive_ratio"] = 0
                features["negative_ratio"] = 0
            
            # Sentiment momentum (change over time)
            features["sentiment_momentum"] = features["sentiment_score"].diff()
            features["sentiment_acceleration"] = features["sentiment_momentum"].diff()
            
            return features
            
        except Exception as e:
            self.logger.error(f"Error creating sentiment features: {e}")
            return pd.DataFrame(index=market_data.index)
    
    async def _create_time_features(self, market_data: pd.DataFrame) -> pd.DataFrame:
        """Create time-based features."""
        features = pd.DataFrame(index=market_data.index)
        
        try:
            # Extract time components
            features["day_of_week"] = market_data.index.dayofweek
            features["day_of_month"] = market_data.index.day
            features["month"] = market_data.index.month
            features["quarter"] = market_data.index.quarter
            
            # Market timing features
            features["is_monday"] = (features["day_of_week"] == 0).astype(int)
            features["is_friday"] = (features["day_of_week"] == 4).astype(int)
            features["is_month_end"] = (features["day_of_month"] > 25).astype(int)
            features["is_quarter_end"] = features["month"].isin([3, 6, 9, 12]).astype(int)
            
            # Cyclical encoding
            features["day_sin"] = np.sin(2 * np.pi * features["day_of_week"] / 7)
            features["day_cos"] = np.cos(2 * np.pi * features["day_of_week"] / 7)
            features["month_sin"] = np.sin(2 * np.pi * features["month"] / 12)
            features["month_cos"] = np.cos(2 * np.pi * features["month"] / 12)
            
            return features
            
        except Exception as e:
            self.logger.error(f"Error creating time features: {e}")
            return pd.DataFrame(index=market_data.index)
    
    async def _create_statistical_features(self, market_data: pd.DataFrame) -> pd.DataFrame:
        """Create statistical features."""
        features = pd.DataFrame(index=market_data.index)
        
        try:
            close = market_data["close"]
            volume = market_data.get("volume")
            
            # Returns and log returns
            returns = close.pct_change()
            features["returns"] = returns
            features["log_returns"] = np.log(close / close.shift(1))
            
            # Rolling statistics
            for window in [5, 10, 20, 50]:
                features[f"returns_mean_{window}"] = returns.rolling(window).mean()
                features[f"returns_std_{window}"] = returns.rolling(window).std()
                features[f"returns_skew_{window}"] = returns.rolling(window).skew()
                features[f"returns_kurt_{window}"] = returns.rolling(window).kurt()
                
                # Z-score
                mean = returns.rolling(window).mean()
                std = returns.rolling(window).std()
                features[f"returns_zscore_{window}"] = (returns - mean) / (std + 1e-8)
            
            # Price statistics
            for window in [10, 20, 50]:
                features[f"price_percentile_{window}"] = close.rolling(window).rank(pct=True)
                features[f"price_range_{window}"] = (close - close.rolling(window).min()) / (
                    close.rolling(window).max() - close.rolling(window).min() + 1e-8
                )
            
            # Volume statistics (if available)
            if volume is not None:
                for window in [5, 10, 20]:
                    features[f"volume_zscore_{window}"] = (
                        volume - volume.rolling(window).mean()
                    ) / (volume.rolling(window).std() + 1e-8)
            
            return features
            
        except Exception as e:
            self.logger.error(f"Error creating statistical features: {e}")
            return pd.DataFrame(index=market_data.index)
    
    def _clean_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """Clean and preprocess features."""
        # Remove infinite values
        features = features.replace([np.inf, -np.inf], np.nan)
        
        # Forward fill and then fill remaining NaNs with 0
        features = features.ffill().fillna(0)
        
        # Remove constant features
        constant_features = features.columns[features.nunique() <= 1].tolist()
        if constant_features:
            features = features.drop(columns=constant_features)
            self.logger.info(f"Removed {len(constant_features)} constant features")
        
        # Remove highly correlated features
        if len(features.columns) > 1:
            correlation_matrix = features.corr().abs()
            upper_triangle = correlation_matrix.where(
                np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool)
            )
            
            high_corr_features = [
                column for column in upper_triangle.columns 
                if any(upper_triangle[column] > 0.95)
            ]
            
            if high_corr_features:
                features = features.drop(columns=high_corr_features)
                self.logger.info(f"Removed {len(high_corr_features)} highly correlated features")
        
        return features
    
    async def _select_features(
        self,
        features: pd.DataFrame,
        market_data: pd.DataFrame
    ) -> pd.DataFrame:
        """Perform feature selection."""
        if self.max_features and len(features.columns) <= self.max_features:
            return features
        
        try:
            # Create target for feature selection (next day return > 0)
            close = market_data["close"]
            target = (close.shift(-1) > close).astype(int)
            
            # Align features and target
            aligned_data = pd.concat([features, target.rename("target")], axis=1).dropna()
            
            if len(aligned_data) < 50:  # Not enough data for feature selection
                return features
            
            X = aligned_data.drop("target", axis=1)
            y = aligned_data["target"]
            
            # Use mutual information for feature selection
            from sklearn.feature_selection import mutual_info_classif, SelectKBest
            
            n_features = self.max_features or min(50, len(X.columns))
            selector = SelectKBest(score_func=mutual_info_classif, k=n_features)
            
            X_selected = selector.fit_transform(X, y)
            selected_feature_names = X.columns[selector.get_support()].tolist()
            
            self.selected_features = selected_feature_names
            self.logger.info(f"Selected {len(selected_feature_names)} features using mutual information")
            
            return features[selected_feature_names]
            
        except ImportError:
            self.logger.warning("scikit-learn not available for feature selection")
            return features
        except Exception as e:
            self.logger.warning(f"Error in feature selection: {e}")
            return features
    
    def _categorize_market_cap(self, market_cap: float) -> int:
        """Categorize market cap into size categories."""
        if market_cap < 2e9:  # < $2B
            return 0  # Micro cap
        elif market_cap < 10e9:  # < $10B
            return 1  # Small cap
        elif market_cap < 200e9:  # < $200B
            return 2  # Mid cap
        else:
            return 3  # Large cap
    
    def _categorize_pe_ratio(self, pe_ratio: float) -> int:
        """Categorize P/E ratio."""
        if pe_ratio < 0:
            return 0  # Negative earnings
        elif pe_ratio < 15:
            return 1  # Low P/E
        elif pe_ratio < 25:
            return 2  # Moderate P/E
        else:
            return 3  # High P/E
    
    def get_feature_info(self) -> Dict[str, Any]:
        """Get information about the feature pipeline."""
        return {
            "include_technical": self.include_technical,
            "include_quantum": self.include_quantum,
            "include_fundamental": self.include_fundamental,
            "include_sentiment": self.include_sentiment,
            "feature_selection": self.feature_selection,
            "max_features": self.max_features,
            "total_features": len(self.feature_columns),
            "selected_features": len(self.selected_features) if self.selected_features else None,
            "feature_columns": self.feature_columns,
            "quantum_info": self.quantum_engineer.get_feature_info() if self.include_quantum else None
        }
