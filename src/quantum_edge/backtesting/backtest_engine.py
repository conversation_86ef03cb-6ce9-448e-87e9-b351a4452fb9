"""
Backtesting Engine for QuantumEdge microcap discovery system.
Simulates historical performance of the discovery and investment strategy.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..config.logging_config import LoggerMixin
from ..discovery.discovery_engine import DiscoveryEngine
from ..risk.risk_manager import RiskManager
from ..risk.position_sizer import PositionSizer


@dataclass
class BacktestTrade:
    """Represents a single trade in the backtest."""
    symbol: str
    entry_date: datetime
    exit_date: Optional[datetime]
    entry_price: float
    exit_price: Optional[float]
    position_size: float  # Number of shares
    position_value: float  # Dollar value
    growth_score: float
    risk_score: float
    trade_return: Optional[float]  # Return %
    trade_pnl: Optional[float]  # P&L in dollars
    exit_reason: Optional[str]
    days_held: Optional[int]


@dataclass
class BacktestResults:
    """Comprehensive backtest results."""
    start_date: datetime
    end_date: datetime
    initial_capital: float
    final_capital: float
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    avg_win: float
    avg_loss: float
    total_trades: int
    profitable_trades: int
    losing_trades: int
    trades: List[BacktestTrade]
    daily_returns: pd.Series
    equity_curve: pd.Series
    monthly_returns: pd.Series
    sector_performance: Dict[str, float]
    risk_metrics: Dict[str, float]


class BacktestEngine(LoggerMixin):
    """
    Comprehensive backtesting engine for microcap discovery strategy.
    Simulates realistic trading with transaction costs, slippage, and liquidity constraints.
    """
    
    def __init__(
        self,
        discovery_engine: DiscoveryEngine,
        risk_manager: RiskManager,
        position_sizer: PositionSizer,
        initial_capital: float = 100_000,
        transaction_cost: float = 0.005,  # 0.5% transaction cost
        slippage: float = 0.002,  # 0.2% slippage
        max_positions: int = 20,
        rebalance_frequency: int = 30  # Days between rebalances
    ):
        """
        Initialize backtesting engine.
        
        Args:
            discovery_engine: Discovery engine for finding opportunities
            risk_manager: Risk management system
            position_sizer: Position sizing system
            initial_capital: Starting capital
            transaction_cost: Transaction cost as percentage
            slippage: Market impact/slippage as percentage
            max_positions: Maximum number of positions
            rebalance_frequency: Days between portfolio rebalances
        """
        self.discovery_engine = discovery_engine
        self.risk_manager = risk_manager
        self.position_sizer = position_sizer
        self.initial_capital = initial_capital
        self.transaction_cost = transaction_cost
        self.slippage = slippage
        self.max_positions = max_positions
        self.rebalance_frequency = rebalance_frequency
        
        self.logger.info("Backtest engine initialized")
    
    async def run_backtest(
        self,
        start_date: datetime,
        end_date: datetime,
        market_data: Dict[str, pd.DataFrame],
        benchmark_data: Optional[pd.DataFrame] = None
    ) -> BacktestResults:
        """
        Run comprehensive backtest simulation.
        
        Args:
            start_date: Backtest start date
            end_date: Backtest end date
            market_data: Historical market data for all symbols
            benchmark_data: Benchmark data for comparison
            
        Returns:
            BacktestResults object with comprehensive results
        """
        self.logger.info(f"Running backtest from {start_date} to {end_date}")
        
        # Initialize backtest state
        current_date = start_date
        current_capital = self.initial_capital
        current_positions = {}  # symbol -> BacktestTrade
        all_trades = []
        daily_portfolio_values = []
        daily_dates = []
        
        # Generate trading dates
        trading_dates = pd.bdate_range(start_date, end_date, freq='D')
        
        last_rebalance_date = start_date
        
        for current_date in trading_dates:
            try:
                # Check if we need to rebalance
                days_since_rebalance = (current_date - last_rebalance_date).days
                
                if days_since_rebalance >= self.rebalance_frequency or not current_positions:
                    # Run discovery and rebalancing
                    new_opportunities = await self._discover_opportunities(
                        current_date, market_data
                    )
                    
                    # Execute rebalancing
                    current_positions, trades = await self._rebalance_portfolio(
                        current_positions, new_opportunities, current_date, market_data
                    )
                    
                    all_trades.extend(trades)
                    last_rebalance_date = current_date
                
                # Update existing positions and check exit conditions
                current_positions, exit_trades = await self._update_positions(
                    current_positions, current_date, market_data
                )
                
                all_trades.extend(exit_trades)
                
                # Calculate current portfolio value
                portfolio_value = await self._calculate_portfolio_value(
                    current_positions, current_date, market_data
                )
                
                daily_portfolio_values.append(portfolio_value)
                daily_dates.append(current_date)
                
                # Update current capital
                current_capital = portfolio_value
                
            except Exception as e:
                self.logger.error(f"Error on {current_date}: {e}")
                continue
        
        # Calculate final results
        results = await self._calculate_backtest_results(
            start_date, end_date, self.initial_capital, current_capital,
            all_trades, daily_portfolio_values, daily_dates
        )
        
        self.logger.info(f"Backtest completed: {results.total_return:.1%} total return")
        return results
    
    async def _discover_opportunities(
        self,
        current_date: datetime,
        market_data: Dict[str, pd.DataFrame]
    ) -> List[Dict[str, Any]]:
        """Discover investment opportunities at a given date."""
        try:
            # Simulate discovery process with historical data
            # In real backtest, we'd use only data available up to current_date
            
            # Mock discovery results for backtesting
            opportunities = []
            
            # Sample some symbols from available market data
            available_symbols = list(market_data.keys())[:50]  # Limit for performance
            
            for symbol in available_symbols:
                if symbol in market_data:
                    symbol_data = market_data[symbol]
                    
                    # Only use data up to current date
                    historical_data = symbol_data[symbol_data.index <= current_date]
                    
                    if len(historical_data) >= 30:  # Need minimum history
                        # Calculate simple growth score based on momentum
                        recent_returns = historical_data["close"].pct_change(20).iloc[-1]
                        volatility = historical_data["close"].pct_change().std() * np.sqrt(252)
                        
                        # Simple scoring (in real system, use full discovery engine)
                        growth_score = min(100, max(0, 50 + recent_returns * 100))
                        risk_score = min(100, volatility * 100)
                        
                        if growth_score > 60:  # Only consider high-growth opportunities
                            opportunities.append({
                                "symbol": symbol,
                                "growth_score": growth_score,
                                "risk_score": risk_score,
                                "current_price": historical_data["close"].iloc[-1],
                                "market_cap": 500_000_000,  # Mock market cap
                                "sector": "biotech" if symbol.startswith(("A", "B")) else "ai_ml"
                            })
            
            # Sort by growth score and return top opportunities
            opportunities.sort(key=lambda x: x["growth_score"], reverse=True)
            return opportunities[:self.max_positions]
            
        except Exception as e:
            self.logger.error(f"Error discovering opportunities: {e}")
            return []
    
    async def _rebalance_portfolio(
        self,
        current_positions: Dict[str, BacktestTrade],
        new_opportunities: List[Dict[str, Any]],
        current_date: datetime,
        market_data: Dict[str, pd.DataFrame]
    ) -> Tuple[Dict[str, BacktestTrade], List[BacktestTrade]]:
        """Rebalance portfolio based on new opportunities."""
        trades = []
        new_positions = {}
        
        # Close existing positions not in new opportunities
        new_symbols = {opp["symbol"] for opp in new_opportunities}
        
        for symbol, position in current_positions.items():
            if symbol not in new_symbols:
                # Close position
                exit_trade = await self._close_position(position, current_date, market_data, "rebalance")
                if exit_trade:
                    trades.append(exit_trade)
            else:
                # Keep existing position
                new_positions[symbol] = position
        
        # Open new positions
        available_capital = await self._calculate_available_capital(new_positions, current_date, market_data)
        
        for opportunity in new_opportunities:
            symbol = opportunity["symbol"]
            
            if symbol not in new_positions:
                # Open new position
                new_trade = await self._open_position(
                    opportunity, current_date, market_data, available_capital
                )
                
                if new_trade:
                    new_positions[symbol] = new_trade
                    trades.append(new_trade)
        
        return new_positions, trades
    
    async def _open_position(
        self,
        opportunity: Dict[str, Any],
        current_date: datetime,
        market_data: Dict[str, pd.DataFrame],
        available_capital: float
    ) -> Optional[BacktestTrade]:
        """Open a new position."""
        try:
            symbol = opportunity["symbol"]
            current_price = opportunity["current_price"]
            growth_score = opportunity["growth_score"]
            risk_score = opportunity["risk_score"]
            
            # Calculate position size (simplified)
            position_size_pct = min(0.05, max(0.01, growth_score / 2000))  # 1-5% based on score
            position_value = available_capital * position_size_pct
            
            # Apply transaction costs and slippage
            effective_price = current_price * (1 + self.slippage + self.transaction_cost)
            shares = int(position_value / effective_price)
            
            if shares > 0:
                actual_position_value = shares * effective_price
                
                return BacktestTrade(
                    symbol=symbol,
                    entry_date=current_date,
                    exit_date=None,
                    entry_price=effective_price,
                    exit_price=None,
                    position_size=shares,
                    position_value=actual_position_value,
                    growth_score=growth_score,
                    risk_score=risk_score,
                    trade_return=None,
                    trade_pnl=None,
                    exit_reason=None,
                    days_held=None
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error opening position for {opportunity.get('symbol')}: {e}")
            return None
    
    async def _close_position(
        self,
        position: BacktestTrade,
        current_date: datetime,
        market_data: Dict[str, pd.DataFrame],
        exit_reason: str
    ) -> Optional[BacktestTrade]:
        """Close an existing position."""
        try:
            symbol = position.symbol
            
            if symbol not in market_data:
                return None
            
            symbol_data = market_data[symbol]
            historical_data = symbol_data[symbol_data.index <= current_date]
            
            if len(historical_data) == 0:
                return None
            
            current_price = historical_data["close"].iloc[-1]
            
            # Apply transaction costs and slippage
            effective_exit_price = current_price * (1 - self.slippage - self.transaction_cost)
            
            # Calculate returns
            trade_return = (effective_exit_price - position.entry_price) / position.entry_price
            trade_pnl = position.position_size * (effective_exit_price - position.entry_price)
            days_held = (current_date - position.entry_date).days
            
            # Create exit trade record
            exit_trade = BacktestTrade(
                symbol=position.symbol,
                entry_date=position.entry_date,
                exit_date=current_date,
                entry_price=position.entry_price,
                exit_price=effective_exit_price,
                position_size=position.position_size,
                position_value=position.position_value,
                growth_score=position.growth_score,
                risk_score=position.risk_score,
                trade_return=trade_return,
                trade_pnl=trade_pnl,
                exit_reason=exit_reason,
                days_held=days_held
            )
            
            return exit_trade
            
        except Exception as e:
            self.logger.error(f"Error closing position for {position.symbol}: {e}")
            return None
    
    async def _update_positions(
        self,
        current_positions: Dict[str, BacktestTrade],
        current_date: datetime,
        market_data: Dict[str, pd.DataFrame]
    ) -> Tuple[Dict[str, BacktestTrade], List[BacktestTrade]]:
        """Update positions and check exit conditions."""
        updated_positions = {}
        exit_trades = []
        
        for symbol, position in current_positions.items():
            # Check stop loss and other exit conditions
            should_exit, exit_reason = await self._check_exit_conditions(
                position, current_date, market_data
            )
            
            if should_exit:
                exit_trade = await self._close_position(position, current_date, market_data, exit_reason)
                if exit_trade:
                    exit_trades.append(exit_trade)
            else:
                updated_positions[symbol] = position
        
        return updated_positions, exit_trades
    
    async def _check_exit_conditions(
        self,
        position: BacktestTrade,
        current_date: datetime,
        market_data: Dict[str, pd.DataFrame]
    ) -> Tuple[bool, str]:
        """Check if position should be exited."""
        try:
            symbol = position.symbol
            
            if symbol not in market_data:
                return True, "no_data"
            
            symbol_data = market_data[symbol]
            historical_data = symbol_data[symbol_data.index <= current_date]
            
            if len(historical_data) == 0:
                return True, "no_data"
            
            current_price = historical_data["close"].iloc[-1]
            
            # Stop loss check (15% default)
            stop_loss_level = 0.15
            if current_price < position.entry_price * (1 - stop_loss_level):
                return True, "stop_loss"
            
            # Time-based exit (hold for max 90 days)
            days_held = (current_date - position.entry_date).days
            if days_held > 90:
                return True, "time_limit"
            
            # Profit taking (50% gain)
            if current_price > position.entry_price * 1.5:
                return True, "profit_taking"
            
            return False, ""
            
        except Exception as e:
            self.logger.error(f"Error checking exit conditions for {position.symbol}: {e}")
            return True, "error"
    
    async def _calculate_portfolio_value(
        self,
        current_positions: Dict[str, BacktestTrade],
        current_date: datetime,
        market_data: Dict[str, pd.DataFrame]
    ) -> float:
        """Calculate current portfolio value."""
        total_value = 0.0
        
        for position in current_positions.values():
            try:
                symbol = position.symbol
                
                if symbol in market_data:
                    symbol_data = market_data[symbol]
                    historical_data = symbol_data[symbol_data.index <= current_date]
                    
                    if len(historical_data) > 0:
                        current_price = historical_data["close"].iloc[-1]
                        position_value = position.position_size * current_price
                        total_value += position_value
                    
            except Exception as e:
                self.logger.warning(f"Error calculating value for {position.symbol}: {e}")
                continue
        
        return total_value
    
    async def _calculate_available_capital(
        self,
        current_positions: Dict[str, BacktestTrade],
        current_date: datetime,
        market_data: Dict[str, pd.DataFrame]
    ) -> float:
        """Calculate available capital for new positions."""
        portfolio_value = await self._calculate_portfolio_value(current_positions, current_date, market_data)
        
        # Assume we want to be 90% invested, 10% cash
        target_invested = portfolio_value * 0.9
        currently_invested = sum(
            position.position_size * market_data[position.symbol].loc[
                market_data[position.symbol].index <= current_date
            ]["close"].iloc[-1]
            for position in current_positions.values()
            if position.symbol in market_data and len(
                market_data[position.symbol].loc[market_data[position.symbol].index <= current_date]
            ) > 0
        )
        
        return max(0, target_invested - currently_invested)
    
    async def _calculate_backtest_results(
        self,
        start_date: datetime,
        end_date: datetime,
        initial_capital: float,
        final_capital: float,
        all_trades: List[BacktestTrade],
        daily_values: List[float],
        daily_dates: List[datetime]
    ) -> BacktestResults:
        """Calculate comprehensive backtest results."""
        
        # Basic performance metrics
        total_return = (final_capital - initial_capital) / initial_capital
        days = (end_date - start_date).days
        annualized_return = (1 + total_return) ** (365 / days) - 1
        
        # Create equity curve
        equity_curve = pd.Series(daily_values, index=daily_dates)
        daily_returns = equity_curve.pct_change().dropna()
        
        # Risk metrics
        volatility = daily_returns.std() * np.sqrt(252) if len(daily_returns) > 0 else 0
        sharpe_ratio = (annualized_return - 0.05) / volatility if volatility > 0 else 0
        
        # Drawdown calculation
        running_max = equity_curve.expanding().max()
        drawdown = (equity_curve - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # Trade statistics
        completed_trades = [t for t in all_trades if t.exit_date is not None]
        profitable_trades = [t for t in completed_trades if t.trade_return and t.trade_return > 0]
        losing_trades = [t for t in completed_trades if t.trade_return and t.trade_return <= 0]
        
        win_rate = len(profitable_trades) / len(completed_trades) if completed_trades else 0
        avg_win = np.mean([t.trade_return for t in profitable_trades]) if profitable_trades else 0
        avg_loss = np.mean([t.trade_return for t in losing_trades]) if losing_trades else 0
        
        # Monthly returns
        monthly_returns = equity_curve.resample('M').last().pct_change().dropna()
        
        # Sector performance
        sector_performance = {}
        for trade in completed_trades:
            sector = "biotech" if trade.symbol.startswith(("A", "B")) else "ai_ml"  # Mock sector
            if sector not in sector_performance:
                sector_performance[sector] = []
            if trade.trade_return:
                sector_performance[sector].append(trade.trade_return)
        
        sector_performance = {
            sector: np.mean(returns) for sector, returns in sector_performance.items()
        }
        
        return BacktestResults(
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            final_capital=final_capital,
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            total_trades=len(all_trades),
            profitable_trades=len(profitable_trades),
            losing_trades=len(losing_trades),
            trades=all_trades,
            daily_returns=daily_returns,
            equity_curve=equity_curve,
            monthly_returns=monthly_returns,
            sector_performance=sector_performance,
            risk_metrics={
                "volatility": volatility,
                "max_drawdown": max_drawdown,
                "sharpe_ratio": sharpe_ratio
            }
        )

    def generate_backtest_report(self, results: BacktestResults) -> Dict[str, Any]:
        """Generate comprehensive backtest report."""
        return {
            "performance_summary": {
                "total_return": f"{results.total_return:.1%}",
                "annualized_return": f"{results.annualized_return:.1%}",
                "volatility": f"{results.volatility:.1%}",
                "sharpe_ratio": f"{results.sharpe_ratio:.2f}",
                "max_drawdown": f"{results.max_drawdown:.1%}",
                "win_rate": f"{results.win_rate:.1%}"
            },
            "trade_statistics": {
                "total_trades": results.total_trades,
                "profitable_trades": results.profitable_trades,
                "losing_trades": results.losing_trades,
                "average_win": f"{results.avg_win:.1%}",
                "average_loss": f"{results.avg_loss:.1%}",
                "profit_factor": abs(results.avg_win / results.avg_loss) if results.avg_loss != 0 else float('inf')
            },
            "sector_performance": {
                sector: f"{performance:.1%}"
                for sector, performance in results.sector_performance.items()
            },
            "monthly_returns": results.monthly_returns.to_dict(),
            "risk_analysis": results.risk_metrics
        }
