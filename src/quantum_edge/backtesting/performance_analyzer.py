"""
Performance Analyzer for QuantumEdge backtesting system.
Provides detailed performance analysis and benchmarking capabilities.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..config.logging_config import LoggerMixin
from .backtest_engine import BacktestResults, BacktestTrade


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics."""
    returns_analysis: Dict[str, float]
    risk_analysis: Dict[str, float]
    drawdown_analysis: Dict[str, float]
    trade_analysis: Dict[str, Any]
    benchmark_comparison: Dict[str, float]
    rolling_metrics: Dict[str, pd.Series]
    attribution_analysis: Dict[str, float]


class PerformanceAnalyzer(LoggerMixin):
    """
    Advanced performance analysis for backtesting results.
    Provides detailed metrics, attribution analysis, and benchmarking.
    """
    
    def __init__(self, risk_free_rate: float = 0.05):
        """
        Initialize performance analyzer.
        
        Args:
            risk_free_rate: Risk-free rate for calculations
        """
        self.risk_free_rate = risk_free_rate
        self.logger.info("Performance analyzer initialized")
    
    async def analyze_performance(
        self,
        backtest_results: BacktestResults,
        benchmark_data: Optional[pd.DataFrame] = None,
        rolling_window: int = 252  # 1 year
    ) -> PerformanceMetrics:
        """
        Comprehensive performance analysis.
        
        Args:
            backtest_results: Backtest results to analyze
            benchmark_data: Benchmark data for comparison
            rolling_window: Window for rolling metrics
            
        Returns:
            PerformanceMetrics object
        """
        self.logger.info("Analyzing backtest performance")
        
        # Returns analysis
        returns_analysis = await self._analyze_returns(backtest_results)
        
        # Risk analysis
        risk_analysis = await self._analyze_risk(backtest_results)
        
        # Drawdown analysis
        drawdown_analysis = await self._analyze_drawdowns(backtest_results)
        
        # Trade analysis
        trade_analysis = await self._analyze_trades(backtest_results)
        
        # Benchmark comparison
        benchmark_comparison = await self._compare_to_benchmark(
            backtest_results, benchmark_data
        )
        
        # Rolling metrics
        rolling_metrics = await self._calculate_rolling_metrics(
            backtest_results, rolling_window
        )
        
        # Attribution analysis
        attribution_analysis = await self._analyze_attribution(backtest_results)
        
        return PerformanceMetrics(
            returns_analysis=returns_analysis,
            risk_analysis=risk_analysis,
            drawdown_analysis=drawdown_analysis,
            trade_analysis=trade_analysis,
            benchmark_comparison=benchmark_comparison,
            rolling_metrics=rolling_metrics,
            attribution_analysis=attribution_analysis
        )
    
    async def _analyze_returns(self, results: BacktestResults) -> Dict[str, float]:
        """Analyze return characteristics."""
        daily_returns = results.daily_returns
        
        if len(daily_returns) == 0:
            return {}
        
        return {
            "total_return": results.total_return,
            "annualized_return": results.annualized_return,
            "daily_mean_return": daily_returns.mean(),
            "daily_std_return": daily_returns.std(),
            "skewness": daily_returns.skew(),
            "kurtosis": daily_returns.kurtosis(),
            "best_day": daily_returns.max(),
            "worst_day": daily_returns.min(),
            "positive_days": (daily_returns > 0).sum() / len(daily_returns),
            "negative_days": (daily_returns < 0).sum() / len(daily_returns),
            "monthly_volatility": results.monthly_returns.std() if len(results.monthly_returns) > 0 else 0
        }
    
    async def _analyze_risk(self, results: BacktestResults) -> Dict[str, float]:
        """Analyze risk characteristics."""
        daily_returns = results.daily_returns
        
        if len(daily_returns) == 0:
            return {}
        
        # Value at Risk calculations
        var_95 = -np.percentile(daily_returns, 5)
        var_99 = -np.percentile(daily_returns, 1)
        
        # Expected Shortfall
        tail_losses = daily_returns[daily_returns <= -var_95]
        expected_shortfall = -tail_losses.mean() if len(tail_losses) > 0 else 0
        
        # Downside deviation
        downside_returns = daily_returns[daily_returns < 0]
        downside_deviation = downside_returns.std() * np.sqrt(252)
        
        return {
            "volatility": results.volatility,
            "downside_deviation": downside_deviation,
            "var_95": var_95,
            "var_99": var_99,
            "expected_shortfall": expected_shortfall,
            "sharpe_ratio": results.sharpe_ratio,
            "sortino_ratio": (results.annualized_return - self.risk_free_rate) / downside_deviation if downside_deviation > 0 else 0,
            "calmar_ratio": results.annualized_return / abs(results.max_drawdown) if results.max_drawdown != 0 else 0,
            "ulcer_index": self._calculate_ulcer_index(results.equity_curve)
        }
    
    async def _analyze_drawdowns(self, results: BacktestResults) -> Dict[str, float]:
        """Analyze drawdown characteristics."""
        equity_curve = results.equity_curve
        
        if len(equity_curve) == 0:
            return {}
        
        # Calculate drawdowns
        running_max = equity_curve.expanding().max()
        drawdowns = (equity_curve - running_max) / running_max
        
        # Find drawdown periods
        in_drawdown = drawdowns < 0
        drawdown_periods = []
        
        if in_drawdown.any():
            # Find start and end of drawdown periods
            drawdown_starts = in_drawdown & ~in_drawdown.shift(1, fill_value=False)
            drawdown_ends = ~in_drawdown & in_drawdown.shift(1, fill_value=False)
            
            starts = drawdown_starts[drawdown_starts].index
            ends = drawdown_ends[drawdown_ends].index
            
            # Handle case where we end in a drawdown
            if len(starts) > len(ends):
                ends = ends.append(pd.Index([equity_curve.index[-1]]))
            
            for start, end in zip(starts, ends):
                period_drawdowns = drawdowns[start:end]
                max_dd = period_drawdowns.min()
                duration = (end - start).days
                
                drawdown_periods.append({
                    "start": start,
                    "end": end,
                    "max_drawdown": max_dd,
                    "duration_days": duration
                })
        
        # Calculate statistics
        if drawdown_periods:
            durations = [dd["duration_days"] for dd in drawdown_periods]
            max_drawdowns = [dd["max_drawdown"] for dd in drawdown_periods]
            
            return {
                "max_drawdown": results.max_drawdown,
                "avg_drawdown": np.mean(max_drawdowns),
                "max_drawdown_duration": max(durations),
                "avg_drawdown_duration": np.mean(durations),
                "num_drawdown_periods": len(drawdown_periods),
                "recovery_factor": results.total_return / abs(results.max_drawdown) if results.max_drawdown != 0 else 0,
                "time_underwater_pct": sum(durations) / len(equity_curve) * 100
            }
        else:
            return {
                "max_drawdown": 0,
                "avg_drawdown": 0,
                "max_drawdown_duration": 0,
                "avg_drawdown_duration": 0,
                "num_drawdown_periods": 0,
                "recovery_factor": float('inf'),
                "time_underwater_pct": 0
            }
    
    async def _analyze_trades(self, results: BacktestResults) -> Dict[str, Any]:
        """Analyze individual trade characteristics."""
        completed_trades = [t for t in results.trades if t.exit_date is not None]
        
        if not completed_trades:
            return {}
        
        # Trade returns
        trade_returns = [t.trade_return for t in completed_trades if t.trade_return is not None]
        trade_pnls = [t.trade_pnl for t in completed_trades if t.trade_pnl is not None]
        holding_periods = [t.days_held for t in completed_trades if t.days_held is not None]
        
        # Win/loss analysis
        winning_trades = [r for r in trade_returns if r > 0]
        losing_trades = [r for r in trade_returns if r <= 0]
        
        # Consecutive wins/losses
        consecutive_wins, consecutive_losses = self._calculate_consecutive_trades(trade_returns)
        
        return {
            "total_trades": len(completed_trades),
            "winning_trades": len(winning_trades),
            "losing_trades": len(losing_trades),
            "win_rate": len(winning_trades) / len(completed_trades) if completed_trades else 0,
            "avg_win": np.mean(winning_trades) if winning_trades else 0,
            "avg_loss": np.mean(losing_trades) if losing_trades else 0,
            "largest_win": max(winning_trades) if winning_trades else 0,
            "largest_loss": min(losing_trades) if losing_trades else 0,
            "profit_factor": abs(sum(winning_trades) / sum(losing_trades)) if losing_trades and sum(losing_trades) != 0 else float('inf'),
            "avg_holding_period": np.mean(holding_periods) if holding_periods else 0,
            "max_consecutive_wins": consecutive_wins,
            "max_consecutive_losses": consecutive_losses,
            "expectancy": np.mean(trade_returns) if trade_returns else 0,
            "total_pnl": sum(trade_pnls) if trade_pnls else 0,
            "gross_profit": sum(winning_trades) if winning_trades else 0,
            "gross_loss": sum(losing_trades) if losing_trades else 0
        }
    
    async def _compare_to_benchmark(
        self,
        results: BacktestResults,
        benchmark_data: Optional[pd.DataFrame]
    ) -> Dict[str, float]:
        """Compare performance to benchmark."""
        if benchmark_data is None or len(benchmark_data) == 0:
            return {}
        
        # Align dates
        strategy_returns = results.daily_returns
        benchmark_returns = benchmark_data["close"].pct_change().dropna()
        
        # Find common dates
        common_dates = strategy_returns.index.intersection(benchmark_returns.index)
        
        if len(common_dates) < 10:
            return {}
        
        strategy_aligned = strategy_returns.loc[common_dates]
        benchmark_aligned = benchmark_returns.loc[common_dates]
        
        # Calculate benchmark metrics
        benchmark_total_return = (1 + benchmark_aligned).prod() - 1
        benchmark_volatility = benchmark_aligned.std() * np.sqrt(252)
        benchmark_sharpe = (benchmark_total_return - self.risk_free_rate) / benchmark_volatility if benchmark_volatility > 0 else 0
        
        # Calculate relative metrics
        excess_returns = strategy_aligned - benchmark_aligned
        tracking_error = excess_returns.std() * np.sqrt(252)
        information_ratio = excess_returns.mean() * np.sqrt(252) / tracking_error if tracking_error > 0 else 0
        
        # Beta calculation
        beta = strategy_aligned.cov(benchmark_aligned) / benchmark_aligned.var() if benchmark_aligned.var() > 0 else 1
        alpha = strategy_aligned.mean() - beta * benchmark_aligned.mean()
        
        return {
            "benchmark_total_return": benchmark_total_return,
            "benchmark_volatility": benchmark_volatility,
            "benchmark_sharpe": benchmark_sharpe,
            "excess_return": results.total_return - benchmark_total_return,
            "tracking_error": tracking_error,
            "information_ratio": information_ratio,
            "beta": beta,
            "alpha": alpha * 252,  # Annualized alpha
            "correlation": strategy_aligned.corr(benchmark_aligned),
            "up_capture": self._calculate_capture_ratio(strategy_aligned, benchmark_aligned, True),
            "down_capture": self._calculate_capture_ratio(strategy_aligned, benchmark_aligned, False)
        }
    
    async def _calculate_rolling_metrics(
        self,
        results: BacktestResults,
        window: int
    ) -> Dict[str, pd.Series]:
        """Calculate rolling performance metrics."""
        daily_returns = results.daily_returns
        
        if len(daily_returns) < window:
            return {}
        
        return {
            "rolling_return": daily_returns.rolling(window).apply(lambda x: (1 + x).prod() - 1),
            "rolling_volatility": daily_returns.rolling(window).std() * np.sqrt(252),
            "rolling_sharpe": daily_returns.rolling(window).apply(
                lambda x: (x.mean() * 252 - self.risk_free_rate) / (x.std() * np.sqrt(252))
            ),
            "rolling_max_drawdown": results.equity_curve.rolling(window).apply(
                lambda x: ((x - x.expanding().max()) / x.expanding().max()).min()
            )
        }
    
    async def _analyze_attribution(self, results: BacktestResults) -> Dict[str, float]:
        """Analyze performance attribution by various factors."""
        completed_trades = [t for t in results.trades if t.exit_date is not None]
        
        if not completed_trades:
            return {}
        
        # Sector attribution
        sector_returns = {}
        for trade in completed_trades:
            sector = "biotech" if trade.symbol.startswith(("A", "B")) else "ai_ml"  # Mock sector
            if sector not in sector_returns:
                sector_returns[sector] = []
            if trade.trade_return:
                sector_returns[sector].append(trade.trade_return)
        
        sector_attribution = {
            f"{sector}_return": np.mean(returns) for sector, returns in sector_returns.items()
        }
        
        # Growth score attribution
        high_growth_trades = [t for t in completed_trades if t.growth_score > 80]
        medium_growth_trades = [t for t in completed_trades if 60 <= t.growth_score <= 80]
        low_growth_trades = [t for t in completed_trades if t.growth_score < 60]
        
        growth_attribution = {
            "high_growth_return": np.mean([t.trade_return for t in high_growth_trades if t.trade_return]) if high_growth_trades else 0,
            "medium_growth_return": np.mean([t.trade_return for t in medium_growth_trades if t.trade_return]) if medium_growth_trades else 0,
            "low_growth_return": np.mean([t.trade_return for t in low_growth_trades if t.trade_return]) if low_growth_trades else 0
        }
        
        # Holding period attribution
        short_term_trades = [t for t in completed_trades if t.days_held and t.days_held <= 30]
        medium_term_trades = [t for t in completed_trades if t.days_held and 30 < t.days_held <= 60]
        long_term_trades = [t for t in completed_trades if t.days_held and t.days_held > 60]
        
        holding_attribution = {
            "short_term_return": np.mean([t.trade_return for t in short_term_trades if t.trade_return]) if short_term_trades else 0,
            "medium_term_return": np.mean([t.trade_return for t in medium_term_trades if t.trade_return]) if medium_term_trades else 0,
            "long_term_return": np.mean([t.trade_return for t in long_term_trades if t.trade_return]) if long_term_trades else 0
        }
        
        return {**sector_attribution, **growth_attribution, **holding_attribution}
    
    def _calculate_ulcer_index(self, equity_curve: pd.Series) -> float:
        """Calculate Ulcer Index (alternative drawdown measure)."""
        if len(equity_curve) == 0:
            return 0
        
        running_max = equity_curve.expanding().max()
        drawdowns = (equity_curve - running_max) / running_max * 100
        squared_drawdowns = drawdowns ** 2
        
        return np.sqrt(squared_drawdowns.mean())
    
    def _calculate_consecutive_trades(self, trade_returns: List[float]) -> Tuple[int, int]:
        """Calculate maximum consecutive wins and losses."""
        if not trade_returns:
            return 0, 0
        
        max_consecutive_wins = 0
        max_consecutive_losses = 0
        current_wins = 0
        current_losses = 0
        
        for return_val in trade_returns:
            if return_val > 0:
                current_wins += 1
                current_losses = 0
                max_consecutive_wins = max(max_consecutive_wins, current_wins)
            else:
                current_losses += 1
                current_wins = 0
                max_consecutive_losses = max(max_consecutive_losses, current_losses)
        
        return max_consecutive_wins, max_consecutive_losses
    
    def _calculate_capture_ratio(
        self,
        strategy_returns: pd.Series,
        benchmark_returns: pd.Series,
        upside: bool
    ) -> float:
        """Calculate upside or downside capture ratio."""
        if upside:
            mask = benchmark_returns > 0
        else:
            mask = benchmark_returns < 0
        
        if not mask.any():
            return 0
        
        strategy_filtered = strategy_returns[mask]
        benchmark_filtered = benchmark_returns[mask]
        
        if len(strategy_filtered) == 0 or len(benchmark_filtered) == 0:
            return 0
        
        strategy_avg = strategy_filtered.mean()
        benchmark_avg = benchmark_filtered.mean()
        
        if benchmark_avg == 0:
            return 0
        
        return strategy_avg / benchmark_avg
