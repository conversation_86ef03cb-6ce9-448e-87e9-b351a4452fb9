"""
Walk-Forward Testing for QuantumEdge system.
Implements robust out-of-sample testing with rolling optimization windows.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..config.logging_config import LoggerMixin
from .backtest_engine import BacktestEngine, BacktestResults


@dataclass
class WalkForwardPeriod:
    """Represents a single walk-forward testing period."""
    period_id: int
    train_start: datetime
    train_end: datetime
    test_start: datetime
    test_end: datetime
    optimization_params: Dict[str, Any]
    backtest_results: Optional[BacktestResults]
    out_of_sample_return: float
    out_of_sample_sharpe: float
    out_of_sample_max_dd: float


@dataclass
class WalkForwardResults:
    """Comprehensive walk-forward testing results."""
    total_periods: int
    train_window_days: int
    test_window_days: int
    periods: List[WalkForwardPeriod]
    combined_results: BacktestResults
    stability_metrics: Dict[str, float]
    parameter_stability: Dict[str, List[float]]
    out_of_sample_performance: Dict[str, float]
    degradation_analysis: Dict[str, float]


class WalkForwardTester(LoggerMixin):
    """
    Walk-forward testing engine for robust strategy validation.
    Tests strategy performance on out-of-sample data with rolling optimization.
    """
    
    def __init__(
        self,
        backtest_engine: BacktestEngine,
        train_window_months: int = 12,  # 12 months training
        test_window_months: int = 3,    # 3 months testing
        step_months: int = 3,           # 3 months step forward
        min_trades_per_period: int = 10 # Minimum trades for valid period
    ):
        """
        Initialize walk-forward tester.
        
        Args:
            backtest_engine: Backtesting engine
            train_window_months: Training window in months
            test_window_months: Testing window in months
            step_months: Step size in months
            min_trades_per_period: Minimum trades for valid period
        """
        self.backtest_engine = backtest_engine
        self.train_window_months = train_window_months
        self.test_window_months = test_window_months
        self.step_months = step_months
        self.min_trades_per_period = min_trades_per_period
        
        self.logger.info("Walk-forward tester initialized")
    
    async def run_walk_forward_test(
        self,
        start_date: datetime,
        end_date: datetime,
        market_data: Dict[str, pd.DataFrame],
        parameter_ranges: Dict[str, List[Any]],
        benchmark_data: Optional[pd.DataFrame] = None
    ) -> WalkForwardResults:
        """
        Run comprehensive walk-forward testing.
        
        Args:
            start_date: Overall test start date
            end_date: Overall test end date
            market_data: Historical market data
            parameter_ranges: Parameter ranges for optimization
            benchmark_data: Benchmark data for comparison
            
        Returns:
            WalkForwardResults object
        """
        self.logger.info(f"Running walk-forward test from {start_date} to {end_date}")
        
        # Generate walk-forward periods
        periods = self._generate_walk_forward_periods(start_date, end_date)
        
        # Run each period
        completed_periods = []
        
        for period in periods:
            try:
                self.logger.info(f"Processing period {period.period_id}: {period.test_start} to {period.test_end}")
                
                # Optimize parameters on training data
                optimal_params = await self._optimize_parameters(
                    period.train_start, period.train_end, market_data, parameter_ranges
                )
                
                # Test on out-of-sample data
                test_results = await self._test_out_of_sample(
                    period.test_start, period.test_end, market_data, optimal_params
                )
                
                # Update period with results
                period.optimization_params = optimal_params
                period.backtest_results = test_results
                period.out_of_sample_return = test_results.total_return
                period.out_of_sample_sharpe = test_results.sharpe_ratio
                period.out_of_sample_max_dd = test_results.max_drawdown
                
                # Only include periods with sufficient trades
                if test_results.total_trades >= self.min_trades_per_period:
                    completed_periods.append(period)
                    self.logger.info(f"Period {period.period_id} completed: {test_results.total_return:.1%} return")
                else:
                    self.logger.warning(f"Period {period.period_id} skipped: insufficient trades ({test_results.total_trades})")
                
            except Exception as e:
                self.logger.error(f"Error processing period {period.period_id}: {e}")
                continue
        
        # Combine results and analyze
        combined_results = await self._combine_period_results(completed_periods, market_data)
        stability_metrics = await self._calculate_stability_metrics(completed_periods)
        parameter_stability = await self._analyze_parameter_stability(completed_periods)
        out_of_sample_performance = await self._analyze_out_of_sample_performance(completed_periods)
        degradation_analysis = await self._analyze_performance_degradation(completed_periods)
        
        results = WalkForwardResults(
            total_periods=len(completed_periods),
            train_window_days=self.train_window_months * 30,
            test_window_days=self.test_window_months * 30,
            periods=completed_periods,
            combined_results=combined_results,
            stability_metrics=stability_metrics,
            parameter_stability=parameter_stability,
            out_of_sample_performance=out_of_sample_performance,
            degradation_analysis=degradation_analysis
        )
        
        self.logger.info(f"Walk-forward test completed: {len(completed_periods)} periods")
        return results
    
    def _generate_walk_forward_periods(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> List[WalkForwardPeriod]:
        """Generate walk-forward testing periods."""
        periods = []
        period_id = 1
        
        current_date = start_date
        
        while current_date < end_date:
            # Training period
            train_start = current_date
            train_end = train_start + timedelta(days=self.train_window_months * 30)
            
            # Testing period
            test_start = train_end + timedelta(days=1)
            test_end = test_start + timedelta(days=self.test_window_months * 30)
            
            # Check if we have enough data
            if test_end <= end_date:
                period = WalkForwardPeriod(
                    period_id=period_id,
                    train_start=train_start,
                    train_end=train_end,
                    test_start=test_start,
                    test_end=test_end,
                    optimization_params={},
                    backtest_results=None,
                    out_of_sample_return=0.0,
                    out_of_sample_sharpe=0.0,
                    out_of_sample_max_dd=0.0
                )
                periods.append(period)
                period_id += 1
            
            # Step forward
            current_date += timedelta(days=self.step_months * 30)
        
        return periods
    
    async def _optimize_parameters(
        self,
        train_start: datetime,
        train_end: datetime,
        market_data: Dict[str, pd.DataFrame],
        parameter_ranges: Dict[str, List[Any]]
    ) -> Dict[str, Any]:
        """Optimize parameters on training data."""
        try:
            # For simplicity, we'll use a grid search approach
            # In practice, you might use more sophisticated optimization
            
            best_params = {}
            best_sharpe = -float('inf')
            
            # Generate parameter combinations (simplified)
            param_combinations = self._generate_parameter_combinations(parameter_ranges)
            
            # Limit combinations for performance
            max_combinations = 20
            if len(param_combinations) > max_combinations:
                # Sample random combinations
                import random
                param_combinations = random.sample(param_combinations, max_combinations)
            
            for params in param_combinations:
                try:
                    # Update backtest engine parameters
                    self._update_engine_parameters(params)
                    
                    # Run backtest on training data
                    train_results = await self.backtest_engine.run_backtest(
                        train_start, train_end, market_data
                    )
                    
                    # Evaluate performance (using Sharpe ratio as primary metric)
                    if train_results.sharpe_ratio > best_sharpe:
                        best_sharpe = train_results.sharpe_ratio
                        best_params = params.copy()
                
                except Exception as e:
                    self.logger.warning(f"Error testing parameters {params}: {e}")
                    continue
            
            return best_params if best_params else self._get_default_parameters()
            
        except Exception as e:
            self.logger.error(f"Error in parameter optimization: {e}")
            return self._get_default_parameters()
    
    async def _test_out_of_sample(
        self,
        test_start: datetime,
        test_end: datetime,
        market_data: Dict[str, pd.DataFrame],
        optimal_params: Dict[str, Any]
    ) -> BacktestResults:
        """Test optimized parameters on out-of-sample data."""
        # Update engine with optimal parameters
        self._update_engine_parameters(optimal_params)
        
        # Run backtest on test period
        return await self.backtest_engine.run_backtest(
            test_start, test_end, market_data
        )
    
    def _generate_parameter_combinations(
        self,
        parameter_ranges: Dict[str, List[Any]]
    ) -> List[Dict[str, Any]]:
        """Generate all parameter combinations."""
        if not parameter_ranges:
            return [{}]
        
        import itertools
        
        keys = list(parameter_ranges.keys())
        values = list(parameter_ranges.values())
        
        combinations = []
        for combination in itertools.product(*values):
            param_dict = dict(zip(keys, combination))
            combinations.append(param_dict)
        
        return combinations
    
    def _update_engine_parameters(self, params: Dict[str, Any]):
        """Update backtest engine parameters."""
        # Update relevant parameters in the backtest engine
        if "transaction_cost" in params:
            self.backtest_engine.transaction_cost = params["transaction_cost"]
        if "max_positions" in params:
            self.backtest_engine.max_positions = params["max_positions"]
        if "rebalance_frequency" in params:
            self.backtest_engine.rebalance_frequency = params["rebalance_frequency"]
    
    def _get_default_parameters(self) -> Dict[str, Any]:
        """Get default parameters."""
        return {
            "transaction_cost": 0.005,
            "max_positions": 20,
            "rebalance_frequency": 30
        }
    
    async def _combine_period_results(
        self,
        periods: List[WalkForwardPeriod],
        market_data: Dict[str, pd.DataFrame]
    ) -> BacktestResults:
        """Combine results from all periods into single backtest result."""
        if not periods:
            # Return empty results
            return BacktestResults(
                start_date=datetime.now(),
                end_date=datetime.now(),
                initial_capital=100000,
                final_capital=100000,
                total_return=0,
                annualized_return=0,
                volatility=0,
                sharpe_ratio=0,
                max_drawdown=0,
                win_rate=0,
                avg_win=0,
                avg_loss=0,
                total_trades=0,
                profitable_trades=0,
                losing_trades=0,
                trades=[],
                daily_returns=pd.Series(dtype=float),
                equity_curve=pd.Series(dtype=float),
                monthly_returns=pd.Series(dtype=float),
                sector_performance={},
                risk_metrics={}
            )
        
        # Combine all trades
        all_trades = []
        for period in periods:
            if period.backtest_results:
                all_trades.extend(period.backtest_results.trades)
        
        # Combine daily returns
        all_daily_returns = pd.Series(dtype=float)
        all_equity_values = pd.Series(dtype=float)
        
        for period in periods:
            if period.backtest_results:
                all_daily_returns = pd.concat([all_daily_returns, period.backtest_results.daily_returns])
                all_equity_values = pd.concat([all_equity_values, period.backtest_results.equity_curve])
        
        # Calculate combined metrics
        if len(all_daily_returns) > 0:
            total_return = (all_equity_values.iloc[-1] / all_equity_values.iloc[0]) - 1
            volatility = all_daily_returns.std() * np.sqrt(252)
            sharpe_ratio = (all_daily_returns.mean() * 252 - 0.05) / volatility if volatility > 0 else 0
            
            # Max drawdown
            running_max = all_equity_values.expanding().max()
            drawdowns = (all_equity_values - running_max) / running_max
            max_drawdown = drawdowns.min()
        else:
            total_return = 0
            volatility = 0
            sharpe_ratio = 0
            max_drawdown = 0
        
        # Trade statistics
        completed_trades = [t for t in all_trades if t.exit_date is not None]
        profitable_trades = [t for t in completed_trades if t.trade_return and t.trade_return > 0]
        
        return BacktestResults(
            start_date=periods[0].test_start,
            end_date=periods[-1].test_end,
            initial_capital=100000,  # Default
            final_capital=100000 * (1 + total_return),
            total_return=total_return,
            annualized_return=(1 + total_return) ** (365 / len(all_daily_returns)) - 1 if len(all_daily_returns) > 0 else 0,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=len(profitable_trades) / len(completed_trades) if completed_trades else 0,
            avg_win=np.mean([t.trade_return for t in profitable_trades]) if profitable_trades else 0,
            avg_loss=np.mean([t.trade_return for t in completed_trades if t.trade_return and t.trade_return <= 0]) or 0,
            total_trades=len(all_trades),
            profitable_trades=len(profitable_trades),
            losing_trades=len(completed_trades) - len(profitable_trades),
            trades=all_trades,
            daily_returns=all_daily_returns,
            equity_curve=all_equity_values,
            monthly_returns=all_equity_values.resample('M').last().pct_change().dropna(),
            sector_performance={},
            risk_metrics={}
        )
    
    async def _calculate_stability_metrics(
        self,
        periods: List[WalkForwardPeriod]
    ) -> Dict[str, float]:
        """Calculate stability metrics across periods."""
        if not periods:
            return {}
        
        returns = [p.out_of_sample_return for p in periods]
        sharpe_ratios = [p.out_of_sample_sharpe for p in periods]
        max_drawdowns = [p.out_of_sample_max_dd for p in periods]
        
        return {
            "return_stability": 1 / (1 + np.std(returns)) if returns else 0,
            "sharpe_stability": 1 / (1 + np.std(sharpe_ratios)) if sharpe_ratios else 0,
            "drawdown_stability": 1 / (1 + np.std(max_drawdowns)) if max_drawdowns else 0,
            "positive_periods_pct": sum(1 for r in returns if r > 0) / len(returns) if returns else 0,
            "avg_return": np.mean(returns) if returns else 0,
            "avg_sharpe": np.mean(sharpe_ratios) if sharpe_ratios else 0,
            "worst_period_return": min(returns) if returns else 0,
            "best_period_return": max(returns) if returns else 0
        }
    
    async def _analyze_parameter_stability(
        self,
        periods: List[WalkForwardPeriod]
    ) -> Dict[str, List[float]]:
        """Analyze parameter stability across periods."""
        parameter_history = {}
        
        for period in periods:
            for param_name, param_value in period.optimization_params.items():
                if param_name not in parameter_history:
                    parameter_history[param_name] = []
                parameter_history[param_name].append(param_value)
        
        return parameter_history
    
    async def _analyze_out_of_sample_performance(
        self,
        periods: List[WalkForwardPeriod]
    ) -> Dict[str, float]:
        """Analyze out-of-sample performance characteristics."""
        if not periods:
            return {}
        
        returns = [p.out_of_sample_return for p in periods]
        
        return {
            "oos_total_return": np.prod([1 + r for r in returns]) - 1,
            "oos_avg_return": np.mean(returns),
            "oos_volatility": np.std(returns),
            "oos_sharpe": np.mean(returns) / np.std(returns) if np.std(returns) > 0 else 0,
            "oos_win_rate": sum(1 for r in returns if r > 0) / len(returns),
            "oos_best_period": max(returns),
            "oos_worst_period": min(returns),
            "oos_consistency": len([r for r in returns if r > 0]) / len(returns)
        }
    
    async def _analyze_performance_degradation(
        self,
        periods: List[WalkForwardPeriod]
    ) -> Dict[str, float]:
        """Analyze performance degradation over time."""
        if len(periods) < 2:
            return {}
        
        # Split into early and late periods
        mid_point = len(periods) // 2
        early_periods = periods[:mid_point]
        late_periods = periods[mid_point:]
        
        early_returns = [p.out_of_sample_return for p in early_periods]
        late_returns = [p.out_of_sample_return for p in late_periods]
        
        early_avg = np.mean(early_returns) if early_returns else 0
        late_avg = np.mean(late_returns) if late_returns else 0
        
        return {
            "early_period_avg_return": early_avg,
            "late_period_avg_return": late_avg,
            "performance_degradation": early_avg - late_avg,
            "degradation_pct": (early_avg - late_avg) / early_avg if early_avg != 0 else 0,
            "trend_slope": self._calculate_trend_slope([p.out_of_sample_return for p in periods])
        }
    
    def _calculate_trend_slope(self, values: List[float]) -> float:
        """Calculate trend slope using linear regression."""
        if len(values) < 2:
            return 0
        
        x = np.arange(len(values))
        y = np.array(values)
        
        # Simple linear regression
        n = len(values)
        sum_x = np.sum(x)
        sum_y = np.sum(y)
        sum_xy = np.sum(x * y)
        sum_x2 = np.sum(x * x)
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        return slope
    
    def generate_walk_forward_report(self, results: WalkForwardResults) -> Dict[str, Any]:
        """Generate comprehensive walk-forward testing report."""
        return {
            "test_summary": {
                "total_periods": results.total_periods,
                "train_window_days": results.train_window_days,
                "test_window_days": results.test_window_days,
                "combined_return": f"{results.combined_results.total_return:.1%}",
                "combined_sharpe": f"{results.combined_results.sharpe_ratio:.2f}",
                "combined_max_dd": f"{results.combined_results.max_drawdown:.1%}"
            },
            "stability_analysis": {
                metric: f"{value:.3f}" if isinstance(value, float) else value
                for metric, value in results.stability_metrics.items()
            },
            "out_of_sample_performance": {
                metric: f"{value:.1%}" if "return" in metric else f"{value:.3f}"
                for metric, value in results.out_of_sample_performance.items()
            },
            "degradation_analysis": {
                metric: f"{value:.1%}" if "return" in metric or "degradation" in metric else f"{value:.3f}"
                for metric, value in results.degradation_analysis.items()
            },
            "parameter_stability": {
                param: {
                    "mean": np.mean(values),
                    "std": np.std(values),
                    "min": min(values),
                    "max": max(values)
                }
                for param, values in results.parameter_stability.items()
            },
            "period_details": [
                {
                    "period": period.period_id,
                    "test_start": period.test_start.strftime("%Y-%m-%d"),
                    "test_end": period.test_end.strftime("%Y-%m-%d"),
                    "return": f"{period.out_of_sample_return:.1%}",
                    "sharpe": f"{period.out_of_sample_sharpe:.2f}",
                    "max_dd": f"{period.out_of_sample_max_dd:.1%}",
                    "trades": period.backtest_results.total_trades if period.backtest_results else 0
                }
                for period in results.periods
            ]
        }
