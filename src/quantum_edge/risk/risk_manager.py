"""
Risk Management Engine for QuantumEdge microcap discovery system.
Implements comprehensive risk controls for high-growth potential investments.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum

from ..config.logging_config import LoggerMixin


class RiskLevel(Enum):
    """Risk level classifications."""
    VERY_LOW = "very_low"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"
    EXTREME = "extreme"


@dataclass
class RiskAssessment:
    """Comprehensive risk assessment for a stock."""
    symbol: str
    overall_risk_level: RiskLevel
    risk_score: float  # 0-100 (higher = riskier)
    liquidity_risk: float
    volatility_risk: float
    concentration_risk: float
    sector_risk: float
    market_cap_risk: float
    fundamental_risk: float
    regulatory_risk: float
    key_risk_factors: List[str]
    risk_mitigation_suggestions: List[str]
    max_position_size_pct: float  # Maximum % of portfolio
    stop_loss_level: float  # Suggested stop loss %
    confidence: float  # Confidence in assessment (0-1)
    assessment_timestamp: datetime


class RiskManager(LoggerMixin):
    """
    Comprehensive risk management system for microcap investments.
    Focuses on protecting capital while allowing for high-growth potential.
    """
    
    def __init__(
        self,
        max_portfolio_risk: float = 0.02,  # 2% max portfolio risk per position
        max_sector_concentration: float = 0.20,  # 20% max in any sector
        max_single_position: float = 0.05,  # 5% max single position
        volatility_lookback_days: int = 30
    ):
        """
        Initialize risk manager.
        
        Args:
            max_portfolio_risk: Maximum portfolio risk per position (as decimal)
            max_sector_concentration: Maximum concentration in any sector
            max_single_position: Maximum single position size
            volatility_lookback_days: Days to look back for volatility calculation
        """
        self.max_portfolio_risk = max_portfolio_risk
        self.max_sector_concentration = max_sector_concentration
        self.max_single_position = max_single_position
        self.volatility_lookback_days = volatility_lookback_days
        
        # Risk thresholds for microcaps
        self.risk_thresholds = {
            "market_cap": {
                "very_high": 50_000_000,    # <$50M = very high risk
                "high": 100_000_000,        # <$100M = high risk
                "moderate": 500_000_000,    # <$500M = moderate risk
                "low": 1_000_000_000,       # <$1B = low risk
            },
            "daily_volume": {
                "very_high": 100_000,       # <$100k = very high risk
                "high": 500_000,            # <$500k = high risk
                "moderate": 1_000_000,      # <$1M = moderate risk
                "low": 5_000_000,           # <$5M = low risk
            },
            "volatility": {
                "low": 2.0,                 # <2% = low risk
                "moderate": 4.0,            # <4% = moderate risk
                "high": 8.0,                # <8% = high risk
                "very_high": 15.0,          # <15% = very high risk
            }
        }
        
        # Sector risk multipliers
        self.sector_risk_multipliers = {
            "biotech": 1.8,        # High regulatory risk
            "crypto": 2.0,         # Extreme volatility
            "penny_stocks": 2.5,   # Very high risk
            "ai_ml": 1.4,          # Moderate tech risk
            "clean_energy": 1.5,   # Policy dependent
            "space_tech": 1.6,     # Early stage
            "quantum": 1.7,        # Experimental
            "cybersecurity": 1.2,  # Stable demand
            "fintech": 1.3,        # Regulatory oversight
            "other": 1.0           # Baseline
        }
        
        self.logger.info("Risk manager initialized")
    
    async def assess_risk(
        self,
        symbol: str,
        company_data: Dict[str, Any],
        market_data: Optional[pd.DataFrame] = None,
        portfolio_context: Optional[Dict[str, Any]] = None
    ) -> RiskAssessment:
        """
        Comprehensive risk assessment for a microcap stock.
        
        Args:
            symbol: Stock symbol
            company_data: Company fundamental data
            market_data: Historical price/volume data
            portfolio_context: Current portfolio composition
            
        Returns:
            RiskAssessment object
        """
        self.logger.info(f"Assessing risk for {symbol}")
        
        # Calculate individual risk components
        liquidity_risk = await self._assess_liquidity_risk(company_data, market_data)
        volatility_risk = await self._assess_volatility_risk(market_data)
        market_cap_risk = await self._assess_market_cap_risk(company_data)
        sector_risk = await self._assess_sector_risk(company_data)
        fundamental_risk = await self._assess_fundamental_risk(company_data)
        regulatory_risk = await self._assess_regulatory_risk(company_data)
        concentration_risk = await self._assess_concentration_risk(symbol, portfolio_context)
        
        # Calculate overall risk score (weighted average)
        risk_components = {
            "liquidity": (liquidity_risk, 0.25),
            "volatility": (volatility_risk, 0.20),
            "market_cap": (market_cap_risk, 0.15),
            "sector": (sector_risk, 0.15),
            "fundamental": (fundamental_risk, 0.15),
            "regulatory": (regulatory_risk, 0.05),
            "concentration": (concentration_risk, 0.05)
        }
        
        overall_risk_score = sum(
            score * weight for score, weight in risk_components.values()
        )
        
        # Determine risk level
        risk_level = self._determine_risk_level(overall_risk_score)
        
        # Generate risk factors and mitigation suggestions
        key_risk_factors = self._identify_key_risk_factors(risk_components, company_data)
        mitigation_suggestions = self._generate_mitigation_suggestions(risk_components, company_data)
        
        # Calculate position sizing recommendations
        max_position_size = self._calculate_max_position_size(overall_risk_score, risk_level)
        stop_loss_level = self._calculate_stop_loss_level(volatility_risk, risk_level)
        
        return RiskAssessment(
            symbol=symbol,
            overall_risk_level=risk_level,
            risk_score=overall_risk_score,
            liquidity_risk=liquidity_risk,
            volatility_risk=volatility_risk,
            concentration_risk=concentration_risk,
            sector_risk=sector_risk,
            market_cap_risk=market_cap_risk,
            fundamental_risk=fundamental_risk,
            regulatory_risk=regulatory_risk,
            key_risk_factors=key_risk_factors,
            risk_mitigation_suggestions=mitigation_suggestions,
            max_position_size_pct=max_position_size,
            stop_loss_level=stop_loss_level,
            confidence=0.75,  # Default confidence level
            assessment_timestamp=datetime.now()
        )
    
    async def _assess_liquidity_risk(
        self,
        company_data: Dict[str, Any],
        market_data: Optional[pd.DataFrame]
    ) -> float:
        """Assess liquidity risk (0-100, higher = riskier)."""
        risk_score = 50.0  # Base score
        
        try:
            # Daily volume analysis
            avg_volume = company_data.get("avg_volume", 0)
            
            if avg_volume < self.risk_thresholds["daily_volume"]["very_high"]:
                risk_score = 90.0
            elif avg_volume < self.risk_thresholds["daily_volume"]["high"]:
                risk_score = 75.0
            elif avg_volume < self.risk_thresholds["daily_volume"]["moderate"]:
                risk_score = 60.0
            elif avg_volume < self.risk_thresholds["daily_volume"]["low"]:
                risk_score = 40.0
            else:
                risk_score = 20.0
            
            # Bid-ask spread analysis (if available)
            if market_data is not None and len(market_data) > 0:
                # Estimate spread from high-low range
                recent_data = market_data.tail(10)
                if not recent_data.empty:
                    avg_spread_pct = ((recent_data["high"] - recent_data["low"]) / recent_data["close"]).mean() * 100
                    
                    if avg_spread_pct > 5:  # >5% spread
                        risk_score += 20
                    elif avg_spread_pct > 2:  # >2% spread
                        risk_score += 10
            
        except Exception as e:
            self.logger.warning(f"Error assessing liquidity risk: {e}")
        
        return min(100.0, max(0.0, risk_score))
    
    async def _assess_volatility_risk(self, market_data: Optional[pd.DataFrame]) -> float:
        """Assess volatility risk (0-100, higher = riskier)."""
        if market_data is None or len(market_data) < 10:
            return 70.0  # High risk for insufficient data
        
        try:
            # Calculate daily returns
            returns = market_data["close"].pct_change().dropna()
            
            if len(returns) < 10:
                return 70.0
            
            # Annualized volatility
            daily_vol = returns.std()
            annual_vol = daily_vol * np.sqrt(252) * 100  # Convert to percentage
            
            # Risk scoring based on volatility
            if annual_vol > self.risk_thresholds["volatility"]["very_high"]:
                return 95.0
            elif annual_vol > self.risk_thresholds["volatility"]["high"]:
                return 80.0
            elif annual_vol > self.risk_thresholds["volatility"]["moderate"]:
                return 60.0
            elif annual_vol > self.risk_thresholds["volatility"]["low"]:
                return 40.0
            else:
                return 20.0
                
        except Exception as e:
            self.logger.warning(f"Error assessing volatility risk: {e}")
            return 70.0
    
    async def _assess_market_cap_risk(self, company_data: Dict[str, Any]) -> float:
        """Assess market cap risk (0-100, higher = riskier)."""
        market_cap = company_data.get("market_cap", 0)
        
        if market_cap < self.risk_thresholds["market_cap"]["very_high"]:
            return 95.0
        elif market_cap < self.risk_thresholds["market_cap"]["high"]:
            return 80.0
        elif market_cap < self.risk_thresholds["market_cap"]["moderate"]:
            return 60.0
        elif market_cap < self.risk_thresholds["market_cap"]["low"]:
            return 40.0
        else:
            return 20.0
    
    async def _assess_sector_risk(self, company_data: Dict[str, Any]) -> float:
        """Assess sector-specific risk (0-100, higher = riskier)."""
        sector = company_data.get("sector_category", "other")
        base_risk = 50.0
        
        # Apply sector multiplier
        multiplier = self.sector_risk_multipliers.get(sector, 1.0)
        sector_risk = base_risk * multiplier
        
        return min(100.0, max(0.0, sector_risk))
    
    async def _assess_fundamental_risk(self, company_data: Dict[str, Any]) -> float:
        """Assess fundamental financial risk (0-100, higher = riskier)."""
        risk_score = 50.0  # Base score
        
        try:
            # P/E ratio analysis
            pe_ratio = company_data.get("pe_ratio")
            if pe_ratio:
                if pe_ratio < 0:  # Negative earnings
                    risk_score += 30
                elif pe_ratio > 100:  # Very high valuation
                    risk_score += 20
                elif pe_ratio > 50:  # High valuation
                    risk_score += 10
            
            # Revenue growth
            revenue_growth = company_data.get("quarterly_revenue_growth")
            if revenue_growth is not None:
                if revenue_growth < -20:  # Declining revenue
                    risk_score += 25
                elif revenue_growth < 0:  # Negative growth
                    risk_score += 15
                elif revenue_growth > 100:  # Unsustainable growth
                    risk_score += 10
            
            # Debt levels (if available)
            debt_to_equity = company_data.get("debt_to_equity")
            if debt_to_equity:
                if debt_to_equity > 2.0:  # High debt
                    risk_score += 20
                elif debt_to_equity > 1.0:  # Moderate debt
                    risk_score += 10
            
        except Exception as e:
            self.logger.warning(f"Error assessing fundamental risk: {e}")
        
        return min(100.0, max(0.0, risk_score))
    
    async def _assess_regulatory_risk(self, company_data: Dict[str, Any]) -> float:
        """Assess regulatory risk (0-100, higher = riskier)."""
        sector = company_data.get("sector_category", "other")
        
        # High regulatory risk sectors
        high_reg_risk_sectors = {
            "biotech": 80.0,
            "pharmaceuticals": 85.0,
            "crypto": 90.0,
            "fintech": 60.0,
            "clean_energy": 50.0,  # Policy dependent
        }
        
        return high_reg_risk_sectors.get(sector, 30.0)  # Default low regulatory risk
    
    async def _assess_concentration_risk(
        self,
        symbol: str,
        portfolio_context: Optional[Dict[str, Any]]
    ) -> float:
        """Assess portfolio concentration risk (0-100, higher = riskier)."""
        if not portfolio_context:
            return 20.0  # Low risk if no existing positions
        
        # Check sector concentration
        current_sector = portfolio_context.get("symbol_sector", {}).get(symbol, "other")
        sector_exposure = portfolio_context.get("sector_exposure", {}).get(current_sector, 0)
        
        if sector_exposure > self.max_sector_concentration:
            return 90.0
        elif sector_exposure > self.max_sector_concentration * 0.8:
            return 70.0
        elif sector_exposure > self.max_sector_concentration * 0.6:
            return 50.0
        else:
            return 20.0
    
    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """Determine overall risk level from risk score."""
        if risk_score >= 85:
            return RiskLevel.EXTREME
        elif risk_score >= 70:
            return RiskLevel.VERY_HIGH
        elif risk_score >= 55:
            return RiskLevel.HIGH
        elif risk_score >= 40:
            return RiskLevel.MODERATE
        elif risk_score >= 25:
            return RiskLevel.LOW
        else:
            return RiskLevel.VERY_LOW
    
    def _identify_key_risk_factors(
        self,
        risk_components: Dict[str, Tuple[float, float]],
        company_data: Dict[str, Any]
    ) -> List[str]:
        """Identify the top risk factors."""
        risk_factors = []
        
        # Check each component for high risk
        for component, (score, weight) in risk_components.items():
            if score > 70:
                if component == "liquidity":
                    risk_factors.append("Low trading volume - difficult to exit position")
                elif component == "volatility":
                    risk_factors.append("High price volatility - large price swings")
                elif component == "market_cap":
                    risk_factors.append("Very small company - higher business risk")
                elif component == "sector":
                    sector = company_data.get("sector_category", "unknown")
                    risk_factors.append(f"High-risk sector ({sector}) - regulatory/market risks")
                elif component == "fundamental":
                    risk_factors.append("Weak financials - profitability concerns")
                elif component == "regulatory":
                    risk_factors.append("High regulatory risk - policy changes impact")
                elif component == "concentration":
                    risk_factors.append("Portfolio concentration risk - overexposure to sector")
        
        return risk_factors[:5]  # Top 5 risk factors
    
    def _generate_mitigation_suggestions(
        self,
        risk_components: Dict[str, Tuple[float, float]],
        company_data: Dict[str, Any]
    ) -> List[str]:
        """Generate risk mitigation suggestions."""
        suggestions = []
        
        for component, (score, weight) in risk_components.items():
            if score > 60:
                if component == "liquidity":
                    suggestions.append("Use smaller position sizes and limit orders")
                elif component == "volatility":
                    suggestions.append("Set tight stop losses and consider options hedging")
                elif component == "market_cap":
                    suggestions.append("Limit position size to <2% of portfolio")
                elif component == "sector":
                    suggestions.append("Diversify across multiple sectors")
                elif component == "fundamental":
                    suggestions.append("Monitor quarterly earnings closely")
                elif component == "regulatory":
                    suggestions.append("Stay informed on regulatory developments")
        
        # General suggestions
        suggestions.extend([
            "Use dollar-cost averaging for entry",
            "Set clear exit criteria before entering",
            "Monitor position daily for news/catalysts"
        ])
        
        return suggestions[:5]  # Top 5 suggestions
    
    def _calculate_max_position_size(self, risk_score: float, risk_level: RiskLevel) -> float:
        """Calculate maximum recommended position size as % of portfolio."""
        base_size = self.max_single_position
        
        # Adjust based on risk level
        risk_adjustments = {
            RiskLevel.VERY_LOW: 1.0,
            RiskLevel.LOW: 0.8,
            RiskLevel.MODERATE: 0.6,
            RiskLevel.HIGH: 0.4,
            RiskLevel.VERY_HIGH: 0.2,
            RiskLevel.EXTREME: 0.1
        }
        
        adjustment = risk_adjustments.get(risk_level, 0.5)
        return base_size * adjustment
    
    def _calculate_stop_loss_level(self, volatility_risk: float, risk_level: RiskLevel) -> float:
        """Calculate recommended stop loss level as % below entry."""
        base_stop = 0.15  # 15% base stop loss
        
        # Adjust based on volatility and risk
        if volatility_risk > 80:
            base_stop = 0.25  # 25% for very volatile stocks
        elif volatility_risk > 60:
            base_stop = 0.20  # 20% for volatile stocks
        
        # Tighter stops for higher risk
        if risk_level in [RiskLevel.VERY_HIGH, RiskLevel.EXTREME]:
            base_stop *= 0.8  # Tighter stops
        
        return base_stop
