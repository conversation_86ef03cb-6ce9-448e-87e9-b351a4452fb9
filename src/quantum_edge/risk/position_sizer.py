"""
Position Sizing Engine for QuantumEdge microcap discovery system.
Implements Kelly Criterion and risk-adjusted position sizing for optimal capital allocation.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..config.logging_config import LoggerMixin
from .risk_manager import RiskAssessment, RiskLevel


@dataclass
class PositionSize:
    """Position sizing recommendation."""
    symbol: str
    recommended_size_pct: float  # % of portfolio
    recommended_size_dollars: float  # Dollar amount
    max_shares: int  # Maximum shares to buy
    entry_strategy: str  # How to enter the position
    risk_per_share: float  # Risk per share in dollars
    expected_return: float  # Expected return %
    kelly_fraction: float  # Kelly criterion fraction
    confidence_level: float  # Confidence in sizing (0-1)
    sizing_rationale: List[str]  # Reasons for this sizing
    calculation_timestamp: datetime


class PositionSizer(LoggerMixin):
    """
    Advanced position sizing engine for microcap investments.
    Uses Kelly Criterion, risk parity, and growth potential scoring.
    """
    
    def __init__(
        self,
        portfolio_value: float = 100_000,  # Default $100k portfolio
        max_kelly_fraction: float = 0.25,  # Cap Kelly at 25%
        min_position_size: float = 0.005,  # 0.5% minimum position
        max_position_size: float = 0.05,   # 5% maximum position
        risk_free_rate: float = 0.05       # 5% risk-free rate
    ):
        """
        Initialize position sizer.
        
        Args:
            portfolio_value: Total portfolio value
            max_kelly_fraction: Maximum Kelly fraction to use
            min_position_size: Minimum position size as % of portfolio
            max_position_size: Maximum position size as % of portfolio
            risk_free_rate: Risk-free rate for calculations
        """
        self.portfolio_value = portfolio_value
        self.max_kelly_fraction = max_kelly_fraction
        self.min_position_size = min_position_size
        self.max_position_size = max_position_size
        self.risk_free_rate = risk_free_rate
        
        self.logger.info("Position sizer initialized")
    
    async def calculate_position_size(
        self,
        symbol: str,
        current_price: float,
        growth_score: float,  # 0-100 growth potential score
        risk_assessment: RiskAssessment,
        market_data: Optional[pd.DataFrame] = None,
        portfolio_context: Optional[Dict[str, Any]] = None
    ) -> PositionSize:
        """
        Calculate optimal position size using multiple methodologies.
        
        Args:
            symbol: Stock symbol
            current_price: Current stock price
            growth_score: Growth potential score (0-100)
            risk_assessment: Risk assessment from RiskManager
            market_data: Historical price data
            portfolio_context: Current portfolio composition
            
        Returns:
            PositionSize recommendation
        """
        self.logger.info(f"Calculating position size for {symbol}")
        
        # Calculate expected return from growth score
        expected_return = self._growth_score_to_expected_return(growth_score)
        
        # Calculate volatility from market data or risk assessment
        volatility = await self._estimate_volatility(market_data, risk_assessment)
        
        # Calculate Kelly fraction
        kelly_fraction = self._calculate_kelly_fraction(expected_return, volatility)
        
        # Apply risk adjustments
        risk_adjusted_fraction = self._apply_risk_adjustments(
            kelly_fraction, risk_assessment, portfolio_context
        )
        
        # Calculate final position size
        final_position_pct = self._finalize_position_size(
            risk_adjusted_fraction, risk_assessment.max_position_size_pct
        )
        
        # Convert to dollar amounts and shares
        position_dollars = final_position_pct * self.portfolio_value
        max_shares = int(position_dollars / current_price) if current_price > 0 else 0
        
        # Determine entry strategy
        entry_strategy = self._determine_entry_strategy(
            risk_assessment, volatility, growth_score
        )
        
        # Calculate risk per share
        risk_per_share = current_price * risk_assessment.stop_loss_level
        
        # Calculate confidence level
        confidence_level = self._calculate_confidence_level(
            growth_score, risk_assessment, market_data
        )
        
        # Generate sizing rationale
        sizing_rationale = self._generate_sizing_rationale(
            kelly_fraction, risk_adjusted_fraction, final_position_pct,
            risk_assessment, growth_score
        )
        
        return PositionSize(
            symbol=symbol,
            recommended_size_pct=final_position_pct,
            recommended_size_dollars=position_dollars,
            max_shares=max_shares,
            entry_strategy=entry_strategy,
            risk_per_share=risk_per_share,
            expected_return=expected_return,
            kelly_fraction=kelly_fraction,
            confidence_level=confidence_level,
            sizing_rationale=sizing_rationale,
            calculation_timestamp=datetime.now()
        )
    
    def _growth_score_to_expected_return(self, growth_score: float) -> float:
        """Convert growth score (0-100) to expected annual return."""
        # Map growth score to expected returns
        # High growth scores = higher expected returns
        
        if growth_score >= 90:
            return 2.0  # 200% expected return for exceptional opportunities
        elif growth_score >= 80:
            return 1.5  # 150% for very high potential
        elif growth_score >= 70:
            return 1.0  # 100% for high potential
        elif growth_score >= 60:
            return 0.75  # 75% for good potential
        elif growth_score >= 50:
            return 0.5   # 50% for moderate potential
        elif growth_score >= 40:
            return 0.3   # 30% for low potential
        else:
            return 0.1   # 10% for very low potential
    
    async def _estimate_volatility(
        self,
        market_data: Optional[pd.DataFrame],
        risk_assessment: RiskAssessment
    ) -> float:
        """Estimate annualized volatility."""
        if market_data is not None and len(market_data) >= 20:
            try:
                returns = market_data["close"].pct_change().dropna()
                if len(returns) >= 10:
                    daily_vol = returns.std()
                    annual_vol = daily_vol * np.sqrt(252)
                    return annual_vol
            except Exception as e:
                self.logger.warning(f"Error calculating volatility from market data: {e}")
        
        # Fallback: estimate from risk assessment
        volatility_risk = risk_assessment.volatility_risk
        
        if volatility_risk >= 90:
            return 1.0  # 100% volatility
        elif volatility_risk >= 80:
            return 0.8  # 80% volatility
        elif volatility_risk >= 70:
            return 0.6  # 60% volatility
        elif volatility_risk >= 60:
            return 0.5  # 50% volatility
        elif volatility_risk >= 50:
            return 0.4  # 40% volatility
        else:
            return 0.3  # 30% volatility
    
    def _calculate_kelly_fraction(self, expected_return: float, volatility: float) -> float:
        """Calculate Kelly Criterion fraction."""
        if volatility <= 0:
            return 0.0
        
        # Kelly formula: f = (bp - q) / b
        # Where: b = odds received (expected_return), p = probability of win, q = probability of loss
        # Simplified: f = (expected_return - risk_free_rate) / volatility^2
        
        excess_return = expected_return - self.risk_free_rate
        kelly_fraction = excess_return / (volatility ** 2)
        
        # Cap at maximum Kelly fraction
        return min(kelly_fraction, self.max_kelly_fraction)
    
    def _apply_risk_adjustments(
        self,
        kelly_fraction: float,
        risk_assessment: RiskAssessment,
        portfolio_context: Optional[Dict[str, Any]]
    ) -> float:
        """Apply risk-based adjustments to Kelly fraction."""
        adjusted_fraction = kelly_fraction
        
        # Risk level adjustments
        risk_multipliers = {
            RiskLevel.VERY_LOW: 1.0,
            RiskLevel.LOW: 0.9,
            RiskLevel.MODERATE: 0.8,
            RiskLevel.HIGH: 0.6,
            RiskLevel.VERY_HIGH: 0.4,
            RiskLevel.EXTREME: 0.2
        }
        
        risk_multiplier = risk_multipliers.get(risk_assessment.overall_risk_level, 0.5)
        adjusted_fraction *= risk_multiplier
        
        # Liquidity adjustments
        if risk_assessment.liquidity_risk > 80:
            adjusted_fraction *= 0.5  # Halve for very illiquid stocks
        elif risk_assessment.liquidity_risk > 60:
            adjusted_fraction *= 0.7  # Reduce for illiquid stocks
        
        # Concentration risk adjustments
        if risk_assessment.concentration_risk > 70:
            adjusted_fraction *= 0.6  # Reduce for concentration risk
        
        # Portfolio context adjustments
        if portfolio_context:
            # Reduce if portfolio is already concentrated
            total_positions = portfolio_context.get("total_positions", 1)
            if total_positions < 5:  # Very concentrated portfolio
                adjusted_fraction *= 0.8
        
        return max(0.0, adjusted_fraction)
    
    def _finalize_position_size(
        self,
        risk_adjusted_fraction: float,
        max_position_from_risk: float
    ) -> float:
        """Finalize position size with all constraints."""
        # Start with risk-adjusted Kelly fraction
        final_size = risk_adjusted_fraction
        
        # Apply risk manager's maximum
        final_size = min(final_size, max_position_from_risk)
        
        # Apply absolute limits
        final_size = min(final_size, self.max_position_size)
        final_size = max(final_size, self.min_position_size)
        
        return final_size
    
    def _determine_entry_strategy(
        self,
        risk_assessment: RiskAssessment,
        volatility: float,
        growth_score: float
    ) -> str:
        """Determine optimal entry strategy."""
        if risk_assessment.liquidity_risk > 80:
            return "Dollar-cost averaging over 5-10 days with limit orders"
        elif volatility > 0.6:  # High volatility
            return "Scale in over 3-5 days, buy on dips"
        elif growth_score > 85:  # Very high growth potential
            return "Quick entry over 1-2 days to capture momentum"
        elif risk_assessment.overall_risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH]:
            return "Gradual entry with tight risk management"
        else:
            return "Standard entry over 2-3 days"
    
    def _calculate_confidence_level(
        self,
        growth_score: float,
        risk_assessment: RiskAssessment,
        market_data: Optional[pd.DataFrame]
    ) -> float:
        """Calculate confidence level in position sizing (0-1)."""
        confidence = 0.5  # Base confidence
        
        # Growth score confidence
        if growth_score > 80:
            confidence += 0.2
        elif growth_score > 60:
            confidence += 0.1
        elif growth_score < 40:
            confidence -= 0.1
        
        # Risk assessment confidence
        if risk_assessment.confidence > 0.7:
            confidence += 0.15
        elif risk_assessment.confidence < 0.4:
            confidence -= 0.15
        
        # Data availability confidence
        if market_data is not None and len(market_data) >= 50:
            confidence += 0.1
        elif market_data is None or len(market_data) < 10:
            confidence -= 0.2
        
        # Risk level confidence (lower risk = higher confidence)
        if risk_assessment.overall_risk_level in [RiskLevel.VERY_LOW, RiskLevel.LOW]:
            confidence += 0.1
        elif risk_assessment.overall_risk_level in [RiskLevel.VERY_HIGH, RiskLevel.EXTREME]:
            confidence -= 0.2
        
        return max(0.1, min(1.0, confidence))
    
    def _generate_sizing_rationale(
        self,
        kelly_fraction: float,
        risk_adjusted_fraction: float,
        final_position_pct: float,
        risk_assessment: RiskAssessment,
        growth_score: float
    ) -> List[str]:
        """Generate rationale for position sizing decision."""
        rationale = []
        
        # Kelly fraction rationale
        if kelly_fraction > 0.15:
            rationale.append(f"Kelly Criterion suggests {kelly_fraction:.1%} allocation")
        else:
            rationale.append(f"Kelly Criterion suggests small {kelly_fraction:.1%} allocation")
        
        # Risk adjustments
        if risk_adjusted_fraction < kelly_fraction * 0.8:
            rationale.append("Position reduced due to high risk factors")
        
        # Growth potential
        if growth_score > 80:
            rationale.append("High growth potential supports larger position")
        elif growth_score < 50:
            rationale.append("Moderate growth potential limits position size")
        
        # Risk level impact
        if risk_assessment.overall_risk_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH]:
            rationale.append("High risk level requires smaller position")
        elif risk_assessment.overall_risk_level in [RiskLevel.LOW, RiskLevel.VERY_LOW]:
            rationale.append("Low risk level allows larger position")
        
        # Liquidity considerations
        if risk_assessment.liquidity_risk > 70:
            rationale.append("Low liquidity requires gradual entry and smaller size")
        
        # Final sizing
        if final_position_pct >= 0.04:  # 4%+
            rationale.append("Large position justified by risk-reward profile")
        elif final_position_pct <= 0.01:  # 1%-
            rationale.append("Small position due to risk concerns")
        else:
            rationale.append("Moderate position balances growth potential and risk")
        
        return rationale[:5]  # Top 5 rationale points
    
    async def calculate_portfolio_allocation(
        self,
        opportunities: List[Dict[str, Any]],
        total_capital: float,
        max_positions: int = 20
    ) -> Dict[str, PositionSize]:
        """
        Calculate optimal portfolio allocation across multiple opportunities.
        
        Args:
            opportunities: List of investment opportunities with scores and risk assessments
            total_capital: Total capital to allocate
            max_positions: Maximum number of positions
            
        Returns:
            Dictionary mapping symbols to position sizes
        """
        self.logger.info(f"Calculating portfolio allocation for {len(opportunities)} opportunities")
        
        # Update portfolio value
        self.portfolio_value = total_capital
        
        # Sort opportunities by risk-adjusted score
        sorted_opportunities = sorted(
            opportunities,
            key=lambda x: x.get("growth_score", 0) / (1 + x.get("risk_score", 50) / 100),
            reverse=True
        )
        
        # Take top opportunities up to max_positions
        selected_opportunities = sorted_opportunities[:max_positions]
        
        portfolio_allocation = {}
        total_allocated = 0.0
        
        for opp in selected_opportunities:
            try:
                symbol = opp["symbol"]
                current_price = opp.get("current_price", 10.0)  # Default price
                growth_score = opp.get("growth_score", 50.0)
                risk_assessment = opp.get("risk_assessment")
                
                if not risk_assessment:
                    continue
                
                # Calculate position size
                position_size = await self.calculate_position_size(
                    symbol=symbol,
                    current_price=current_price,
                    growth_score=growth_score,
                    risk_assessment=risk_assessment,
                    market_data=opp.get("market_data"),
                    portfolio_context={"total_positions": len(selected_opportunities)}
                )
                
                # Check if we have enough capital left
                if total_allocated + position_size.recommended_size_pct <= 0.95:  # Leave 5% cash
                    portfolio_allocation[symbol] = position_size
                    total_allocated += position_size.recommended_size_pct
                
            except Exception as e:
                self.logger.error(f"Error calculating position size for {opp.get('symbol', 'unknown')}: {e}")
                continue
        
        self.logger.info(f"Portfolio allocation complete: {len(portfolio_allocation)} positions, {total_allocated:.1%} allocated")
        
        return portfolio_allocation
