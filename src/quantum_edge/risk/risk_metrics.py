"""
Risk Metrics Calculator for QuantumEdge system.
Provides comprehensive risk measurement and monitoring capabilities.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..config.logging_config import LoggerMixin


@dataclass
class PortfolioRiskMetrics:
    """Comprehensive portfolio risk metrics."""
    portfolio_value: float
    total_var_95: float  # 95% Value at Risk
    total_var_99: float  # 99% Value at Risk
    expected_shortfall: float  # Expected loss beyond VaR
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    volatility: float
    beta: float  # Market beta
    correlation_to_market: float
    concentration_risk: float
    liquidity_risk: float
    sector_concentration: Dict[str, float]
    position_sizes: Dict[str, float]
    risk_contribution: Dict[str, float]  # Risk contribution by position
    calculation_timestamp: datetime


class RiskMetrics(LoggerMixin):
    """
    Advanced risk metrics calculator for portfolio and individual positions.
    Provides VaR, stress testing, and risk decomposition.
    """
    
    def __init__(
        self,
        confidence_levels: List[float] = [0.95, 0.99],
        lookback_days: int = 252,  # 1 year
        market_benchmark: str = "SPY"
    ):
        """
        Initialize risk metrics calculator.
        
        Args:
            confidence_levels: VaR confidence levels
            lookback_days: Days to look back for calculations
            market_benchmark: Market benchmark for beta calculation
        """
        self.confidence_levels = confidence_levels
        self.lookback_days = lookback_days
        self.market_benchmark = market_benchmark
        
        self.logger.info("Risk metrics calculator initialized")
    
    async def calculate_portfolio_risk(
        self,
        portfolio_positions: Dict[str, Dict[str, Any]],
        market_data: Dict[str, pd.DataFrame],
        portfolio_value: float
    ) -> PortfolioRiskMetrics:
        """
        Calculate comprehensive portfolio risk metrics.
        
        Args:
            portfolio_positions: Dictionary of positions with weights and data
            market_data: Historical market data for each position
            portfolio_value: Total portfolio value
            
        Returns:
            PortfolioRiskMetrics object
        """
        self.logger.info("Calculating portfolio risk metrics")
        
        # Calculate portfolio returns
        portfolio_returns = await self._calculate_portfolio_returns(
            portfolio_positions, market_data
        )
        
        # Value at Risk calculations
        var_95 = self._calculate_var(portfolio_returns, 0.95) * portfolio_value
        var_99 = self._calculate_var(portfolio_returns, 0.99) * portfolio_value
        
        # Expected Shortfall (Conditional VaR)
        expected_shortfall = self._calculate_expected_shortfall(
            portfolio_returns, 0.95
        ) * portfolio_value
        
        # Performance metrics
        sharpe_ratio = self._calculate_sharpe_ratio(portfolio_returns)
        sortino_ratio = self._calculate_sortino_ratio(portfolio_returns)
        max_drawdown = self._calculate_max_drawdown(portfolio_returns)
        volatility = portfolio_returns.std() * np.sqrt(252)  # Annualized
        
        # Market risk metrics
        market_returns = market_data.get(self.market_benchmark)
        if market_returns is not None:
            beta, correlation = self._calculate_market_risk_metrics(
                portfolio_returns, market_returns["close"].pct_change().dropna()
            )
        else:
            beta, correlation = 1.0, 0.5  # Default values
        
        # Concentration and liquidity risk
        concentration_risk = self._calculate_concentration_risk(portfolio_positions)
        liquidity_risk = self._calculate_liquidity_risk(portfolio_positions)
        
        # Sector concentration
        sector_concentration = self._calculate_sector_concentration(portfolio_positions)
        
        # Position sizes
        position_sizes = {
            symbol: pos.get("weight", 0.0)
            for symbol, pos in portfolio_positions.items()
        }
        
        # Risk contribution by position
        risk_contribution = await self._calculate_risk_contribution(
            portfolio_positions, market_data, portfolio_returns
        )
        
        return PortfolioRiskMetrics(
            portfolio_value=portfolio_value,
            total_var_95=var_95,
            total_var_99=var_99,
            expected_shortfall=expected_shortfall,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            volatility=volatility,
            beta=beta,
            correlation_to_market=correlation,
            concentration_risk=concentration_risk,
            liquidity_risk=liquidity_risk,
            sector_concentration=sector_concentration,
            position_sizes=position_sizes,
            risk_contribution=risk_contribution,
            calculation_timestamp=datetime.now()
        )
    
    async def _calculate_portfolio_returns(
        self,
        portfolio_positions: Dict[str, Dict[str, Any]],
        market_data: Dict[str, pd.DataFrame]
    ) -> pd.Series:
        """Calculate historical portfolio returns."""
        portfolio_returns = None
        
        for symbol, position in portfolio_positions.items():
            weight = position.get("weight", 0.0)
            
            if symbol in market_data and weight > 0:
                stock_data = market_data[symbol]
                if len(stock_data) > 1:
                    stock_returns = stock_data["close"].pct_change().dropna()
                    weighted_returns = stock_returns * weight
                    
                    if portfolio_returns is None:
                        portfolio_returns = weighted_returns
                    else:
                        # Align indices and add
                        common_index = portfolio_returns.index.intersection(weighted_returns.index)
                        portfolio_returns = (
                            portfolio_returns.loc[common_index] + 
                            weighted_returns.loc[common_index]
                        )
        
        if portfolio_returns is None:
            # Return empty series if no data
            return pd.Series(dtype=float)
        
        return portfolio_returns.dropna()
    
    def _calculate_var(self, returns: pd.Series, confidence_level: float) -> float:
        """Calculate Value at Risk at given confidence level."""
        if len(returns) == 0:
            return 0.0
        
        return -np.percentile(returns, (1 - confidence_level) * 100)
    
    def _calculate_expected_shortfall(self, returns: pd.Series, confidence_level: float) -> float:
        """Calculate Expected Shortfall (Conditional VaR)."""
        if len(returns) == 0:
            return 0.0
        
        var_threshold = -self._calculate_var(returns, confidence_level)
        tail_losses = returns[returns <= var_threshold]
        
        if len(tail_losses) == 0:
            return 0.0
        
        return -tail_losses.mean()
    
    def _calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.05) -> float:
        """Calculate Sharpe ratio."""
        if len(returns) == 0 or returns.std() == 0:
            return 0.0
        
        excess_returns = returns.mean() * 252 - risk_free_rate  # Annualized
        volatility = returns.std() * np.sqrt(252)  # Annualized
        
        return excess_returns / volatility
    
    def _calculate_sortino_ratio(self, returns: pd.Series, risk_free_rate: float = 0.05) -> float:
        """Calculate Sortino ratio (downside deviation)."""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns.mean() * 252 - risk_free_rate  # Annualized
        downside_returns = returns[returns < 0]
        
        if len(downside_returns) == 0:
            return float('inf')  # No downside
        
        downside_deviation = downside_returns.std() * np.sqrt(252)
        
        if downside_deviation == 0:
            return float('inf')
        
        return excess_returns / downside_deviation
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """Calculate maximum drawdown."""
        if len(returns) == 0:
            return 0.0
        
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        
        return drawdown.min()
    
    def _calculate_market_risk_metrics(
        self,
        portfolio_returns: pd.Series,
        market_returns: pd.Series
    ) -> Tuple[float, float]:
        """Calculate beta and correlation to market."""
        if len(portfolio_returns) == 0 or len(market_returns) == 0:
            return 1.0, 0.0
        
        # Align returns
        common_index = portfolio_returns.index.intersection(market_returns.index)
        if len(common_index) < 10:  # Need minimum data
            return 1.0, 0.0
        
        port_aligned = portfolio_returns.loc[common_index]
        market_aligned = market_returns.loc[common_index]
        
        # Calculate correlation
        correlation = port_aligned.corr(market_aligned)
        
        # Calculate beta
        market_variance = market_aligned.var()
        if market_variance == 0:
            beta = 1.0
        else:
            covariance = port_aligned.cov(market_aligned)
            beta = covariance / market_variance
        
        return beta, correlation
    
    def _calculate_concentration_risk(self, portfolio_positions: Dict[str, Dict[str, Any]]) -> float:
        """Calculate portfolio concentration risk (Herfindahl index)."""
        weights = [pos.get("weight", 0.0) for pos in portfolio_positions.values()]
        
        if not weights or sum(weights) == 0:
            return 0.0
        
        # Normalize weights
        total_weight = sum(weights)
        normalized_weights = [w / total_weight for w in weights]
        
        # Herfindahl index (sum of squared weights)
        herfindahl_index = sum(w ** 2 for w in normalized_weights)
        
        return herfindahl_index
    
    def _calculate_liquidity_risk(self, portfolio_positions: Dict[str, Dict[str, Any]]) -> float:
        """Calculate portfolio liquidity risk."""
        total_weight = 0.0
        weighted_liquidity_risk = 0.0
        
        for position in portfolio_positions.values():
            weight = position.get("weight", 0.0)
            liquidity_risk = position.get("liquidity_risk", 50.0)  # Default medium risk
            
            total_weight += weight
            weighted_liquidity_risk += weight * liquidity_risk
        
        if total_weight == 0:
            return 50.0  # Default medium risk
        
        return weighted_liquidity_risk / total_weight
    
    def _calculate_sector_concentration(self, portfolio_positions: Dict[str, Dict[str, Any]]) -> Dict[str, float]:
        """Calculate concentration by sector."""
        sector_weights = {}
        
        for position in portfolio_positions.values():
            weight = position.get("weight", 0.0)
            sector = position.get("sector", "other")
            
            sector_weights[sector] = sector_weights.get(sector, 0.0) + weight
        
        return sector_weights
    
    async def _calculate_risk_contribution(
        self,
        portfolio_positions: Dict[str, Dict[str, Any]],
        market_data: Dict[str, pd.DataFrame],
        portfolio_returns: pd.Series
    ) -> Dict[str, float]:
        """Calculate risk contribution of each position."""
        risk_contributions = {}
        
        if len(portfolio_returns) == 0:
            return {symbol: 0.0 for symbol in portfolio_positions.keys()}
        
        portfolio_volatility = portfolio_returns.std()
        
        if portfolio_volatility == 0:
            return {symbol: 0.0 for symbol in portfolio_positions.keys()}
        
        for symbol, position in portfolio_positions.items():
            weight = position.get("weight", 0.0)
            
            if symbol in market_data and weight > 0:
                stock_data = market_data[symbol]
                if len(stock_data) > 1:
                    stock_returns = stock_data["close"].pct_change().dropna()
                    
                    # Calculate correlation with portfolio
                    common_index = portfolio_returns.index.intersection(stock_returns.index)
                    if len(common_index) > 10:
                        port_aligned = portfolio_returns.loc[common_index]
                        stock_aligned = stock_returns.loc[common_index]
                        
                        correlation = port_aligned.corr(stock_aligned)
                        stock_volatility = stock_aligned.std()
                        
                        # Risk contribution = weight * correlation * stock_vol / portfolio_vol
                        risk_contribution = (
                            weight * correlation * stock_volatility / portfolio_volatility
                        )
                        
                        risk_contributions[symbol] = risk_contribution
                    else:
                        risk_contributions[symbol] = weight  # Fallback to weight
                else:
                    risk_contributions[symbol] = weight
            else:
                risk_contributions[symbol] = 0.0
        
        return risk_contributions
    
    async def stress_test_portfolio(
        self,
        portfolio_positions: Dict[str, Dict[str, Any]],
        market_data: Dict[str, pd.DataFrame],
        stress_scenarios: List[Dict[str, float]]
    ) -> Dict[str, float]:
        """
        Perform stress testing on portfolio.
        
        Args:
            portfolio_positions: Portfolio positions
            market_data: Historical market data
            stress_scenarios: List of stress scenarios (e.g., {"market_crash": -0.3})
            
        Returns:
            Dictionary of scenario names to portfolio impact
        """
        stress_results = {}
        
        for scenario in stress_scenarios:
            scenario_name = list(scenario.keys())[0]
            market_shock = list(scenario.values())[0]
            
            # Calculate portfolio impact
            total_impact = 0.0
            
            for symbol, position in portfolio_positions.items():
                weight = position.get("weight", 0.0)
                beta = position.get("beta", 1.0)  # Default beta of 1
                
                # Estimate stock impact based on beta and market shock
                stock_impact = beta * market_shock
                position_impact = weight * stock_impact
                
                total_impact += position_impact
            
            stress_results[scenario_name] = total_impact
        
        return stress_results
    
    def generate_risk_report(self, risk_metrics: PortfolioRiskMetrics) -> Dict[str, Any]:
        """Generate comprehensive risk report."""
        return {
            "portfolio_summary": {
                "total_value": risk_metrics.portfolio_value,
                "number_of_positions": len(risk_metrics.position_sizes),
                "largest_position": max(risk_metrics.position_sizes.values()) if risk_metrics.position_sizes else 0,
                "cash_allocation": max(0, 1.0 - sum(risk_metrics.position_sizes.values()))
            },
            "risk_metrics": {
                "var_95_daily": risk_metrics.total_var_95,
                "var_99_daily": risk_metrics.total_var_99,
                "expected_shortfall": risk_metrics.expected_shortfall,
                "volatility_annual": risk_metrics.volatility,
                "max_drawdown": risk_metrics.max_drawdown,
                "concentration_risk": risk_metrics.concentration_risk,
                "liquidity_risk": risk_metrics.liquidity_risk
            },
            "performance_metrics": {
                "sharpe_ratio": risk_metrics.sharpe_ratio,
                "sortino_ratio": risk_metrics.sortino_ratio,
                "beta": risk_metrics.beta,
                "correlation_to_market": risk_metrics.correlation_to_market
            },
            "concentration_analysis": {
                "sector_concentration": risk_metrics.sector_concentration,
                "top_5_positions": dict(
                    sorted(risk_metrics.position_sizes.items(), 
                          key=lambda x: x[1], reverse=True)[:5]
                ),
                "risk_contribution": risk_metrics.risk_contribution
            },
            "risk_warnings": self._generate_risk_warnings(risk_metrics),
            "recommendations": self._generate_risk_recommendations(risk_metrics)
        }
    
    def _generate_risk_warnings(self, risk_metrics: PortfolioRiskMetrics) -> List[str]:
        """Generate risk warnings based on metrics."""
        warnings = []
        
        # Concentration warnings
        if risk_metrics.concentration_risk > 0.4:
            warnings.append("High portfolio concentration - consider diversification")
        
        # Liquidity warnings
        if risk_metrics.liquidity_risk > 70:
            warnings.append("High liquidity risk - may be difficult to exit positions quickly")
        
        # Volatility warnings
        if risk_metrics.volatility > 0.4:  # 40% annual volatility
            warnings.append("Very high portfolio volatility - expect large price swings")
        
        # Drawdown warnings
        if risk_metrics.max_drawdown < -0.3:  # 30% drawdown
            warnings.append("Large historical drawdowns - portfolio has experienced significant losses")
        
        # Sector concentration warnings
        for sector, weight in risk_metrics.sector_concentration.items():
            if weight > 0.5:  # 50% in one sector
                warnings.append(f"High concentration in {sector} sector ({weight:.1%})")
        
        # Position size warnings
        for symbol, weight in risk_metrics.position_sizes.items():
            if weight > 0.1:  # 10% in single position
                warnings.append(f"Large position in {symbol} ({weight:.1%})")
        
        return warnings
    
    def _generate_risk_recommendations(self, risk_metrics: PortfolioRiskMetrics) -> List[str]:
        """Generate risk management recommendations."""
        recommendations = []
        
        # Diversification recommendations
        if risk_metrics.concentration_risk > 0.3:
            recommendations.append("Consider adding more positions to reduce concentration risk")
        
        # Liquidity recommendations
        if risk_metrics.liquidity_risk > 60:
            recommendations.append("Consider reducing position sizes in illiquid stocks")
        
        # Volatility recommendations
        if risk_metrics.volatility > 0.35:
            recommendations.append("Consider hedging strategies or reducing position sizes")
        
        # Sector diversification
        dominant_sectors = [
            sector for sector, weight in risk_metrics.sector_concentration.items()
            if weight > 0.3
        ]
        if dominant_sectors:
            recommendations.append(f"Consider diversifying away from {', '.join(dominant_sectors)}")
        
        # Performance recommendations
        if risk_metrics.sharpe_ratio < 0.5:
            recommendations.append("Low risk-adjusted returns - review investment selection")
        
        return recommendations
