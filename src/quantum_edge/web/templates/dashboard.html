{% extends "base.html" %}

{% block title %}Dashboard - QuantumEdge{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-6 fw-bold text-dark">
            <i class="fas fa-tachometer-alt text-primary me-3"></i>
            QuantumEdge Dashboard
        </h1>
        <p class="lead text-muted">Real-time microcap discovery and portfolio monitoring</p>
    </div>
</div>

<!-- Key Metrics Row -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="ytd-return">+78.3%</div>
            <div class="metric-label">YTD Return</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="metric-value" id="total-opportunities">1,247</div>
            <div class="metric-label">Total Opportunities</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="metric-value" id="high-potential">89</div>
            <div class="metric-label">High Potential (>80)</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
            <div class="metric-value" id="portfolio-value">$156,780</div>
            <div class="metric-label">Portfolio Value</div>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-scatter me-2"></i>
                    Growth vs Risk Analysis
                </h5>
            </div>
            <div class="card-body">
                <div id="discovery-chart" style="height: 400px;"></div>
                <div class="loading-spinner" id="discovery-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Sector Performance
                </h5>
            </div>
            <div class="card-body">
                <div id="sector-chart" style="height: 400px;"></div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Performance vs Benchmark
                </h5>
            </div>
            <div class="card-body">
                <div id="equity-curve" style="height: 350px;"></div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Monthly Returns
                </h5>
            </div>
            <div class="card-body">
                <div id="monthly-heatmap" style="height: 200px;"></div>
                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted">Best Month</small>
                            <div class="fw-bold text-success">+22.0%</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">Worst Month</small>
                            <div class="fw-bold text-danger">-5.0%</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">Avg Month</small>
                            <div class="fw-bold text-primary">+11.2%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity Row -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    Recent High-Potential Discoveries
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Score</th>
                                <th>Catalyst</th>
                                <th>Discovered</th>
                            </tr>
                        </thead>
                        <tbody id="recent-discoveries">
                            <!-- Will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Risk Alerts
                </h5>
            </div>
            <div class="card-body">
                <div id="risk-alerts">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status Row -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server me-2"></i>
                    System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-2">
                            <span class="status-indicator status-online"></span>
                            <span class="fw-bold">Discovery Engine</span>
                        </div>
                        <small class="text-muted">Operational</small>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-2">
                            <span class="status-indicator status-online"></span>
                            <span class="fw-bold">Risk Manager</span>
                        </div>
                        <small class="text-muted">Operational</small>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-2">
                            <span class="status-indicator status-online"></span>
                            <span class="fw-bold">Data Sources</span>
                        </div>
                        <small class="text-muted">5/5 Online</small>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-2">
                            <span class="status-indicator status-online"></span>
                            <span class="fw-bold">Last Update</span>
                        </div>
                        <small class="text-muted" id="last-update">2 minutes ago</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Dashboard initialization
    $(document).ready(function() {
        loadDashboardData();
        
        // Refresh data every 5 minutes
        setInterval(loadDashboardData, 5 * 60 * 1000);
    });
    
    async function loadDashboardData() {
        try {
            showLoading('discovery-loading');
            
            // Load dashboard stats
            const stats = await apiCall('/api/dashboard/stats');
            updateDashboardStats(stats);
            
            // Load discovery results for charts
            const discoveryResults = await apiCall('/api/discovery/results');
            updateCharts(discoveryResults);
            
            hideLoading('discovery-loading');
            
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            hideLoading('discovery-loading');
        }
    }
    
    function updateDashboardStats(stats) {
        // Update key metrics
        document.getElementById('ytd-return').textContent = stats.performance_metrics.ytd_return;
        document.getElementById('total-opportunities').textContent = stats.performance_metrics.total_opportunities.toLocaleString();
        document.getElementById('high-potential').textContent = stats.performance_metrics.high_potential_count;
        document.getElementById('portfolio-value').textContent = formatCurrency(stats.performance_metrics.portfolio_value);
        
        // Update recent discoveries
        const recentDiscoveries = document.getElementById('recent-discoveries');
        recentDiscoveries.innerHTML = '';
        
        stats.recent_discoveries.forEach(discovery => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><strong>${discovery.symbol}</strong></td>
                <td><span class="badge ${getScoreClass(discovery.score)}">${discovery.score}</span></td>
                <td><small>${discovery.catalyst}</small></td>
                <td><small class="text-muted">${discovery.discovered}</small></td>
            `;
            recentDiscoveries.appendChild(row);
        });
        
        // Update risk alerts
        const riskAlerts = document.getElementById('risk-alerts');
        riskAlerts.innerHTML = '';
        
        stats.risk_alerts.forEach(alert => {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${alert.severity === 'high' ? 'danger' : alert.severity === 'medium' ? 'warning' : 'info'} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${alert.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            riskAlerts.appendChild(alertDiv);
        });
    }
    
    function updateCharts(discoveryResults) {
        // Mock chart data - in real implementation, this would come from the dashboard manager
        
        // Discovery scatter plot
        const discoveryData = [{
            x: [15, 25, 35, 45, 55, 65],
            y: [87.5, 84.2, 81.7, 78.3, 75.1, 72.8],
            mode: 'markers',
            type: 'scatter',
            text: ['ATOS', 'BBAI', 'PLUG', 'CLSK', 'FCEL', 'BLDP'],
            marker: {
                size: [20, 18, 16, 14, 12, 10],
                color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#4ECDC4', '#45B7D1', '#45B7D1'],
                opacity: 0.7
            },
            hovertemplate: '<b>%{text}</b><br>Growth Score: %{y}<br>Risk Score: %{x}<extra></extra>'
        }];
        
        const discoveryLayout = {
            title: '',
            xaxis: { title: 'Risk Score' },
            yaxis: { title: 'Growth Potential Score' },
            hovermode: 'closest',
            margin: { t: 20, r: 20, b: 40, l: 60 }
        };
        
        Plotly.newPlot('discovery-chart', discoveryData, discoveryLayout, {responsive: true});
        
        // Sector performance chart
        const sectorData = [{
            x: ['Biotech', 'AI/ML', 'Clean Energy', 'Space Tech', 'Quantum'],
            y: [87.5, 84.2, 81.7, 78.3, 75.1],
            type: 'bar',
            marker: { color: '#4ECDC4' },
            text: ['87.5', '84.2', '81.7', '78.3', '75.1'],
            textposition: 'auto'
        }];
        
        const sectorLayout = {
            title: '',
            xaxis: { title: 'Sector' },
            yaxis: { title: 'Average Score' },
            margin: { t: 20, r: 20, b: 60, l: 60 }
        };
        
        Plotly.newPlot('sector-chart', sectorData, sectorLayout, {responsive: true});
        
        // Equity curve
        const dates = [];
        const strategyValues = [];
        const benchmarkValues = [];
        
        for (let i = 0; i < 365; i++) {
            const date = new Date();
            date.setDate(date.getDate() - (365 - i));
            dates.push(date.toISOString().split('T')[0]);
            
            // Mock equity curve with growth
            strategyValues.push(100000 * (1 + (i / 365) * 0.783 + Math.sin(i / 30) * 0.05));
            benchmarkValues.push(100000 * (1 + (i / 365) * 0.12 + Math.sin(i / 40) * 0.02));
        }
        
        const equityData = [
            {
                x: dates,
                y: strategyValues,
                type: 'scatter',
                mode: 'lines',
                name: 'QuantumEdge Strategy',
                line: { color: '#4ECDC4', width: 2 }
            },
            {
                x: dates,
                y: benchmarkValues,
                type: 'scatter',
                mode: 'lines',
                name: 'Benchmark (SPY)',
                line: { color: '#FF6B6B', width: 2, dash: 'dash' }
            }
        ];
        
        const equityLayout = {
            title: '',
            xaxis: { title: 'Date' },
            yaxis: { title: 'Portfolio Value ($)' },
            hovermode: 'x unified',
            margin: { t: 20, r: 20, b: 40, l: 80 }
        };
        
        Plotly.newPlot('equity-curve', equityData, equityLayout, {responsive: true});
        
        // Monthly returns heatmap
        const monthlyData = [{
            z: [[0.12, 0.08, -0.05, 0.15, 0.22, 0.09, 0.18, -0.03, 0.11, 0.07, 0.14, 0.06]],
            x: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            y: ['2023'],
            type: 'heatmap',
            colorscale: 'RdYlGn',
            zmid: 0,
            text: [['12.0%', '8.0%', '-5.0%', '15.0%', '22.0%', '9.0%', '18.0%', '-3.0%', '11.0%', '7.0%', '14.0%', '6.0%']],
            texttemplate: '%{text}',
            textfont: { size: 12 }
        }];
        
        const monthlyLayout = {
            title: '',
            margin: { t: 20, r: 20, b: 40, l: 60 }
        };
        
        Plotly.newPlot('monthly-heatmap', monthlyData, monthlyLayout, {responsive: true});
    }
</script>
{% endblock %}
