<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}QuantumEdge Microcap Discovery{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <style>
        :root {
            --primary-color: #4ECDC4;
            --secondary-color: #45B7D1;
            --accent-color: #FF6B6B;
            --dark-color: #2C3E50;
            --light-color: #ECF0F1;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--dark-color), #34495e);
            min-height: 100vh;
            padding-top: 20px;
        }
        
        .sidebar .nav-link {
            color: #bdc3c7;
            padding: 12px 20px;
            margin: 5px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: white;
            transform: translateX(5px);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background-color: #2ecc71;
            animation: pulse 2s infinite;
        }
        
        .status-offline {
            background-color: #e74c3c;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(78, 205, 196, 0.1);
        }
        
        .badge-score {
            font-size: 0.9rem;
            padding: 8px 12px;
            border-radius: 20px;
        }
        
        .score-excellent {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }
        
        .score-good {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .score-moderate {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }
        
        .score-poor {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line me-2"></i>QuantumEdge
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <span class="status-indicator status-online"></span>
                            System Online
                        </span>
                    </li>
                    <li class="nav-item">
                        <span class="navbar-text me-3">
                            <i class="fas fa-clock me-1"></i>
                            <span id="current-time"></span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link {% if request.endpoint == 'discovery' %}active{% endif %}" href="{{ url_for('discovery') }}">
                        <i class="fas fa-search me-2"></i>Discovery
                    </a>
                    <a class="nav-link {% if request.endpoint == 'risk' %}active{% endif %}" href="{{ url_for('risk') }}">
                        <i class="fas fa-shield-alt me-2"></i>Risk Analysis
                    </a>
                    <a class="nav-link {% if request.endpoint == 'portfolio' %}active{% endif %}" href="{{ url_for('portfolio') }}">
                        <i class="fas fa-briefcase me-2"></i>Portfolio
                    </a>
                    <a class="nav-link {% if request.endpoint == 'backtesting' %}active{% endif %}" href="{{ url_for('backtesting') }}">
                        <i class="fas fa-chart-bar me-2"></i>Backtesting
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10">
                <div class="container-fluid py-4">
                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString();
        }
        
        setInterval(updateTime, 1000);
        updateTime();
        
        // Utility functions
        function showLoading(elementId) {
            document.getElementById(elementId).style.display = 'block';
        }
        
        function hideLoading(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }
        
        function formatCurrency(value) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(value);
        }
        
        function formatPercentage(value) {
            return (value * 100).toFixed(1) + '%';
        }
        
        function getScoreClass(score) {
            if (score >= 80) return 'score-excellent';
            if (score >= 70) return 'score-good';
            if (score >= 60) return 'score-moderate';
            return 'score-poor';
        }
        
        // API helper functions
        async function apiCall(url, method = 'GET', data = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            try {
                const response = await fetch(url, options);
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.error || 'API call failed');
                }
                
                return result.data;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
