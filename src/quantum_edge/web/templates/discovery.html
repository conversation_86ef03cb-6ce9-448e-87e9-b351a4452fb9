{% extends "base.html" %}

{% block title %}Discovery - QuantumEdge{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="display-6 fw-bold text-dark">
            <i class="fas fa-search text-primary me-3"></i>
            Microcap Discovery Engine
        </h1>
        <p class="lead text-muted">Discover high-growth potential microcap opportunities</p>
    </div>
</div>

<!-- Discovery Controls -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Discovery Parameters
                </h5>
            </div>
            <div class="card-body">
                <form id="discovery-form">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="target-sectors" class="form-label">Target Sectors</label>
                            <select class="form-select" id="target-sectors" multiple>
                                <option value="biotech">Biotechnology</option>
                                <option value="ai_ml">AI/Machine Learning</option>
                                <option value="clean_energy">Clean Energy</option>
                                <option value="space_tech">Space Technology</option>
                                <option value="quantum">Quantum Computing</option>
                                <option value="cybersecurity">Cybersecurity</option>
                                <option value="fintech">FinTech</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="max-results" class="form-label">Max Results</label>
                            <select class="form-select" id="max-results">
                                <option value="25">25</option>
                                <option value="50" selected>50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="min-score" class="form-label">Min Growth Score</label>
                            <select class="form-select" id="min-score">
                                <option value="60">60</option>
                                <option value="70" selected>70</option>
                                <option value="80">80</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-play me-2"></i>Run Discovery
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Discovery Results -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-star me-2"></i>
                    Top Opportunities
                </h5>
                <span class="badge bg-primary" id="results-count">0 results</span>
            </div>
            <div class="card-body">
                <div class="loading-spinner" id="discovery-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Analyzing market data...</p>
                </div>
                
                <div class="table-responsive" id="results-table" style="display: none;">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Company</th>
                                <th>Score</th>
                                <th>Sector</th>
                                <th>Market Cap</th>
                                <th>Key Catalyst</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="opportunities-tbody">
                            <!-- Results will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Sector Breakdown
                </h5>
            </div>
            <div class="card-body">
                <div id="sector-breakdown-chart" style="height: 300px;"></div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Discovery Stats
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="metric-value text-primary" id="companies-analyzed">0</div>
                        <div class="metric-label">Companies Analyzed</div>
                    </div>
                    <div class="col-6">
                        <div class="metric-value text-success" id="catalysts-detected">0</div>
                        <div class="metric-label">Catalysts Detected</div>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="metric-value text-warning" id="processing-time">0s</div>
                        <div class="metric-label">Processing Time</div>
                    </div>
                    <div class="col-6">
                        <div class="metric-value text-info" id="last-discovery">Never</div>
                        <div class="metric-label">Last Discovery</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Company Detail Modal -->
<div class="modal fade" id="companyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="companyModalTitle">Company Analysis</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="companyModalBody">
                <!-- Company details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="add-to-watchlist">
                    <i class="fas fa-eye me-2"></i>Add to Watchlist
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentResults = [];
    
    $(document).ready(function() {
        // Load initial results
        loadDiscoveryResults();
        
        // Handle form submission
        $('#discovery-form').on('submit', function(e) {
            e.preventDefault();
            runDiscovery();
        });
    });
    
    async function runDiscovery() {
        try {
            showLoading('discovery-loading');
            document.getElementById('results-table').style.display = 'none';
            
            const formData = {
                target_sectors: Array.from(document.getElementById('target-sectors').selectedOptions).map(option => option.value),
                max_results: parseInt(document.getElementById('max-results').value),
                min_score: parseInt(document.getElementById('min-score').value)
            };
            
            const results = await apiCall('/api/discovery/run', 'POST', formData);
            
            currentResults = results;
            displayResults(results);
            updateDiscoveryStats(results);
            
            hideLoading('discovery-loading');
            document.getElementById('results-table').style.display = 'block';
            
        } catch (error) {
            console.error('Error running discovery:', error);
            hideLoading('discovery-loading');
            alert('Error running discovery: ' + error.message);
        }
    }
    
    async function loadDiscoveryResults() {
        try {
            const results = await apiCall('/api/discovery/results');
            currentResults = results;
            displayResults(results);
            updateDiscoveryStats(results);
            document.getElementById('results-table').style.display = 'block';
        } catch (error) {
            console.error('Error loading discovery results:', error);
        }
    }
    
    function displayResults(results) {
        const tbody = document.getElementById('opportunities-tbody');
        tbody.innerHTML = '';
        
        const opportunities = results.top_opportunities || [];
        
        document.getElementById('results-count').textContent = `${opportunities.length} results`;
        
        opportunities.forEach(opportunity => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <strong>${opportunity.symbol}</strong>
                </td>
                <td>
                    <div class="fw-bold">${opportunity.company_name}</div>
                    <small class="text-muted">${opportunity.sector.replace('_', ' ').toUpperCase()}</small>
                </td>
                <td>
                    <span class="badge ${getScoreClass(opportunity.total_score)} badge-score">
                        ${opportunity.total_score.toFixed(1)}
                    </span>
                </td>
                <td>
                    <span class="badge bg-secondary">${opportunity.sector.replace('_', ' ')}</span>
                </td>
                <td>
                    ${formatCurrency(opportunity.market_cap)}
                </td>
                <td>
                    <small>${opportunity.key_catalysts[0] || 'N/A'}</small>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="showCompanyDetails('${opportunity.symbol}')">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
        
        // Update sector breakdown chart
        updateSectorChart(results.sector_analysis || {});
    }
    
    function updateDiscoveryStats(results) {
        const stats = results.processing_stats || {};
        
        document.getElementById('companies-analyzed').textContent = (stats.companies_analyzed || 0).toLocaleString();
        document.getElementById('catalysts-detected').textContent = (stats.catalysts_detected || 0).toLocaleString();
        document.getElementById('processing-time').textContent = (stats.processing_time_seconds || 0).toFixed(1) + 's';
        document.getElementById('last-discovery').textContent = new Date().toLocaleTimeString();
    }
    
    function updateSectorChart(sectorData) {
        const sectors = Object.keys(sectorData);
        const counts = sectors.map(sector => sectorData[sector].total_companies || 0);
        
        const data = [{
            labels: sectors.map(s => s.replace('_', ' ').toUpperCase()),
            values: counts,
            type: 'pie',
            hole: 0.4,
            marker: {
                colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
            }
        }];
        
        const layout = {
            margin: { t: 20, r: 20, b: 20, l: 20 },
            showlegend: true,
            legend: { orientation: 'v', x: 1, y: 0.5 }
        };
        
        Plotly.newPlot('sector-breakdown-chart', data, layout, {responsive: true});
    }
    
    async function showCompanyDetails(symbol) {
        try {
            const details = await apiCall(`/api/company/${symbol}`);
            
            document.getElementById('companyModalTitle').textContent = `${details.company_name} (${symbol})`;
            
            const modalBody = document.getElementById('companyModalBody');
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Growth Scores</h6>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Total Score:</span>
                                <span class="badge ${getScoreClass(details.growth_score.total_score)} badge-score">
                                    ${details.growth_score.total_score.toFixed(1)}
                                </span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Catalyst Score:</span>
                                <span class="fw-bold">${details.growth_score.catalyst_score.toFixed(1)}</span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Technical Score:</span>
                                <span class="fw-bold">${details.growth_score.technical_score.toFixed(1)}</span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Fundamental Score:</span>
                                <span class="fw-bold">${details.growth_score.fundamental_score.toFixed(1)}</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Market Data</h6>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Current Price:</span>
                                <span class="fw-bold">${formatCurrency(details.market_data.current_price)}</span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>1D Change:</span>
                                <span class="fw-bold ${details.market_data.price_change_1d >= 0 ? 'text-success' : 'text-danger'}">
                                    ${details.market_data.price_change_1d.toFixed(1)}%
                                </span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Market Cap:</span>
                                <span class="fw-bold">${formatCurrency(details.market_data.market_cap)}</span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>Volume:</span>
                                <span class="fw-bold">${details.market_data.volume.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-12">
                        <h6>Recent Catalysts</h6>
                        ${details.catalysts.map(catalyst => `
                            <div class="alert alert-info">
                                <div class="d-flex justify-content-between">
                                    <strong>${catalyst.title}</strong>
                                    <span class="badge bg-primary">${catalyst.impact_score.toFixed(1)}</span>
                                </div>
                                <p class="mb-1">${catalyst.description}</p>
                                <small class="text-muted">
                                    ${catalyst.source} • ${new Date(catalyst.event_date).toLocaleDateString()}
                                </small>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-12">
                        <h6>Risk Assessment</h6>
                        <div class="alert alert-warning">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Overall Risk Level:</span>
                                <span class="badge bg-warning text-dark">${details.risk_assessment.overall_risk_level.toUpperCase()}</span>
                            </div>
                            <div class="mb-2">
                                <strong>Key Risk Factors:</strong>
                                <ul class="mb-0">
                                    ${details.risk_assessment.key_risk_factors.map(factor => `<li>${factor}</li>`).join('')}
                                </ul>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <small>Max Position Size: ${(details.risk_assessment.max_position_size_pct * 100).toFixed(1)}%</small>
                                </div>
                                <div class="col-6">
                                    <small>Stop Loss Level: ${(details.risk_assessment.stop_loss_level * 100).toFixed(1)}%</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('companyModal'));
            modal.show();
            
        } catch (error) {
            console.error('Error loading company details:', error);
            alert('Error loading company details: ' + error.message);
        }
    }
</script>
{% endblock %}
