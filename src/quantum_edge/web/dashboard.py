"""
Dashboard Manager for QuantumEdge web interface.
Handles data visualization and dashboard components.
"""

import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional

import pandas as pd
import plotly.graph_objs as go
import plotly.express as px
from plotly.utils import PlotlyJSONEncoder

from ..config.logging_config import LoggerMixin


class DashboardManager(LoggerMixin):
    """
    Manages dashboard data visualization and components.
    Creates interactive charts and graphs for the web interface.
    """
    
    def __init__(self):
        """Initialize dashboard manager."""
        self.logger.info("Dashboard manager initialized")
    
    def create_discovery_chart(self, opportunities: List[Dict[str, Any]]) -> str:
        """Create discovery opportunities scatter plot."""
        if not opportunities:
            return self._empty_chart()
        
        # Extract data for plotting
        symbols = [opp.get("symbol", "") for opp in opportunities]
        growth_scores = [opp.get("total_score", 0) for opp in opportunities]
        risk_scores = [abs(opp.get("risk_score", 0)) for opp in opportunities]  # Make positive for plotting
        market_caps = [opp.get("market_cap", 0) / 1_000_000 for opp in opportunities]  # Convert to millions
        sectors = [opp.get("sector", "other") for opp in opportunities]
        
        # Create scatter plot
        fig = go.Figure()
        
        # Color map for sectors
        sector_colors = {
            "biotech": "#FF6B6B",
            "ai_ml": "#4ECDC4", 
            "clean_energy": "#45B7D1",
            "space_tech": "#96CEB4",
            "quantum": "#FFEAA7",
            "cybersecurity": "#DDA0DD",
            "fintech": "#98D8C8",
            "other": "#F7DC6F"
        }
        
        for sector in set(sectors):
            sector_mask = [s == sector for s in sectors]
            sector_symbols = [symbols[i] for i, mask in enumerate(sector_mask) if mask]
            sector_growth = [growth_scores[i] for i, mask in enumerate(sector_mask) if mask]
            sector_risk = [risk_scores[i] for i, mask in enumerate(sector_mask) if mask]
            sector_caps = [market_caps[i] for i, mask in enumerate(sector_mask) if mask]
            
            fig.add_trace(go.Scatter(
                x=sector_risk,
                y=sector_growth,
                mode='markers',
                name=sector.replace('_', ' ').title(),
                text=sector_symbols,
                hovertemplate='<b>%{text}</b><br>' +
                             'Growth Score: %{y:.1f}<br>' +
                             'Risk Score: %{x:.1f}<br>' +
                             'Market Cap: $%{customdata:.0f}M<extra></extra>',
                customdata=sector_caps,
                marker=dict(
                    size=[max(8, min(20, cap/10)) for cap in sector_caps],  # Size by market cap
                    color=sector_colors.get(sector, "#F7DC6F"),
                    opacity=0.7,
                    line=dict(width=1, color='white')
                )
            ))
        
        fig.update_layout(
            title="Growth vs Risk Analysis",
            xaxis_title="Risk Score",
            yaxis_title="Growth Potential Score",
            hovermode='closest',
            template='plotly_white',
            height=500,
            showlegend=True
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def create_sector_performance_chart(self, sector_data: Dict[str, Dict[str, Any]]) -> str:
        """Create sector performance bar chart."""
        if not sector_data:
            return self._empty_chart()
        
        sectors = list(sector_data.keys())
        avg_scores = [data.get("avg_score", 0) for data in sector_data.values()]
        company_counts = [data.get("total_companies", 0) for data in sector_data.values()]
        
        fig = go.Figure()
        
        fig.add_trace(go.Bar(
            x=sectors,
            y=avg_scores,
            name='Average Growth Score',
            text=[f'{score:.1f}' for score in avg_scores],
            textposition='auto',
            marker_color='#4ECDC4',
            hovertemplate='<b>%{x}</b><br>' +
                         'Avg Score: %{y:.1f}<br>' +
                         'Companies: %{customdata}<extra></extra>',
            customdata=company_counts
        ))
        
        fig.update_layout(
            title="Sector Performance Analysis",
            xaxis_title="Sector",
            yaxis_title="Average Growth Score",
            template='plotly_white',
            height=400
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def create_risk_distribution_chart(self, risk_data: Dict[str, Any]) -> str:
        """Create risk distribution histogram."""
        # Mock risk distribution data
        risk_scores = [45, 52, 38, 67, 71, 59, 43, 78, 65, 49, 56, 62, 74, 41, 68]
        
        fig = go.Figure()
        
        fig.add_trace(go.Histogram(
            x=risk_scores,
            nbinsx=10,
            name='Risk Distribution',
            marker_color='#FF6B6B',
            opacity=0.7
        ))
        
        fig.update_layout(
            title="Portfolio Risk Distribution",
            xaxis_title="Risk Score",
            yaxis_title="Number of Positions",
            template='plotly_white',
            height=400
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def create_backtest_equity_curve(self, backtest_data: Dict[str, Any]) -> str:
        """Create backtest equity curve chart."""
        # Generate mock equity curve data
        dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='D')
        
        # Simulate equity curve with some volatility
        returns = []
        for i in range(len(dates)):
            if i == 0:
                returns.append(0.001)  # Small initial return
            else:
                # Add some randomness but with positive drift
                daily_return = 0.002 + (hash(str(dates[i])) % 100 - 50) / 10000
                returns.append(daily_return)
        
        equity_values = [100000]  # Starting value
        for ret in returns:
            equity_values.append(equity_values[-1] * (1 + ret))
        
        # Also create benchmark (SPY-like)
        benchmark_returns = [r * 0.3 for r in returns]  # Lower returns
        benchmark_values = [100000]
        for ret in benchmark_returns:
            benchmark_values.append(benchmark_values[-1] * (1 + ret))
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=dates,
            y=equity_values[1:],  # Skip initial value
            mode='lines',
            name='QuantumEdge Strategy',
            line=dict(color='#4ECDC4', width=2),
            hovertemplate='<b>%{fullData.name}</b><br>' +
                         'Date: %{x}<br>' +
                         'Value: $%{y:,.0f}<extra></extra>'
        ))
        
        fig.add_trace(go.Scatter(
            x=dates,
            y=benchmark_values[1:],
            mode='lines',
            name='Benchmark (SPY)',
            line=dict(color='#FF6B6B', width=2, dash='dash'),
            hovertemplate='<b>%{fullData.name}</b><br>' +
                         'Date: %{x}<br>' +
                         'Value: $%{y:,.0f}<extra></extra>'
        ))
        
        fig.update_layout(
            title="Backtest Performance vs Benchmark",
            xaxis_title="Date",
            yaxis_title="Portfolio Value ($)",
            template='plotly_white',
            height=500,
            hovermode='x unified'
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def create_monthly_returns_heatmap(self, monthly_data: Dict[str, float]) -> str:
        """Create monthly returns heatmap."""
        # Convert monthly data to matrix format
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        
        # Mock data for 2023
        returns_2023 = [0.12, 0.08, -0.05, 0.15, 0.22, 0.09, 
                       0.18, -0.03, 0.11, 0.07, 0.14, 0.06]
        
        # Create heatmap data
        z_data = [returns_2023]
        y_labels = ['2023']
        
        fig = go.Figure(data=go.Heatmap(
            z=z_data,
            x=months,
            y=y_labels,
            colorscale='RdYlGn',
            zmid=0,
            text=[[f'{val:.1%}' for val in returns_2023]],
            texttemplate='%{text}',
            textfont={"size": 12},
            hovertemplate='<b>%{y} %{x}</b><br>' +
                         'Return: %{z:.1%}<extra></extra>'
        ))
        
        fig.update_layout(
            title="Monthly Returns Heatmap",
            template='plotly_white',
            height=200
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def create_catalyst_timeline(self, catalysts: List[Dict[str, Any]]) -> str:
        """Create catalyst events timeline."""
        if not catalysts:
            return self._empty_chart()
        
        # Sort catalysts by date
        sorted_catalysts = sorted(catalysts, key=lambda x: x.get('event_date', ''))
        
        dates = [datetime.fromisoformat(c.get('event_date', datetime.now().isoformat())) for c in sorted_catalysts]
        symbols = [c.get('symbol', 'Unknown') for c in sorted_catalysts]
        event_types = [c.get('event_type', 'unknown') for c in sorted_catalysts]
        impact_scores = [c.get('impact_score', 50) for c in sorted_catalysts]
        titles = [c.get('title', 'Unknown Event') for c in sorted_catalysts]
        
        # Color map for event types
        event_colors = {
            'fda_approval': '#FF6B6B',
            'clinical_trial': '#4ECDC4',
            'partnership': '#45B7D1',
            'technology_breakthrough': '#96CEB4',
            'earnings_surprise': '#FFEAA7',
            'patent_filing': '#DDA0DD',
            'unknown': '#F7DC6F'
        }
        
        fig = go.Figure()
        
        for event_type in set(event_types):
            type_mask = [et == event_type for et in event_types]
            type_dates = [dates[i] for i, mask in enumerate(type_mask) if mask]
            type_symbols = [symbols[i] for i, mask in enumerate(type_mask) if mask]
            type_scores = [impact_scores[i] for i, mask in enumerate(type_mask) if mask]
            type_titles = [titles[i] for i, mask in enumerate(type_mask) if mask]
            
            fig.add_trace(go.Scatter(
                x=type_dates,
                y=type_scores,
                mode='markers',
                name=event_type.replace('_', ' ').title(),
                text=type_symbols,
                hovertemplate='<b>%{text}</b><br>' +
                             '%{customdata}<br>' +
                             'Impact: %{y:.1f}<br>' +
                             'Date: %{x}<extra></extra>',
                customdata=type_titles,
                marker=dict(
                    size=10,
                    color=event_colors.get(event_type, '#F7DC6F'),
                    opacity=0.8,
                    line=dict(width=1, color='white')
                )
            ))
        
        fig.update_layout(
            title="Catalyst Events Timeline",
            xaxis_title="Date",
            yaxis_title="Impact Score",
            template='plotly_white',
            height=400,
            hovermode='closest'
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def create_portfolio_allocation_pie(self, allocations: Dict[str, Dict[str, Any]]) -> str:
        """Create portfolio allocation pie chart."""
        if not allocations:
            return self._empty_chart()
        
        symbols = list(allocations.keys())
        percentages = [alloc.get('recommended_size_pct', 0) * 100 for alloc in allocations.values()]
        
        fig = go.Figure(data=[go.Pie(
            labels=symbols,
            values=percentages,
            hole=0.3,
            hovertemplate='<b>%{label}</b><br>' +
                         'Allocation: %{value:.1f}%<br>' +
                         'Value: $%{customdata:,.0f}<extra></extra>',
            customdata=[alloc.get('recommended_size_dollars', 0) for alloc in allocations.values()]
        )])
        
        fig.update_layout(
            title="Recommended Portfolio Allocation",
            template='plotly_white',
            height=400
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def create_performance_metrics_gauge(self, metrics: Dict[str, float]) -> str:
        """Create performance metrics gauge chart."""
        sharpe_ratio = metrics.get('sharpe_ratio', 1.5)
        
        fig = go.Figure(go.Indicator(
            mode="gauge+number+delta",
            value=sharpe_ratio,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "Sharpe Ratio"},
            delta={'reference': 1.0},
            gauge={
                'axis': {'range': [None, 3]},
                'bar': {'color': "#4ECDC4"},
                'steps': [
                    {'range': [0, 1], 'color': "#FFE5E5"},
                    {'range': [1, 2], 'color': "#E5F7F5"},
                    {'range': [2, 3], 'color': "#B8F2E6"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 2.5
                }
            }
        ))
        
        fig.update_layout(
            template='plotly_white',
            height=300
        )
        
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def _empty_chart(self) -> str:
        """Return empty chart JSON."""
        fig = go.Figure()
        fig.update_layout(
            title="No Data Available",
            template='plotly_white',
            height=400
        )
        return json.dumps(fig, cls=PlotlyJSONEncoder)
    
    def generate_dashboard_data(self, discovery_results: Dict[str, Any]) -> Dict[str, str]:
        """Generate all dashboard charts data."""
        opportunities = discovery_results.get('top_opportunities', [])
        sector_data = discovery_results.get('sector_analysis', {})
        
        return {
            'discovery_chart': self.create_discovery_chart(opportunities),
            'sector_chart': self.create_sector_performance_chart(sector_data),
            'risk_chart': self.create_risk_distribution_chart({}),
            'equity_curve': self.create_backtest_equity_curve({}),
            'monthly_heatmap': self.create_monthly_returns_heatmap({}),
            'catalyst_timeline': self.create_catalyst_timeline([]),
            'allocation_pie': self.create_portfolio_allocation_pie({}),
            'performance_gauge': self.create_performance_metrics_gauge({'sharpe_ratio': 2.1})
        }
