"""
Flask Web Application for QuantumEdge Microcap Discovery System.
Provides web interface for discovery results, risk analysis, and portfolio management.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_cors import CORS

from ..config.settings import QuantumEdgeConfig
from ..config.logging_config import setup_logging, LoggerMixin
from ..discovery.discovery_engine import DiscoveryEngine
from ..risk.risk_manager import RiskManager
from ..risk.position_sizer import PositionSizer
from ..backtesting.backtest_engine import BacktestEngine
from .dashboard import DashboardManager


class QuantumEdgeApp(LoggerMixin):
    """Main Flask application for QuantumEdge system."""
    
    def __init__(self):
        """Initialize the Flask application."""
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'quantum-edge-secret-key-2024'
        
        # Enable CORS for API endpoints
        CORS(self.app)
        
        # Initialize components
        self.config = QuantumEdgeConfig()
        self.dashboard_manager = DashboardManager()
        
        # Initialize discovery engine (mock for web demo)
        self.discovery_engine = None
        self.risk_manager = None
        self.position_sizer = None
        self.backtest_engine = None
        
        # Setup routes
        self._setup_routes()
        
        self.logger.info("QuantumEdge web application initialized")
    
    def _setup_routes(self):
        """Setup Flask routes."""
        
        @self.app.route('/')
        def index():
            """Main dashboard page."""
            return render_template('dashboard.html')
        
        @self.app.route('/discovery')
        def discovery():
            """Discovery results page."""
            return render_template('discovery.html')
        
        @self.app.route('/risk')
        def risk():
            """Risk analysis page."""
            return render_template('risk.html')
        
        @self.app.route('/backtesting')
        def backtesting():
            """Backtesting results page."""
            return render_template('backtesting.html')
        
        @self.app.route('/portfolio')
        def portfolio():
            """Portfolio management page."""
            return render_template('portfolio.html')
        
        # API Routes
        @self.app.route('/api/discovery/run', methods=['POST'])
        def api_run_discovery():
            """Run discovery process."""
            try:
                data = request.get_json() or {}
                target_sectors = data.get('target_sectors', [])
                max_results = data.get('max_results', 50)
                
                # Mock discovery results for demo
                results = self._get_mock_discovery_results(target_sectors, max_results)
                
                return jsonify({
                    'success': True,
                    'data': results
                })
                
            except Exception as e:
                self.logger.error(f"Error in discovery API: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/discovery/results')
        def api_discovery_results():
            """Get latest discovery results."""
            try:
                # Mock results for demo
                results = self._get_mock_discovery_results()
                return jsonify({
                    'success': True,
                    'data': results
                })
                
            except Exception as e:
                self.logger.error(f"Error getting discovery results: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/company/<symbol>')
        def api_company_details(symbol):
            """Get detailed company analysis."""
            try:
                # Mock company details for demo
                details = self._get_mock_company_details(symbol)
                return jsonify({
                    'success': True,
                    'data': details
                })
                
            except Exception as e:
                self.logger.error(f"Error getting company details for {symbol}: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/risk/assess', methods=['POST'])
        def api_risk_assessment():
            """Assess risk for given symbols."""
            try:
                data = request.get_json() or {}
                symbols = data.get('symbols', [])
                
                # Mock risk assessment for demo
                risk_data = self._get_mock_risk_assessment(symbols)
                
                return jsonify({
                    'success': True,
                    'data': risk_data
                })
                
            except Exception as e:
                self.logger.error(f"Error in risk assessment API: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/portfolio/optimize', methods=['POST'])
        def api_portfolio_optimization():
            """Optimize portfolio allocation."""
            try:
                data = request.get_json() or {}
                capital = data.get('capital', 100000)
                symbols = data.get('symbols', [])
                
                # Mock portfolio optimization for demo
                optimization = self._get_mock_portfolio_optimization(capital, symbols)
                
                return jsonify({
                    'success': True,
                    'data': optimization
                })
                
            except Exception as e:
                self.logger.error(f"Error in portfolio optimization API: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/backtest/run', methods=['POST'])
        def api_run_backtest():
            """Run backtest simulation."""
            try:
                data = request.get_json() or {}
                start_date = data.get('start_date', '2023-01-01')
                end_date = data.get('end_date', '2024-01-01')
                
                # Mock backtest results for demo
                results = self._get_mock_backtest_results(start_date, end_date)
                
                return jsonify({
                    'success': True,
                    'data': results
                })
                
            except Exception as e:
                self.logger.error(f"Error in backtest API: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
        
        @self.app.route('/api/dashboard/stats')
        def api_dashboard_stats():
            """Get dashboard statistics."""
            try:
                stats = self._get_mock_dashboard_stats()
                return jsonify({
                    'success': True,
                    'data': stats
                })
                
            except Exception as e:
                self.logger.error(f"Error getting dashboard stats: {e}")
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
    
    def _get_mock_discovery_results(self, target_sectors=None, max_results=50):
        """Generate mock discovery results for demo."""
        mock_opportunities = [
            {
                "symbol": "ATOS",
                "company_name": "Atossa Therapeutics Inc",
                "total_score": 87.5,
                "catalyst_score": 92.0,
                "technical_score": 78.0,
                "fundamental_score": 85.0,
                "sentiment_score": 88.0,
                "innovation_score": 94.0,
                "risk_score": -15.0,
                "confidence": 0.82,
                "sector": "biotech",
                "market_cap": *********,
                "key_catalysts": [
                    "FDA breakthrough therapy designation",
                    "Phase 3 trial positive results",
                    "Strategic partnership with Big Pharma"
                ],
                "risk_factors": [
                    "High regulatory risk",
                    "Small market cap volatility"
                ],
                "last_updated": datetime.now().isoformat()
            },
            {
                "symbol": "BBAI",
                "company_name": "BigBear.ai Holdings Inc",
                "total_score": 84.2,
                "catalyst_score": 88.0,
                "technical_score": 82.0,
                "fundamental_score": 79.0,
                "sentiment_score": 85.0,
                "innovation_score": 91.0,
                "risk_score": -12.0,
                "confidence": 0.78,
                "sector": "ai_ml",
                "market_cap": *********,
                "key_catalysts": [
                    "AI patent portfolio expansion",
                    "Government contract wins",
                    "Revenue growth acceleration"
                ],
                "risk_factors": [
                    "Competition from tech giants",
                    "Government dependency"
                ],
                "last_updated": datetime.now().isoformat()
            },
            {
                "symbol": "PLUG",
                "company_name": "Plug Power Inc",
                "total_score": 81.7,
                "catalyst_score": 85.0,
                "technical_score": 75.0,
                "fundamental_score": 82.0,
                "sentiment_score": 83.0,
                "innovation_score": 87.0,
                "risk_score": -18.0,
                "confidence": 0.75,
                "sector": "clean_energy",
                "market_cap": *********,
                "key_catalysts": [
                    "Hydrogen infrastructure expansion",
                    "Green energy policy support",
                    "Major customer partnerships"
                ],
                "risk_factors": [
                    "Technology adoption risk",
                    "High capital requirements"
                ],
                "last_updated": datetime.now().isoformat()
            }
        ]
        
        # Filter by sectors if specified
        if target_sectors:
            mock_opportunities = [
                opp for opp in mock_opportunities 
                if opp["sector"] in target_sectors
            ]
        
        return {
            "discovery_timestamp": datetime.now().isoformat(),
            "total_opportunities": len(mock_opportunities),
            "top_opportunities": mock_opportunities[:max_results],
            "sector_analysis": {
                "biotech": {"total_companies": 1, "avg_score": 87.5},
                "ai_ml": {"total_companies": 1, "avg_score": 84.2},
                "clean_energy": {"total_companies": 1, "avg_score": 81.7}
            },
            "processing_stats": {
                "companies_analyzed": 150,
                "catalysts_detected": 45,
                "processing_time_seconds": 12.3
            }
        }
    
    def _get_mock_company_details(self, symbol):
        """Generate mock company details for demo."""
        return {
            "symbol": symbol,
            "company_name": f"{symbol} Corporation",
            "growth_score": {
                "total_score": 85.0,
                "catalyst_score": 88.0,
                "technical_score": 82.0,
                "fundamental_score": 84.0,
                "sentiment_score": 86.0,
                "innovation_score": 89.0,
                "risk_score": -15.0,
                "confidence": 0.80
            },
            "catalysts": [
                {
                    "event_type": "fda_approval",
                    "title": "FDA Breakthrough Therapy Designation",
                    "description": "Company received breakthrough therapy designation for lead drug candidate",
                    "impact_score": 92.0,
                    "confidence": 0.85,
                    "event_date": (datetime.now() - timedelta(days=5)).isoformat(),
                    "source": "SEC Filing"
                }
            ],
            "risk_assessment": {
                "overall_risk_level": "moderate",
                "risk_score": 65.0,
                "liquidity_risk": 70.0,
                "volatility_risk": 75.0,
                "market_cap_risk": 80.0,
                "sector_risk": 60.0,
                "key_risk_factors": [
                    "High volatility",
                    "Small market cap",
                    "Regulatory risk"
                ],
                "max_position_size_pct": 0.03,
                "stop_loss_level": 0.15
            },
            "market_data": {
                "current_price": 12.45,
                "price_change_1d": 2.3,
                "price_change_5d": 8.7,
                "volume": 1250000,
                "market_cap": *********
            }
        }
    
    def _get_mock_risk_assessment(self, symbols):
        """Generate mock risk assessment for demo."""
        assessments = {}
        
        for symbol in symbols:
            assessments[symbol] = {
                "overall_risk_level": "moderate",
                "risk_score": 65.0 + hash(symbol) % 20,  # Vary by symbol
                "liquidity_risk": 70.0,
                "volatility_risk": 75.0,
                "market_cap_risk": 80.0,
                "sector_risk": 60.0,
                "key_risk_factors": [
                    "High volatility",
                    "Small market cap"
                ],
                "max_position_size_pct": 0.03,
                "stop_loss_level": 0.15
            }
        
        return {
            "assessments": assessments,
            "portfolio_risk": {
                "total_risk_score": 68.5,
                "concentration_risk": 45.0,
                "sector_concentration": {
                    "biotech": 0.4,
                    "ai_ml": 0.3,
                    "clean_energy": 0.3
                }
            }
        }
    
    def _get_mock_portfolio_optimization(self, capital, symbols):
        """Generate mock portfolio optimization for demo."""
        allocations = {}
        
        for i, symbol in enumerate(symbols):
            allocations[symbol] = {
                "recommended_size_pct": 0.05 - i * 0.01,  # Decreasing allocation
                "recommended_size_dollars": capital * (0.05 - i * 0.01),
                "max_shares": int((capital * (0.05 - i * 0.01)) / (10 + i)),
                "entry_strategy": "Scale in over 3-5 days",
                "risk_per_share": 1.5 + i * 0.2,
                "expected_return": 0.75 - i * 0.1,
                "confidence_level": 0.8 - i * 0.05
            }
        
        return {
            "total_capital": capital,
            "allocations": allocations,
            "portfolio_stats": {
                "total_allocated_pct": sum(alloc["recommended_size_pct"] for alloc in allocations.values()),
                "expected_portfolio_return": 0.65,
                "portfolio_risk": 0.45,
                "sharpe_ratio": 1.44
            }
        }
    
    def _get_mock_backtest_results(self, start_date, end_date):
        """Generate mock backtest results for demo."""
        return {
            "performance_summary": {
                "total_return": "156.7%",
                "annualized_return": "78.3%",
                "volatility": "34.2%",
                "sharpe_ratio": "2.29",
                "max_drawdown": "-18.5%",
                "win_rate": "67.3%"
            },
            "trade_statistics": {
                "total_trades": 127,
                "profitable_trades": 85,
                "losing_trades": 42,
                "average_win": "23.4%",
                "average_loss": "-8.7%",
                "profit_factor": 2.69
            },
            "sector_performance": {
                "biotech": "89.2%",
                "ai_ml": "67.8%",
                "clean_energy": "45.3%"
            },
            "monthly_returns": {
                "2023-01": 0.12,
                "2023-02": 0.08,
                "2023-03": -0.05,
                "2023-04": 0.15,
                "2023-05": 0.22,
                "2023-06": 0.09
            }
        }
    
    def _get_mock_dashboard_stats(self):
        """Generate mock dashboard statistics for demo."""
        return {
            "system_status": {
                "discovery_engine": "operational",
                "risk_manager": "operational",
                "data_sources": "5/5 online",
                "last_update": datetime.now().isoformat()
            },
            "performance_metrics": {
                "ytd_return": "78.3%",
                "total_opportunities": 1247,
                "high_potential_count": 89,
                "active_positions": 15,
                "portfolio_value": 156780
            },
            "recent_discoveries": [
                {
                    "symbol": "ATOS",
                    "score": 87.5,
                    "catalyst": "FDA breakthrough therapy",
                    "discovered": "2 hours ago"
                },
                {
                    "symbol": "BBAI", 
                    "score": 84.2,
                    "catalyst": "AI patent expansion",
                    "discovered": "4 hours ago"
                }
            ],
            "risk_alerts": [
                {
                    "type": "concentration",
                    "message": "High biotech sector concentration (45%)",
                    "severity": "medium"
                }
            ]
        }


def create_app():
    """Create and configure the Flask application."""
    quantum_app = QuantumEdgeApp()
    return quantum_app.app


if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
