"""
API-specific configuration and rate limiting for QuantumEdge system.
Manages API endpoints, rate limits, and request configurations.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from enum import Enum


class APIProvider(Enum):
    """Enumeration of supported API providers."""
    POLYGON = "polygon"
    ALPACA = "alpaca"
    ALPHA_VANTAGE = "alpha_vantage"
    BENZINGA = "benzinga"
    FINNHUB = "finnhub"
    TIINGO = "tiingo"
    SEC_API = "sec_api"
    NASDAQ = "nasdaq"
    GOOGLE_PATENTS = "google_patents"
    USPTO = "uspto"
    PLANET_LABS = "planet_labs"
    STOCKTWITS = "stocktwits"
    REDDIT = "reddit"
    OPENAI = "openai"
    HUGGINGFACE = "huggingface"


@dataclass
class RateLimitConfig:
    """Rate limiting configuration for API calls."""
    requests_per_minute: int
    requests_per_hour: Optional[int] = None
    requests_per_day: Optional[int] = None
    burst_limit: Optional[int] = None
    
    def __post_init__(self):
        """Set default burst limit if not specified."""
        if self.burst_limit is None:
            self.burst_limit = min(self.requests_per_minute // 2, 10)


@dataclass
class APIEndpointConfig:
    """Configuration for a specific API endpoint."""
    base_url: str
    endpoints: Dict[str, str]
    headers: Dict[str, str] = field(default_factory=dict)
    timeout_seconds: int = 30
    retry_attempts: int = 3
    retry_delay_seconds: float = 1.0
    rate_limit: Optional[RateLimitConfig] = None


class APIConfig:
    """Centralized API configuration management."""
    
    def __init__(self):
        """Initialize API configurations."""
        self._configs = self._setup_api_configs()
    
    def _setup_api_configs(self) -> Dict[APIProvider, APIEndpointConfig]:
        """Set up configuration for all API providers."""
        return {
            APIProvider.POLYGON: APIEndpointConfig(
                base_url="https://api.polygon.io",
                endpoints={
                    "gainers": "/v2/snapshot/locale/us/markets/stocks/gainers",
                    "losers": "/v2/snapshot/locale/us/markets/stocks/losers",
                    "ticker_details": "/v3/reference/tickers/{ticker}",
                    "aggregates": "/v2/aggs/ticker/{ticker}/range/{multiplier}/{timespan}/{from}/{to}",
                    "quotes": "/v3/quotes/{ticker}",
                    "trades": "/v3/trades/{ticker}",
                    "news": "/v2/reference/news",
                },
                headers={"Authorization": f"Bearer {os.getenv('POLYGON_API_KEY', '')}"},
                rate_limit=RateLimitConfig(
                    requests_per_minute=int(os.getenv("POLYGON_RATE_LIMIT_PER_MINUTE", "5")),
                    requests_per_day=100000
                )
            ),
            
            APIProvider.ALPACA: APIEndpointConfig(
                base_url="https://data.alpaca.markets",
                endpoints={
                    "bars": "/v2/stocks/bars",
                    "quotes": "/v2/stocks/quotes/latest",
                    "trades": "/v2/stocks/trades/latest",
                    "snapshots": "/v2/stocks/snapshots",
                    "assets": "/v2/assets",
                },
                headers={
                    "APCA-API-KEY-ID": os.getenv("ALPACA_API_KEY", ""),
                    "APCA-API-SECRET-KEY": os.getenv("ALPACA_SECRET_KEY", ""),
                },
                rate_limit=RateLimitConfig(
                    requests_per_minute=int(os.getenv("ALPACA_RATE_LIMIT_PER_MINUTE", "200"))
                )
            ),
            
            APIProvider.ALPHA_VANTAGE: APIEndpointConfig(
                base_url="https://www.alphavantage.co",
                endpoints={
                    "quote": "/query?function=GLOBAL_QUOTE",
                    "overview": "/query?function=OVERVIEW",
                    "income_statement": "/query?function=INCOME_STATEMENT",
                    "balance_sheet": "/query?function=BALANCE_SHEET",
                    "cash_flow": "/query?function=CASH_FLOW",
                    "earnings": "/query?function=EARNINGS",
                    "intraday": "/query?function=TIME_SERIES_INTRADAY",
                },
                rate_limit=RateLimitConfig(
                    requests_per_minute=5,
                    requests_per_day=500
                )
            ),
            
            APIProvider.BENZINGA: APIEndpointConfig(
                base_url="https://api.benzinga.com",
                endpoints={
                    "news": "/api/v2/news",
                    "ratings": "/api/v2.1/calendar/ratings",
                    "earnings": "/api/v2.1/calendar/earnings",
                    "dividends": "/api/v2.1/calendar/dividends",
                    "splits": "/api/v2.1/calendar/splits",
                    "ipos": "/api/v2.1/calendar/ipos",
                },
                rate_limit=RateLimitConfig(
                    requests_per_minute=int(os.getenv("BENZINGA_RATE_LIMIT_PER_MINUTE", "100"))
                )
            ),
            
            APIProvider.STOCKTWITS: APIEndpointConfig(
                base_url="https://api.stocktwits.com",
                endpoints={
                    "streams_symbol": "/api/2/streams/symbol/{symbol}.json",
                    "streams_user": "/api/2/streams/user/{user}.json",
                    "trending": "/api/2/trending/symbols.json",
                    "search": "/api/2/search/symbols.json",
                },
                headers={"Authorization": f"Bearer {os.getenv('STOCKTWITS_ACCESS_TOKEN', '')}"},
                rate_limit=RateLimitConfig(
                    requests_per_minute=200,
                    requests_per_hour=400
                )
            ),
            
            APIProvider.REDDIT: APIEndpointConfig(
                base_url="https://oauth.reddit.com",
                endpoints={
                    "subreddit_hot": "/r/{subreddit}/hot",
                    "subreddit_new": "/r/{subreddit}/new",
                    "search": "/search",
                    "comments": "/r/{subreddit}/comments/{article}",
                },
                rate_limit=RateLimitConfig(
                    requests_per_minute=60,
                    requests_per_hour=600
                )
            ),
            
            APIProvider.OPENAI: APIEndpointConfig(
                base_url="https://api.openai.com",
                endpoints={
                    "chat_completions": "/v1/chat/completions",
                    "embeddings": "/v1/embeddings",
                    "models": "/v1/models",
                },
                headers={
                    "Authorization": f"Bearer {os.getenv('OPENAI_API_KEY', '')}",
                    "Content-Type": "application/json",
                },
                rate_limit=RateLimitConfig(
                    requests_per_minute=3500,
                    requests_per_day=200000
                )
            ),
            
            APIProvider.FINNHUB: APIEndpointConfig(
                base_url="https://finnhub.io/api/v1",
                endpoints={
                    "quote": "/quote",
                    "company_profile": "/stock/profile2",
                    "news": "/news",
                    "earnings": "/calendar/earnings",
                    "insider_trading": "/stock/insider-trading",
                    "recommendation": "/stock/recommendation",
                },
                rate_limit=RateLimitConfig(
                    requests_per_minute=60,
                    requests_per_day=1000
                )
            ),

            APIProvider.TIINGO: APIEndpointConfig(
                base_url="https://api.tiingo.com/tiingo",
                endpoints={
                    "daily": "/daily/{ticker}/prices",
                    "meta": "/daily/{ticker}",
                    "news": "/news",
                    "crypto": "/crypto/prices",
                },
                headers={"Authorization": f"Token {os.getenv('TIINGO_API_KEY', '')}"},
                rate_limit=RateLimitConfig(
                    requests_per_minute=500,
                    requests_per_hour=5000
                )
            ),

            APIProvider.SEC_API: APIEndpointConfig(
                base_url="https://data.sec.gov",
                endpoints={
                    "company_tickers": "/api/xbrl/companyfacts/CIK{cik}.json",
                    "submissions": "/submissions/CIK{cik}.json",
                    "filings": "/Archives/edgar/daily-index/{year}/QTR{quarter}/master.{date}.idx",
                    "company_facts": "/api/xbrl/companyfacts/CIK{cik}.json",
                    "company_concept": "/api/xbrl/companyconcept/CIK{cik}/us-gaap/{tag}.json",
                    "frames": "/api/xbrl/frames/us-gaap/{tag}/USD/{period}.json"
                },
                headers={
                    "User-Agent": "QuantumEdge/1.0 (<EMAIL>)",
                    "Accept": "application/json"
                },
                rate_limit=RateLimitConfig(
                    requests_per_minute=10,  # SEC requires 10 requests per second max
                    requests_per_hour=600
                )
            ),

            APIProvider.NASDAQ: APIEndpointConfig(
                base_url="https://api.nasdaq.com",
                endpoints={
                    "screener": "/api/screener/stocks",
                    "company": "/api/company/{symbol}/company-profile",
                    "dividends": "/api/company/{symbol}/dividends",
                    "insider_trades": "/api/company/{symbol}/insider-trades",
                },
                headers={"X-API-Key": os.getenv('NASDAQ_API_KEY', '')},
                rate_limit=RateLimitConfig(
                    requests_per_minute=100,
                    requests_per_hour=1000
                )
            ),

            APIProvider.GOOGLE_PATENTS: APIEndpointConfig(
                base_url="https://patents.googleapis.com/v1",
                endpoints={
                    "search": "/patents:search",
                    "get": "/patents/{publication_number}",
                },
                headers={"X-API-Key": os.getenv('GOOGLE_PATENTS_API_KEY', '')},
                rate_limit=RateLimitConfig(
                    requests_per_minute=60,  # Reduced for free tier
                    requests_per_day=1000
                )
            ),

            APIProvider.USPTO: APIEndpointConfig(
                base_url="https://developer.uspto.gov/ds-api",
                endpoints={
                    "search": "/search/publications",
                    "details": "/publication/{publication_number}",
                },
                headers={"X-API-Key": os.getenv('USPTO_API_KEY', '')},
                rate_limit=RateLimitConfig(
                    requests_per_minute=120,
                    requests_per_hour=1000
                )
            ),

            APIProvider.PLANET_LABS: APIEndpointConfig(
                base_url="https://api.planet.com",
                endpoints={
                    "search": "/data/v1/quick-search",
                    "orders": "/compute/ops/orders/v2",
                    "assets": "/data/v1/assets",
                },
                headers={"Authorization": f"api-key {os.getenv('PLANET_LABS_API_KEY', '')}"},
                rate_limit=RateLimitConfig(
                    requests_per_minute=60,
                    requests_per_hour=500
                )
            ),

            APIProvider.HUGGINGFACE: APIEndpointConfig(
                base_url="https://api-inference.huggingface.co",
                endpoints={
                    "models": "/models/{model_id}",
                    "pipeline": "/pipeline/{task}",
                },
                headers={"Authorization": f"Bearer {os.getenv('HUGGINGFACE_API_KEY', '')}"},
                rate_limit=RateLimitConfig(
                    requests_per_minute=1000,
                    requests_per_hour=10000
                )
            ),
        }
    
    def get_config(self, provider: APIProvider) -> APIEndpointConfig:
        """Get configuration for a specific API provider."""
        return self._configs[provider]
    
    def get_endpoint_url(self, provider: APIProvider, endpoint: str, **kwargs) -> str:
        """Get full URL for a specific endpoint with parameter substitution."""
        config = self.get_config(provider)
        endpoint_path = config.endpoints.get(endpoint)
        
        if not endpoint_path:
            raise ValueError(f"Endpoint '{endpoint}' not found for provider {provider.value}")
        
        # Format endpoint with provided parameters
        if kwargs:
            endpoint_path = endpoint_path.format(**kwargs)
        
        return f"{config.base_url}{endpoint_path}"
    
    def get_headers(self, provider: APIProvider, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """Get headers for API requests."""
        config = self.get_config(provider)
        headers = config.headers.copy()
        
        if additional_headers:
            headers.update(additional_headers)
        
        return headers
    
    def get_rate_limit(self, provider: APIProvider) -> Optional[RateLimitConfig]:
        """Get rate limit configuration for a provider."""
        config = self.get_config(provider)
        return config.rate_limit
    
    def validate_api_keys(self) -> Dict[APIProvider, bool]:
        """Validate that required API keys are present."""
        validation_results = {}
        
        key_mappings = {
            APIProvider.POLYGON: "POLYGON_API_KEY",
            APIProvider.ALPACA: ["ALPACA_API_KEY", "ALPACA_SECRET_KEY"],
            APIProvider.ALPHA_VANTAGE: "ALPHA_VANTAGE_API_KEY",
            APIProvider.BENZINGA: "BENZINGA_API_KEY",
            APIProvider.FINNHUB: "FINNHUB_API_KEY",
            APIProvider.TIINGO: "TIINGO_API_KEY",
            APIProvider.SEC_API: "SEC_API_KEY",
            APIProvider.NASDAQ: "NASDAQ_API_KEY",
            APIProvider.GOOGLE_PATENTS: "GOOGLE_PATENTS_API_KEY",
            APIProvider.USPTO: "USPTO_API_KEY",
            APIProvider.PLANET_LABS: "PLANET_LABS_API_KEY",
            APIProvider.STOCKTWITS: "STOCKTWITS_ACCESS_TOKEN",
            APIProvider.REDDIT: ["REDDIT_CLIENT_ID", "REDDIT_CLIENT_SECRET"],
            APIProvider.OPENAI: "OPENAI_API_KEY",
            APIProvider.HUGGINGFACE: "HUGGINGFACE_API_KEY",
        }
        
        for provider, keys in key_mappings.items():
            if isinstance(keys, str):
                keys = [keys]
            
            validation_results[provider] = all(
                bool(os.getenv(key)) for key in keys
            )
        
        return validation_results
    
    def get_missing_api_keys(self) -> Dict[APIProvider, list]:
        """Get list of missing API keys for each provider."""
        missing_keys = {}
        
        key_mappings = {
            APIProvider.POLYGON: ["POLYGON_API_KEY"],
            APIProvider.ALPACA: ["ALPACA_API_KEY", "ALPACA_SECRET_KEY"],
            APIProvider.ALPHA_VANTAGE: ["ALPHA_VANTAGE_API_KEY"],
            APIProvider.BENZINGA: ["BENZINGA_API_KEY"],
            APIProvider.FINNHUB: ["FINNHUB_API_KEY"],
            APIProvider.TIINGO: ["TIINGO_API_KEY"],
            APIProvider.SEC_API: ["SEC_API_KEY"],
            APIProvider.NASDAQ: ["NASDAQ_API_KEY"],
            APIProvider.GOOGLE_PATENTS: ["GOOGLE_PATENTS_API_KEY"],
            APIProvider.USPTO: ["USPTO_API_KEY"],
            APIProvider.PLANET_LABS: ["PLANET_LABS_API_KEY"],
            APIProvider.STOCKTWITS: ["STOCKTWITS_ACCESS_TOKEN"],
            APIProvider.REDDIT: ["REDDIT_CLIENT_ID", "REDDIT_CLIENT_SECRET"],
            APIProvider.OPENAI: ["OPENAI_API_KEY"],
            APIProvider.HUGGINGFACE: ["HUGGINGFACE_API_KEY"],
        }
        
        for provider, keys in key_mappings.items():
            missing = [key for key in keys if not os.getenv(key)]
            if missing:
                missing_keys[provider] = missing
        
        return missing_keys
