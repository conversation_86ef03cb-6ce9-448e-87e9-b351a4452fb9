"""
QuantumEdge Configuration Management System
Handles all system configuration with validation and environment management.
"""

import os
import logging
from typing import Optional, List, Dict, Any
from dataclasses import dataclass, field
from pathlib import Path
import datetime as dt
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


@dataclass
class APIConfig:
    """API configuration and credentials."""
    
    # Market Data APIs
    polygon_api_key: str = field(default_factory=lambda: os.getenv("POLYGON_API_KEY", ""))
    alpaca_api_key: str = field(default_factory=lambda: os.getenv("ALPACA_API_KEY", ""))
    alpaca_secret_key: str = field(default_factory=lambda: os.getenv("ALPACA_SECRET_KEY", ""))
    alpha_vantage_api_key: str = field(default_factory=lambda: os.getenv("ALPHA_VANTAGE_API_KEY", ""))
    
    # News & Sentiment APIs
    benzinga_api_key: str = field(default_factory=lambda: os.getenv("BENZINGA_API_KEY", ""))
    stocktwits_access_token: str = field(default_factory=lambda: os.getenv("STOCKTWITS_ACCESS_TOKEN", ""))
    reddit_client_id: str = field(default_factory=lambda: os.getenv("REDDIT_CLIENT_ID", ""))
    reddit_client_secret: str = field(default_factory=lambda: os.getenv("REDDIT_CLIENT_SECRET", ""))
    
    # AI & ML APIs
    openai_api_key: Optional[str] = field(default_factory=lambda: os.getenv("OPENAI_API_KEY"))
    huggingface_api_key: Optional[str] = field(default_factory=lambda: os.getenv("HUGGINGFACE_API_KEY"))
    
    def validate(self) -> List[str]:
        """Validate API configuration and return list of missing keys."""
        missing_keys = []
        
        # Required APIs
        required_apis = {
            "polygon_api_key": "Polygon.io API key",
            "alpaca_api_key": "Alpaca API key", 
            "alpaca_secret_key": "Alpaca secret key",
            "alpha_vantage_api_key": "Alpha Vantage API key",
            "benzinga_api_key": "Benzinga API key",
        }
        
        for key, description in required_apis.items():
            if not getattr(self, key):
                missing_keys.append(f"{description} ({key.upper()})")
        
        return missing_keys


@dataclass
class TradingConfig:
    """Trading and risk management configuration."""
    
    # Market Hours
    pre_market_start_hour: int = field(default_factory=lambda: int(os.getenv("PRE_MARKET_START_HOUR", "4")))
    pre_market_start_minute: int = field(default_factory=lambda: int(os.getenv("PRE_MARKET_START_MINUTE", "0")))
    
    # Screening Parameters
    gap_threshold_percent: float = field(default_factory=lambda: float(os.getenv("GAP_THRESHOLD_PERCENT", "5.0")))
    microcap_ceiling: int = field(default_factory=lambda: int(os.getenv("MICROCAP_CEILING", "300000000")))
    penny_stock_limit: float = field(default_factory=lambda: float(os.getenv("PENNY_STOCK_LIMIT", "5.0")))
    
    # Risk Management
    max_position_size_percent: float = field(default_factory=lambda: float(os.getenv("MAX_POSITION_SIZE_PERCENT", "1.0")))
    vwap_trail_percent: float = field(default_factory=lambda: float(os.getenv("VWAP_TRAIL_PERCENT", "2.0")))
    portfolio_beta_cap: float = field(default_factory=lambda: float(os.getenv("PORTFOLIO_BETA_CAP", "1.5")))
    
    @property
    def pre_market_start_time(self) -> dt.time:
        """Get pre-market start time as datetime.time object."""
        return dt.time(self.pre_market_start_hour, self.pre_market_start_minute)


@dataclass
class DatabaseConfig:
    """Database and caching configuration."""
    
    # Redis Configuration
    redis_host: str = field(default_factory=lambda: os.getenv("REDIS_HOST", "localhost"))
    redis_port: int = field(default_factory=lambda: int(os.getenv("REDIS_PORT", "6379")))
    redis_db: int = field(default_factory=lambda: int(os.getenv("REDIS_DB", "0")))
    redis_password: Optional[str] = field(default_factory=lambda: os.getenv("REDIS_PASSWORD"))
    
    # PostgreSQL Configuration
    database_url: str = field(default_factory=lambda: os.getenv("DATABASE_URL", ""))
    database_host: str = field(default_factory=lambda: os.getenv("DATABASE_HOST", "localhost"))
    database_port: int = field(default_factory=lambda: int(os.getenv("DATABASE_PORT", "5432")))
    database_name: str = field(default_factory=lambda: os.getenv("DATABASE_NAME", "quantum_edge"))
    database_user: str = field(default_factory=lambda: os.getenv("DATABASE_USER", "quantum_user"))
    database_password: str = field(default_factory=lambda: os.getenv("DATABASE_PASSWORD", ""))


@dataclass
class SystemConfig:
    """System-level configuration."""
    
    # Environment
    environment: str = field(default_factory=lambda: os.getenv("ENVIRONMENT", "development"))
    debug: bool = field(default_factory=lambda: os.getenv("DEBUG", "false").lower() == "true")
    log_level: str = field(default_factory=lambda: os.getenv("LOG_LEVEL", "INFO"))
    
    # Performance
    max_concurrent_requests: int = field(default_factory=lambda: int(os.getenv("MAX_CONCURRENT_REQUESTS", "50")))
    request_timeout_seconds: int = field(default_factory=lambda: int(os.getenv("REQUEST_TIMEOUT_SECONDS", "30")))
    retry_attempts: int = field(default_factory=lambda: int(os.getenv("RETRY_ATTEMPTS", "3")))
    retry_delay_seconds: int = field(default_factory=lambda: int(os.getenv("RETRY_DELAY_SECONDS", "1")))
    
    # Caching
    cache_ttl_seconds: int = field(default_factory=lambda: int(os.getenv("CACHE_TTL_SECONDS", "300")))
    max_cache_size: int = field(default_factory=lambda: int(os.getenv("MAX_CACHE_SIZE", "1000")))
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment.lower() == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment.lower() == "development"


@dataclass
class QuantumConfig:
    """Quantum computing configuration."""
    
    quantum_device: str = field(default_factory=lambda: os.getenv("QUANTUM_DEVICE", "lightning.qubit"))
    quantum_wires: int = field(default_factory=lambda: int(os.getenv("QUANTUM_WIRES", "4")))


@dataclass
class ModelConfig:
    """Machine learning model configuration."""
    
    model_path: str = field(default_factory=lambda: os.getenv("MODEL_PATH", "data/models/quantum_edge_v3.pkl"))
    model_retrain_interval_hours: int = field(default_factory=lambda: int(os.getenv("MODEL_RETRAIN_INTERVAL_HOURS", "24")))
    feature_cache_ttl_seconds: int = field(default_factory=lambda: int(os.getenv("FEATURE_CACHE_TTL_SECONDS", "300")))


class QuantumEdgeConfig:
    """Main configuration class that aggregates all configuration sections."""
    
    def __init__(self):
        """Initialize configuration with validation."""
        self.api = APIConfig()
        self.trading = TradingConfig()
        self.database = DatabaseConfig()
        self.system = SystemConfig()
        self.quantum = QuantumConfig()
        self.model = ModelConfig()
        
        # Validate configuration
        self._validate_configuration()
        
        logger.info(f"QuantumEdge configuration loaded for {self.system.environment} environment")
    
    def _validate_configuration(self) -> None:
        """Validate all configuration sections."""
        # Validate API keys
        missing_api_keys = self.api.validate()
        if missing_api_keys and self.system.is_production:
            raise ValueError(f"Missing required API keys in production: {', '.join(missing_api_keys)}")
        elif missing_api_keys:
            logger.warning(f"Missing API keys (development mode): {', '.join(missing_api_keys)}")
        
        # Validate trading parameters
        if self.trading.gap_threshold_percent <= 0:
            raise ValueError("Gap threshold must be positive")
        
        if self.trading.max_position_size_percent <= 0 or self.trading.max_position_size_percent > 100:
            raise ValueError("Position size must be between 0 and 100 percent")
        
        # Validate paths
        model_dir = Path(self.model.model_path).parent
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # Create log directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
    
    def get_sector_keywords(self) -> Dict[str, List[str]]:
        """Get sector-specific keywords for screening."""
        return {
            "ai_ml": os.getenv("AI_KEYWORDS", "artificial intelligence,machine learning,neural network").split(","),
            "quantum": os.getenv("QUANTUM_KEYWORDS", "quantum computing,quantum algorithm,qubit").split(","),
            "biotech": os.getenv("BIOTECH_KEYWORDS", "gene therapy,CRISPR,immunotherapy,clinical trial").split(","),
            "clean_energy": os.getenv("CLEAN_ENERGY_KEYWORDS", "solar,wind,battery,energy storage").split(","),
            "space": os.getenv("SPACE_KEYWORDS", "satellite,space,aerospace,rocket").split(","),
            "cybersecurity": os.getenv("CYBERSECURITY_KEYWORDS", "cybersecurity,zero trust,encryption").split(","),
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary for serialization."""
        return {
            "api": self.api.__dict__,
            "trading": self.trading.__dict__,
            "database": self.database.__dict__,
            "system": self.system.__dict__,
            "quantum": self.quantum.__dict__,
            "model": self.model.__dict__,
        }
