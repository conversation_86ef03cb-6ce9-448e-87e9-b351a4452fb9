"""
Comprehensive logging configuration for QuantumEdge system.
Provides structured logging with multiple handlers and formatters.
"""

import os
import logging
import logging.handlers
from pathlib import Path
from typing import Optional
import structlog


def setup_logging(
    log_level: str = "INFO",
    log_file_path: str = "logs/quantum_edge.log",
    max_size_mb: int = 100,
    backup_count: int = 5,
    enable_structured_logging: bool = True
) -> None:
    """
    Set up comprehensive logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file_path: Path to log file
        max_size_mb: Maximum log file size in MB before rotation
        backup_count: Number of backup files to keep
        enable_structured_logging: Whether to use structured logging
    """
    # Create logs directory
    log_dir = Path(log_file_path).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        fmt='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_file_path,
        maxBytes=max_size_mb * 1024 * 1024,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(file_handler)
    
    # Error file handler
    error_log_path = log_dir / "error.log"
    error_handler = logging.handlers.RotatingFileHandler(
        filename=str(error_log_path),
        maxBytes=max_size_mb * 1024 * 1024,
        backupCount=backup_count,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(detailed_formatter)
    root_logger.addHandler(error_handler)
    
    # Performance log handler
    performance_log_path = log_dir / "performance.log"
    performance_handler = logging.handlers.RotatingFileHandler(
        filename=str(performance_log_path),
        maxBytes=max_size_mb * 1024 * 1024,
        backupCount=backup_count,
        encoding='utf-8'
    )
    performance_handler.setLevel(logging.INFO)
    performance_handler.setFormatter(detailed_formatter)
    
    # Create performance logger
    performance_logger = logging.getLogger("performance")
    performance_logger.addHandler(performance_handler)
    performance_logger.setLevel(logging.INFO)
    performance_logger.propagate = False
    
    # Configure structured logging if enabled
    if enable_structured_logging:
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    
    # Suppress noisy third-party loggers
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("yfinance").setLevel(logging.WARNING)
    logging.getLogger("transformers").setLevel(logging.WARNING)
    
    logging.info(f"Logging configured - Level: {log_level}, File: {log_file_path}")


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the specified name."""
    return logging.getLogger(name)


def get_performance_logger() -> logging.Logger:
    """Get the performance logger instance."""
    return logging.getLogger("performance")


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger instance for this class."""
        return logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
    
    @property
    def performance_logger(self) -> logging.Logger:
        """Get performance logger instance."""
        return get_performance_logger()


def log_execution_time(func_name: str, execution_time: float, **kwargs) -> None:
    """Log function execution time to performance logger."""
    performance_logger = get_performance_logger()
    performance_logger.info(
        f"Function execution time",
        extra={
            "function": func_name,
            "execution_time_seconds": execution_time,
            **kwargs
        }
    )


def log_api_call(api_name: str, endpoint: str, response_time: float, status_code: Optional[int] = None) -> None:
    """Log API call performance metrics."""
    performance_logger = get_performance_logger()
    performance_logger.info(
        f"API call performance",
        extra={
            "api_name": api_name,
            "endpoint": endpoint,
            "response_time_seconds": response_time,
            "status_code": status_code
        }
    )
