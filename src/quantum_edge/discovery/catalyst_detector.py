"""
Real-Time Catalyst Detector for QuantumEdge system.
Monitors multiple sources for breakthrough events that could trigger 100x growth.
"""

import asyncio
from typing import Dict, Any, List, Optional, Set
import pandas as pd
from datetime import datetime, timedelta
import re
from dataclasses import dataclass

from ..config.logging_config import LoggerMixin
from ..data.sources.sec_client import SECClient
from ..data.sources.benzinga_client import BenzingaClient
from ..data.sources.patents_client import PatentsClient


@dataclass
class CatalystEvent:
    """Represents a detected catalyst event."""
    symbol: str
    event_type: str
    event_date: datetime
    title: str
    description: str
    source: str
    impact_score: float  # 0-100
    confidence: float    # 0-1
    url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class CatalystDetector(LoggerMixin):
    """
    Real-time catalyst detection engine.
    Monitors SEC filings, news, patents, and other sources for breakthrough events.
    """
    
    def __init__(
        self,
        sec_client: Optional[SECClient] = None,
        benzinga_client: Optional[BenzingaClient] = None,
        patents_client: Optional[PatentsClient] = None
    ):
        """
        Initialize catalyst detector.
        
        Args:
            sec_client: SEC API client for regulatory filings
            benzinga_client: Benzinga client for news and events
            patents_client: Patents client for innovation tracking
        """
        self.sec_client = sec_client
        self.benzinga_client = benzinga_client
        self.patents_client = patents_client
        
        # Catalyst detection patterns
        self.breakthrough_keywords = {
            "fda_approval": [
                "fda approval", "fda approves", "breakthrough therapy", "fast track",
                "orphan drug", "priority review", "accelerated approval", "biologics license"
            ],
            "clinical_trial": [
                "phase 3", "phase iii", "clinical trial", "positive results", "met primary endpoint",
                "statistically significant", "efficacy", "safety profile"
            ],
            "partnership": [
                "strategic partnership", "collaboration agreement", "licensing deal",
                "joint venture", "acquisition", "merger", "buyout offer"
            ],
            "technology_breakthrough": [
                "breakthrough", "innovation", "patent granted", "intellectual property",
                "proprietary technology", "first-in-class", "novel approach"
            ],
            "financial_milestone": [
                "revenue guidance", "earnings beat", "contract award", "major contract",
                "funding round", "ipo", "uplisting", "nasdaq", "nyse"
            ],
            "regulatory_approval": [
                "regulatory approval", "ce mark", "510k clearance", "fda clearance",
                "european approval", "international approval"
            ]
        }
        
        # High-impact SEC form types
        self.critical_sec_forms = {
            "8-K": "Material events and corporate changes",
            "10-K": "Annual report",
            "10-Q": "Quarterly report", 
            "S-1": "IPO registration",
            "424B4": "Final prospectus",
            "DEF 14A": "Proxy statement"
        }
        
        # Patent classification codes for breakthrough technologies
        self.breakthrough_patent_classes = {
            "A61K": "Pharmaceuticals/Medical preparations",
            "C12N": "Biotechnology/Genetic engineering", 
            "G06N": "Artificial intelligence/Machine learning",
            "H01L": "Semiconductor devices/Quantum computing",
            "H04L": "Digital communication/Cybersecurity",
            "F03D": "Wind energy",
            "H01M": "Battery technology",
            "H02S": "Solar energy"
        }
        
        self.logger.info("Catalyst detector initialized")
    
    async def detect_catalysts(
        self,
        symbols: List[str],
        lookback_days: int = 7,
        min_impact_score: float = 60.0
    ) -> List[CatalystEvent]:
        """
        Detect catalyst events for given symbols.
        
        Args:
            symbols: List of stock symbols to monitor
            lookback_days: Number of days to look back
            min_impact_score: Minimum impact score to include
            
        Returns:
            List of detected catalyst events
        """
        self.logger.info(f"Detecting catalysts for {len(symbols)} symbols")
        
        all_catalysts = []
        
        # 1. SEC Filing Catalysts
        if self.sec_client:
            sec_catalysts = await self._detect_sec_catalysts(symbols, lookback_days)
            all_catalysts.extend(sec_catalysts)
        
        # 2. News & Event Catalysts
        if self.benzinga_client:
            news_catalysts = await self._detect_news_catalysts(symbols, lookback_days)
            all_catalysts.extend(news_catalysts)
        
        # 3. Patent Catalysts
        if self.patents_client:
            patent_catalysts = await self._detect_patent_catalysts(symbols, lookback_days)
            all_catalysts.extend(patent_catalysts)
        
        # Filter by impact score and deduplicate
        high_impact_catalysts = [
            catalyst for catalyst in all_catalysts
            if catalyst.impact_score >= min_impact_score
        ]
        
        # Sort by impact score
        high_impact_catalysts.sort(key=lambda x: x.impact_score, reverse=True)
        
        self.logger.info(f"Detected {len(high_impact_catalysts)} high-impact catalysts")
        return high_impact_catalysts
    
    async def _detect_sec_catalysts(
        self,
        symbols: List[str],
        lookback_days: int
    ) -> List[CatalystEvent]:
        """Detect catalysts from SEC filings."""
        catalysts = []
        
        try:
            # Get recent 8-K filings (most important for catalysts)
            filings_data = await self.sec_client.get_8k_filings(symbols, lookback_days)
            
            for symbol, filings in filings_data.items():
                for filing in filings:
                    try:
                        # Analyze filing content
                        catalyst = await self._analyze_sec_filing(symbol, filing)
                        if catalyst:
                            catalysts.append(catalyst)
                    
                    except Exception as e:
                        self.logger.warning(f"Error analyzing filing for {symbol}: {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"Error detecting SEC catalysts: {e}")
        
        return catalysts
    
    async def _analyze_sec_filing(self, symbol: str, filing: Dict[str, Any]) -> Optional[CatalystEvent]:
        """Analyze individual SEC filing for catalyst potential."""
        try:
            form_type = filing.get("formType", "")
            description = filing.get("description", "").lower()
            items = filing.get("items", [])
            filed_date = filing.get("filedAt", "")
            
            # Calculate impact score based on form type and content
            impact_score = 30.0  # Base score for any 8-K
            
            # Form type scoring
            if form_type == "8-K":
                impact_score += 20
            elif form_type in ["10-K", "10-Q"]:
                impact_score += 10
            elif form_type in ["S-1", "424B4"]:
                impact_score += 30  # IPO-related
            
            # Content analysis
            event_type = "regulatory_filing"
            confidence = 0.5
            
            # Check for breakthrough keywords
            for category, keywords in self.breakthrough_keywords.items():
                if any(keyword in description for keyword in keywords):
                    impact_score += 25
                    event_type = category
                    confidence += 0.2
                    break
            
            # Item analysis for 8-K filings
            if form_type == "8-K" and items:
                high_impact_items = [
                    "1.01",  # Entry into Material Agreement
                    "2.02",  # Results of Operations and Financial Condition
                    "7.01",  # Regulation FD Disclosure
                    "8.01"   # Other Events
                ]
                
                for item in items:
                    item_number = item.get("item", "")
                    if any(hi_item in item_number for hi_item in high_impact_items):
                        impact_score += 15
                        break
            
            # Only create catalyst if impact score is meaningful
            if impact_score >= 40:
                return CatalystEvent(
                    symbol=symbol,
                    event_type=event_type,
                    event_date=datetime.fromisoformat(filed_date.replace('Z', '+00:00')) if filed_date else datetime.now(),
                    title=f"{form_type} Filing - {filing.get('companyName', symbol)}",
                    description=description[:500],  # Truncate long descriptions
                    source="SEC",
                    impact_score=min(100.0, impact_score),
                    confidence=min(1.0, confidence),
                    url=filing.get("linkToHtml"),
                    metadata={
                        "form_type": form_type,
                        "items": items,
                        "accession_no": filing.get("accessionNo")
                    }
                )
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error analyzing SEC filing: {e}")
            return None
    
    async def _detect_news_catalysts(
        self,
        symbols: List[str],
        lookback_days: int
    ) -> List[CatalystEvent]:
        """Detect catalysts from news and events."""
        catalysts = []
        
        try:
            # Get recent news
            date_from = (datetime.now() - timedelta(days=lookback_days)).strftime("%Y-%m-%d")
            date_to = datetime.now().strftime("%Y-%m-%d")
            
            news_data = await self.benzinga_client.get_news(
                symbols=symbols,
                date_from=date_from,
                date_to=date_to,
                limit=200
            )
            
            for article in news_data:
                try:
                    # Extract symbols from article
                    article_symbols = [stock.get("name") for stock in article.get("stocks", [])]
                    
                    for symbol in symbols:
                        if symbol in article_symbols:
                            catalyst = await self._analyze_news_article(symbol, article)
                            if catalyst:
                                catalysts.append(catalyst)
                
                except Exception as e:
                    self.logger.warning(f"Error analyzing news article: {e}")
                    continue
            
            # Get earnings events
            earnings_data = await self.benzinga_client.get_earnings_calendar(
                symbols=symbols,
                date_from=date_from,
                date_to=date_to
            )
            
            for earning in earnings_data:
                try:
                    catalyst = await self._analyze_earnings_event(earning)
                    if catalyst:
                        catalysts.append(catalyst)
                
                except Exception as e:
                    self.logger.warning(f"Error analyzing earnings event: {e}")
                    continue
        
        except Exception as e:
            self.logger.error(f"Error detecting news catalysts: {e}")
        
        return catalysts
    
    async def _analyze_news_article(self, symbol: str, article: Dict[str, Any]) -> Optional[CatalystEvent]:
        """Analyze news article for catalyst potential."""
        try:
            title = article.get("title", "").lower()
            teaser = article.get("teaser", "").lower()
            content = (title + " " + teaser).lower()
            
            # Calculate impact score
            impact_score = 20.0  # Base score for any news
            event_type = "news"
            confidence = 0.3
            
            # Check for breakthrough keywords
            for category, keywords in self.breakthrough_keywords.items():
                keyword_matches = sum(1 for keyword in keywords if keyword in content)
                if keyword_matches > 0:
                    impact_score += keyword_matches * 15
                    event_type = category
                    confidence += keyword_matches * 0.1
                    break
            
            # Check for urgency indicators
            urgency_keywords = ["breaking", "urgent", "alert", "immediate", "just announced"]
            if any(keyword in content for keyword in urgency_keywords):
                impact_score += 20
                confidence += 0.2
            
            # Check for quantitative indicators
            if re.search(r'\d+%|\$\d+|\d+x|fold|times', content):
                impact_score += 10
            
            # Only create catalyst if impact score is meaningful
            if impact_score >= 50:
                return CatalystEvent(
                    symbol=symbol,
                    event_type=event_type,
                    event_date=datetime.fromisoformat(article.get("created", datetime.now().isoformat())),
                    title=article.get("title", ""),
                    description=article.get("teaser", "")[:500],
                    source="Benzinga News",
                    impact_score=min(100.0, impact_score),
                    confidence=min(1.0, confidence),
                    url=article.get("url"),
                    metadata={
                        "channels": article.get("channels", []),
                        "tags": article.get("tags", [])
                    }
                )
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error analyzing news article: {e}")
            return None
    
    async def _analyze_earnings_event(self, earning: Dict[str, Any]) -> Optional[CatalystEvent]:
        """Analyze earnings event for catalyst potential."""
        try:
            symbol = earning.get("ticker", "")
            eps_actual = earning.get("eps")
            eps_estimate = earning.get("eps_est")
            revenue_actual = earning.get("revenue")
            revenue_estimate = earning.get("revenue_est")
            
            impact_score = 30.0  # Base score for earnings
            event_type = "earnings"
            confidence = 0.7
            
            # Calculate earnings surprise
            eps_surprise = 0
            revenue_surprise = 0
            
            if eps_actual and eps_estimate and eps_estimate != 0:
                eps_surprise = (eps_actual - eps_estimate) / abs(eps_estimate) * 100
            
            if revenue_actual and revenue_estimate and revenue_estimate != 0:
                revenue_surprise = (revenue_actual - revenue_estimate) / revenue_estimate * 100
            
            # Score based on surprise magnitude
            if abs(eps_surprise) > 20:  # >20% surprise
                impact_score += 30
                event_type = "earnings_surprise"
            elif abs(eps_surprise) > 10:  # >10% surprise
                impact_score += 15
            
            if abs(revenue_surprise) > 15:  # >15% revenue surprise
                impact_score += 25
            elif abs(revenue_surprise) > 5:  # >5% revenue surprise
                impact_score += 10
            
            # Positive vs negative surprise
            if eps_surprise > 0 or revenue_surprise > 0:
                impact_score += 10  # Positive surprise bonus
            
            description = f"EPS: ${eps_actual} vs ${eps_estimate} est. "
            if revenue_actual and revenue_estimate:
                description += f"Revenue: ${revenue_actual:,.0f} vs ${revenue_estimate:,.0f} est."
            
            if impact_score >= 50:
                return CatalystEvent(
                    symbol=symbol,
                    event_type=event_type,
                    event_date=datetime.fromisoformat(earning.get("date", datetime.now().isoformat())),
                    title=f"Earnings Report - {earning.get('name', symbol)}",
                    description=description,
                    source="Benzinga Earnings",
                    impact_score=min(100.0, impact_score),
                    confidence=confidence,
                    metadata={
                        "eps_surprise": eps_surprise,
                        "revenue_surprise": revenue_surprise,
                        "period": earning.get("period"),
                        "importance": earning.get("importance")
                    }
                )
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error analyzing earnings event: {e}")
            return None
    
    async def _detect_patent_catalysts(
        self,
        symbols: List[str],
        lookback_days: int
    ) -> List[CatalystEvent]:
        """Detect catalysts from patent filings."""
        catalysts = []
        
        try:
            # Get recent patents for companies
            patent_data = await self.patents_client.get_company_patents(
                symbols, days_back=lookback_days
            )
            
            for symbol, patents in patent_data.items():
                for patent in patents:
                    try:
                        catalyst = await self._analyze_patent(symbol, patent)
                        if catalyst:
                            catalysts.append(catalyst)
                    
                    except Exception as e:
                        self.logger.warning(f"Error analyzing patent for {symbol}: {e}")
                        continue
        
        except Exception as e:
            self.logger.error(f"Error detecting patent catalysts: {e}")
        
        return catalysts
    
    async def _analyze_patent(self, symbol: str, patent: Dict[str, Any]) -> Optional[CatalystEvent]:
        """Analyze patent for catalyst potential."""
        try:
            title = patent.get("title", "").lower()
            abstract = patent.get("abstract", "").lower()
            classifications = patent.get("classifications", [])
            
            content = title + " " + abstract
            
            impact_score = 25.0  # Base score for any patent
            event_type = "patent_filing"
            confidence = 0.4
            
            # Check for breakthrough technology classifications
            for classification in classifications:
                class_code = classification.get("symbol", "")[:4]  # First 4 characters
                if class_code in self.breakthrough_patent_classes:
                    impact_score += 20
                    event_type = "technology_breakthrough"
                    confidence += 0.2
                    break
            
            # Check for breakthrough keywords in content
            for category, keywords in self.breakthrough_keywords.items():
                if category == "technology_breakthrough":
                    keyword_matches = sum(1 for keyword in keywords if keyword in content)
                    if keyword_matches > 0:
                        impact_score += keyword_matches * 10
                        confidence += keyword_matches * 0.1
            
            # Check for AI/ML specific terms
            ai_keywords = ["artificial intelligence", "machine learning", "neural network", "deep learning"]
            if any(keyword in content for keyword in ai_keywords):
                impact_score += 15
            
            # Check for biotech specific terms
            biotech_keywords = ["therapeutic", "treatment", "drug", "pharmaceutical", "biomarker"]
            if any(keyword in content for keyword in biotech_keywords):
                impact_score += 15
            
            if impact_score >= 45:
                return CatalystEvent(
                    symbol=symbol,
                    event_type=event_type,
                    event_date=datetime.fromisoformat(patent.get("publication_date", datetime.now().isoformat())),
                    title=f"Patent: {patent.get('title', 'New Patent Filing')}",
                    description=patent.get("abstract", "")[:500],
                    source="Patent Database",
                    impact_score=min(100.0, impact_score),
                    confidence=min(1.0, confidence),
                    url=patent.get("url"),
                    metadata={
                        "publication_number": patent.get("publication_number"),
                        "classifications": classifications,
                        "inventors": patent.get("inventors", []),
                        "assignees": patent.get("assignees", [])
                    }
                )
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error analyzing patent: {e}")
            return None
    
    def get_catalyst_summary(self, catalysts: List[CatalystEvent]) -> Dict[str, Any]:
        """Get summary statistics of detected catalysts."""
        if not catalysts:
            return {}
        
        # Group by event type
        by_type = {}
        for catalyst in catalysts:
            event_type = catalyst.event_type
            if event_type not in by_type:
                by_type[event_type] = []
            by_type[event_type].append(catalyst)
        
        # Group by symbol
        by_symbol = {}
        for catalyst in catalysts:
            symbol = catalyst.symbol
            if symbol not in by_symbol:
                by_symbol[symbol] = []
            by_symbol[symbol].append(catalyst)
        
        return {
            "total_catalysts": len(catalysts),
            "by_event_type": {k: len(v) for k, v in by_type.items()},
            "by_symbol": {k: len(v) for k, v in by_symbol.items()},
            "avg_impact_score": sum(c.impact_score for c in catalysts) / len(catalysts),
            "avg_confidence": sum(c.confidence for c in catalysts) / len(catalysts),
            "top_catalysts": sorted(catalysts, key=lambda x: x.impact_score, reverse=True)[:5]
        }
