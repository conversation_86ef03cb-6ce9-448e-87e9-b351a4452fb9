"""
Main Discovery Engine for QuantumEdge system.
Orchestrates microcap universe building, catalyst detection, and growth scoring.
"""

import asyncio
from typing import Dict, Any, List, Optional
import pandas as pd
from datetime import datetime, timedelta
import json
from pathlib import Path

from ..config.logging_config import LoggerMixin
from ..data.aggregators.market_data_aggregator import MarketDataAggregator
from .microcap_universe import MicrocapUniverse
from .catalyst_detector import CatalystDetector, CatalystEvent
from .growth_scorer import GrowthScorer, GrowthScore


class DiscoveryEngine(LoggerMixin):
    """
    Main discovery engine for identifying 100x growth potential microcap stocks.
    Orchestrates universe building, catalyst detection, and growth scoring.
    """
    
    def __init__(
        self,
        market_data_aggregator: MarketDataAggregator,
        microcap_universe: MicrocapUniverse,
        catalyst_detector: CatalystDetector,
        growth_scorer: GrowthScorer,
        results_cache_file: str = "data/discovery_results.json"
    ):
        """
        Initialize discovery engine.
        
        Args:
            market_data_aggregator: Market data aggregator
            microcap_universe: Microcap universe builder
            catalyst_detector: Catalyst detection engine
            growth_scorer: Growth potential scorer
            results_cache_file: File to cache discovery results
        """
        self.market_data_aggregator = market_data_aggregator
        self.microcap_universe = microcap_universe
        self.catalyst_detector = catalyst_detector
        self.growth_scorer = growth_scorer
        self.results_cache_file = Path(results_cache_file)
        
        # Discovery parameters
        self.max_companies_to_analyze = 200  # Limit for performance
        self.min_growth_score = 70.0  # Minimum score for top opportunities
        self.catalyst_lookback_days = 14  # Look back 2 weeks for catalysts
        
        self.logger.info("Discovery engine initialized")
    
    async def discover_growth_opportunities(
        self,
        force_refresh_universe: bool = False,
        target_sectors: Optional[List[str]] = None,
        max_results: int = 50
    ) -> Dict[str, Any]:
        """
        Main discovery process to find 100x growth opportunities.
        
        Args:
            force_refresh_universe: Force refresh of microcap universe
            target_sectors: Specific sectors to focus on
            max_results: Maximum number of results to return
            
        Returns:
            Dictionary with discovery results
        """
        self.logger.info("🚀 Starting QuantumEdge Growth Discovery Process")
        
        discovery_results = {
            "discovery_timestamp": datetime.now().isoformat(),
            "parameters": {
                "force_refresh_universe": force_refresh_universe,
                "target_sectors": target_sectors,
                "max_results": max_results,
                "catalyst_lookback_days": self.catalyst_lookback_days,
                "min_growth_score": self.min_growth_score
            },
            "universe_stats": {},
            "catalyst_summary": {},
            "top_opportunities": [],
            "sector_analysis": {},
            "processing_stats": {}
        }
        
        start_time = datetime.now()
        
        try:
            # Step 1: Build/Update Microcap Universe
            self.logger.info("📊 Step 1: Building microcap universe...")
            universe = await self.microcap_universe.build_universe(force_refresh_universe)
            
            universe_stats = self.microcap_universe.get_universe_stats()
            discovery_results["universe_stats"] = universe_stats
            
            self.logger.info(f"   ✅ Universe built: {len(universe)} companies")
            
            # Step 2: Filter by target sectors if specified
            if target_sectors:
                filtered_universe = {}
                for symbol, company in universe.items():
                    if company.get("sector_category") in target_sectors:
                        filtered_universe[symbol] = company
                universe = filtered_universe
                self.logger.info(f"   🎯 Filtered to {len(universe)} companies in target sectors")
            
            # Step 3: Limit companies for analysis (performance)
            if len(universe) > self.max_companies_to_analyze:
                # Sort by initial growth potential score and take top N
                sorted_companies = sorted(
                    universe.items(),
                    key=lambda x: x[1].get("growth_potential_score", 0),
                    reverse=True
                )
                universe = dict(sorted_companies[:self.max_companies_to_analyze])
                self.logger.info(f"   📈 Limited to top {len(universe)} companies for analysis")
            
            symbols = list(universe.keys())
            
            # Step 4: Detect Catalysts
            self.logger.info("🔍 Step 2: Detecting catalyst events...")
            all_catalysts = await self.catalyst_detector.detect_catalysts(
                symbols=symbols,
                lookback_days=self.catalyst_lookback_days,
                min_impact_score=50.0
            )
            
            # Group catalysts by symbol
            catalysts_by_symbol = {}
            for catalyst in all_catalysts:
                if catalyst.symbol not in catalysts_by_symbol:
                    catalysts_by_symbol[catalyst.symbol] = []
                catalysts_by_symbol[catalyst.symbol].append(catalyst)
            
            catalyst_summary = self.catalyst_detector.get_catalyst_summary(all_catalysts)
            discovery_results["catalyst_summary"] = catalyst_summary
            
            self.logger.info(f"   ✅ Detected {len(all_catalysts)} catalyst events across {len(catalysts_by_symbol)} companies")
            
            # Step 5: Get Market Data for High-Catalyst Companies
            self.logger.info("📈 Step 3: Gathering market data...")
            
            # Prioritize companies with catalysts
            priority_symbols = list(catalysts_by_symbol.keys())[:50]  # Top 50 with catalysts
            remaining_symbols = [s for s in symbols if s not in priority_symbols][:50]  # Additional 50
            analysis_symbols = priority_symbols + remaining_symbols
            
            # Get comprehensive market data
            comprehensive_data = await self.market_data_aggregator.get_comprehensive_analysis(
                symbols=analysis_symbols,
                include_fundamentals=True,
                include_sentiment=True,
                include_insider_data=False,  # Skip for performance
                include_patents=True
            )
            
            self.logger.info(f"   ✅ Gathered comprehensive data for {len(comprehensive_data)} companies")
            
            # Step 6: Score Growth Potential
            self.logger.info("🎯 Step 4: Scoring growth potential...")
            
            growth_scores = []
            
            for symbol in analysis_symbols:
                try:
                    company_data = universe.get(symbol, {})
                    catalysts = catalysts_by_symbol.get(symbol, [])
                    comp_data = comprehensive_data.get(symbol, {})
                    
                    # Combine company data with comprehensive data
                    combined_data = {**company_data}
                    if comp_data.get("fundamental_data"):
                        combined_data.update(comp_data["fundamental_data"])
                    
                    # Score growth potential
                    growth_score = await self.growth_scorer.score_growth_potential(
                        symbol=symbol,
                        company_data=combined_data,
                        catalysts=catalysts,
                        market_data=None,  # TODO: Add historical market data
                        sentiment_data=comp_data.get("sentiment_data"),
                        patent_data=comp_data.get("patent_data", {}).get("recent_patents", [])
                    )
                    
                    growth_scores.append(growth_score)
                    
                except Exception as e:
                    self.logger.warning(f"Error scoring {symbol}: {e}")
                    continue
            
            # Sort by total score
            growth_scores.sort(key=lambda x: x.total_score, reverse=True)
            
            self.logger.info(f"   ✅ Scored {len(growth_scores)} companies")
            
            # Step 7: Filter Top Opportunities
            top_opportunities = [
                score for score in growth_scores
                if score.total_score >= self.min_growth_score
            ][:max_results]
            
            # Convert to serializable format
            discovery_results["top_opportunities"] = [
                {
                    "symbol": score.symbol,
                    "total_score": round(score.total_score, 1),
                    "catalyst_score": round(score.catalyst_score, 1),
                    "technical_score": round(score.technical_score, 1),
                    "fundamental_score": round(score.fundamental_score, 1),
                    "sentiment_score": round(score.sentiment_score, 1),
                    "innovation_score": round(score.innovation_score, 1),
                    "risk_score": round(score.risk_score, 1),
                    "confidence": round(score.confidence, 2),
                    "key_catalysts": score.key_catalysts,
                    "risk_factors": score.risk_factors,
                    "company_name": universe.get(score.symbol, {}).get("name", ""),
                    "sector": universe.get(score.symbol, {}).get("sector_category", ""),
                    "market_cap": universe.get(score.symbol, {}).get("market_cap", 0),
                    "last_updated": score.last_updated.isoformat()
                }
                for score in top_opportunities
            ]
            
            # Step 8: Sector Analysis
            sector_analysis = self._analyze_by_sectors(growth_scores, universe)
            discovery_results["sector_analysis"] = sector_analysis
            
            # Step 9: Processing Statistics
            processing_time = (datetime.now() - start_time).total_seconds()
            discovery_results["processing_stats"] = {
                "total_processing_time_seconds": round(processing_time, 1),
                "companies_analyzed": len(analysis_symbols),
                "catalysts_detected": len(all_catalysts),
                "top_opportunities_found": len(top_opportunities),
                "avg_growth_score": round(sum(s.total_score for s in growth_scores) / len(growth_scores), 1) if growth_scores else 0
            }
            
            # Save results to cache
            await self._save_results(discovery_results)
            
            self.logger.info(f"🎉 Discovery completed! Found {len(top_opportunities)} high-potential opportunities in {processing_time:.1f}s")
            
            return discovery_results
            
        except Exception as e:
            self.logger.error(f"Error in discovery process: {e}")
            discovery_results["error"] = str(e)
            return discovery_results
    
    def _analyze_by_sectors(
        self,
        growth_scores: List[GrowthScore],
        universe: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze opportunities by sector."""
        sector_analysis = {}
        
        # Group by sector
        by_sector = {}
        for score in growth_scores:
            sector = universe.get(score.symbol, {}).get("sector_category", "other")
            if sector not in by_sector:
                by_sector[sector] = []
            by_sector[sector].append(score)
        
        # Analyze each sector
        for sector, scores in by_sector.items():
            if not scores:
                continue
            
            sector_analysis[sector] = {
                "total_companies": len(scores),
                "avg_growth_score": round(sum(s.total_score for s in scores) / len(scores), 1),
                "top_score": round(max(s.total_score for s in scores), 1),
                "high_potential_count": len([s for s in scores if s.total_score >= self.min_growth_score]),
                "top_companies": [
                    {
                        "symbol": s.symbol,
                        "score": round(s.total_score, 1),
                        "key_catalysts": s.key_catalysts[:2]  # Top 2 catalysts
                    }
                    for s in sorted(scores, key=lambda x: x.total_score, reverse=True)[:5]
                ]
            }
        
        return sector_analysis
    
    async def _save_results(self, results: Dict[str, Any]):
        """Save discovery results to cache file."""
        try:
            # Create directory if it doesn't exist
            self.results_cache_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.results_cache_file, 'w') as f:
                json.dump(results, f, indent=2)
            
            self.logger.info(f"Results saved to {self.results_cache_file}")
            
        except Exception as e:
            self.logger.error(f"Error saving results: {e}")
    
    async def get_latest_results(self) -> Optional[Dict[str, Any]]:
        """Get latest discovery results from cache."""
        try:
            if self.results_cache_file.exists():
                with open(self.results_cache_file, 'r') as f:
                    return json.load(f)
            return None
            
        except Exception as e:
            self.logger.error(f"Error loading results: {e}")
            return None
    
    async def get_company_deep_dive(self, symbol: str) -> Dict[str, Any]:
        """Get detailed analysis for a specific company."""
        try:
            # Get comprehensive data for the symbol
            comprehensive_data = await self.market_data_aggregator.get_comprehensive_analysis(
                symbols=[symbol],
                include_fundamentals=True,
                include_sentiment=True,
                include_insider_data=True,
                include_patents=True
            )
            
            # Get recent catalysts
            catalysts = await self.catalyst_detector.detect_catalysts(
                symbols=[symbol],
                lookback_days=30,  # Look back further for deep dive
                min_impact_score=30.0  # Lower threshold for deep dive
            )
            
            # Get universe data
            universe = await self.microcap_universe.build_universe()
            company_data = universe.get(symbol, {})
            
            # Combine all data
            comp_data = comprehensive_data.get(symbol, {})
            combined_data = {**company_data}
            if comp_data.get("fundamental_data"):
                combined_data.update(comp_data["fundamental_data"])
            
            # Score growth potential
            growth_score = await self.growth_scorer.score_growth_potential(
                symbol=symbol,
                company_data=combined_data,
                catalysts=catalysts,
                market_data=None,
                sentiment_data=comp_data.get("sentiment_data"),
                patent_data=comp_data.get("patent_data", {}).get("recent_patents", [])
            )
            
            return {
                "symbol": symbol,
                "company_data": combined_data,
                "growth_score": {
                    "total_score": round(growth_score.total_score, 1),
                    "catalyst_score": round(growth_score.catalyst_score, 1),
                    "technical_score": round(growth_score.technical_score, 1),
                    "fundamental_score": round(growth_score.fundamental_score, 1),
                    "sentiment_score": round(growth_score.sentiment_score, 1),
                    "innovation_score": round(growth_score.innovation_score, 1),
                    "risk_score": round(growth_score.risk_score, 1),
                    "confidence": round(growth_score.confidence, 2),
                    "key_catalysts": growth_score.key_catalysts,
                    "risk_factors": growth_score.risk_factors
                },
                "catalysts": [
                    {
                        "event_type": c.event_type,
                        "title": c.title,
                        "description": c.description[:200],
                        "impact_score": round(c.impact_score, 1),
                        "confidence": round(c.confidence, 2),
                        "event_date": c.event_date.isoformat(),
                        "source": c.source,
                        "url": c.url
                    }
                    for c in sorted(catalysts, key=lambda x: x.impact_score, reverse=True)
                ],
                "comprehensive_data": comp_data,
                "analysis_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error in deep dive for {symbol}: {e}")
            return {"error": str(e)}
    
    async def monitor_watchlist(self, symbols: List[str]) -> Dict[str, Any]:
        """Monitor a watchlist of symbols for new catalysts and score changes."""
        try:
            self.logger.info(f"Monitoring watchlist: {symbols}")
            
            # Get latest catalysts
            catalysts = await self.catalyst_detector.detect_catalysts(
                symbols=symbols,
                lookback_days=1,  # Only today's catalysts
                min_impact_score=60.0
            )
            
            # Group by symbol
            catalysts_by_symbol = {}
            for catalyst in catalysts:
                if catalyst.symbol not in catalysts_by_symbol:
                    catalysts_by_symbol[catalyst.symbol] = []
                catalysts_by_symbol[catalyst.symbol].append(catalyst)
            
            # Get alerts for symbols with new high-impact catalysts
            alerts = []
            for symbol, symbol_catalysts in catalysts_by_symbol.items():
                for catalyst in symbol_catalysts:
                    if catalyst.impact_score >= 80:  # High-impact threshold
                        alerts.append({
                            "symbol": symbol,
                            "alert_type": "high_impact_catalyst",
                            "event_type": catalyst.event_type,
                            "title": catalyst.title,
                            "impact_score": round(catalyst.impact_score, 1),
                            "timestamp": catalyst.event_date.isoformat(),
                            "source": catalyst.source,
                            "url": catalyst.url
                        })
            
            return {
                "monitoring_timestamp": datetime.now().isoformat(),
                "symbols_monitored": symbols,
                "new_catalysts": len(catalysts),
                "high_impact_alerts": alerts,
                "catalysts_by_symbol": {
                    symbol: len(symbol_catalysts)
                    for symbol, symbol_catalysts in catalysts_by_symbol.items()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error monitoring watchlist: {e}")
            return {"error": str(e)}
