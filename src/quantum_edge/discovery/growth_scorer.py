"""
Growth Potential Scorer for QuantumEdge system.
Combines multiple signals to score 100x growth potential of microcap stocks.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..config.logging_config import LoggerMixin
from .catalyst_detector import CatalystEvent


@dataclass
class GrowthScore:
    """Represents a comprehensive growth potential score."""
    symbol: str
    total_score: float  # 0-100
    catalyst_score: float
    technical_score: float
    fundamental_score: float
    sentiment_score: float
    innovation_score: float
    risk_score: float
    confidence: float
    last_updated: datetime
    key_catalysts: List[str]
    risk_factors: List[str]


class GrowthScorer(LoggerMixin):
    """
    Comprehensive growth potential scoring engine.
    Combines catalysts, technicals, fundamentals, sentiment, and innovation metrics.
    """
    
    def __init__(self):
        """Initialize growth scorer."""
        
        # Scoring weights (must sum to 1.0)
        self.weights = {
            "catalyst": 0.35,      # 35% - Most important for 100x growth
            "technical": 0.20,     # 20% - Price momentum and patterns
            "fundamental": 0.15,   # 15% - Financial health
            "sentiment": 0.15,     # 15% - Market sentiment
            "innovation": 0.15     # 15% - Patent/technology innovation
        }
        
        # Risk factors that reduce scores
        self.risk_factors = {
            "high_debt": {"threshold": 0.6, "penalty": -15},  # Debt/Equity > 60%
            "negative_cash_flow": {"penalty": -10},
            "low_liquidity": {"threshold": 500000, "penalty": -20},  # <$500k daily volume
            "high_volatility": {"threshold": 8, "penalty": -5},  # >8% daily volatility
            "regulatory_risk": {"penalty": -10},  # Biotech/pharma specific
            "dilution_risk": {"penalty": -15}  # Recent equity raises
        }
        
        # Sector-specific multipliers for innovation scoring
        self.innovation_multipliers = {
            "biotech": 1.5,
            "ai_ml": 1.4,
            "clean_energy": 1.3,
            "space_tech": 1.2,
            "quantum": 1.3,
            "cybersecurity": 1.1,
            "fintech": 1.1,
            "other": 1.0
        }
        
        self.logger.info("Growth scorer initialized")
    
    async def score_growth_potential(
        self,
        symbol: str,
        company_data: Dict[str, Any],
        catalysts: List[CatalystEvent],
        market_data: Optional[pd.DataFrame] = None,
        sentiment_data: Optional[Dict[str, Any]] = None,
        patent_data: Optional[List[Dict[str, Any]]] = None
    ) -> GrowthScore:
        """
        Calculate comprehensive growth potential score.
        
        Args:
            symbol: Stock symbol
            company_data: Company fundamental data
            catalysts: List of detected catalyst events
            market_data: Historical price/volume data
            sentiment_data: Sentiment analysis data
            patent_data: Patent filing data
            
        Returns:
            GrowthScore object with detailed scoring
        """
        self.logger.info(f"Scoring growth potential for {symbol}")
        
        # Calculate individual component scores
        catalyst_score = await self._score_catalysts(catalysts)
        technical_score = await self._score_technical(market_data) if market_data is not None else 50.0
        fundamental_score = await self._score_fundamentals(company_data)
        sentiment_score = await self._score_sentiment(sentiment_data) if sentiment_data else 50.0
        innovation_score = await self._score_innovation(patent_data, company_data.get("sector_category", "other"))
        
        # Calculate weighted total score
        total_score = (
            catalyst_score * self.weights["catalyst"] +
            technical_score * self.weights["technical"] +
            fundamental_score * self.weights["fundamental"] +
            sentiment_score * self.weights["sentiment"] +
            innovation_score * self.weights["innovation"]
        )
        
        # Apply risk adjustments
        risk_score, risk_factors = await self._assess_risks(company_data, market_data)
        total_score = max(0, total_score + risk_score)  # Risk score is negative
        
        # Calculate confidence based on data availability
        confidence = self._calculate_confidence(
            market_data, sentiment_data, patent_data, len(catalysts)
        )
        
        # Extract key catalysts
        key_catalysts = [
            f"{c.event_type}: {c.title[:50]}..." 
            for c in sorted(catalysts, key=lambda x: x.impact_score, reverse=True)[:3]
        ]
        
        return GrowthScore(
            symbol=symbol,
            total_score=min(100.0, max(0.0, total_score)),
            catalyst_score=catalyst_score,
            technical_score=technical_score,
            fundamental_score=fundamental_score,
            sentiment_score=sentiment_score,
            innovation_score=innovation_score,
            risk_score=risk_score,
            confidence=confidence,
            last_updated=datetime.now(),
            key_catalysts=key_catalysts,
            risk_factors=risk_factors
        )
    
    async def _score_catalysts(self, catalysts: List[CatalystEvent]) -> float:
        """Score based on catalyst events (0-100)."""
        if not catalysts:
            return 20.0  # Base score for no catalysts
        
        # Weight recent catalysts more heavily
        now = datetime.now()
        weighted_score = 0.0
        total_weight = 0.0
        
        for catalyst in catalysts:
            # Time decay factor (more recent = higher weight)
            days_ago = (now - catalyst.event_date).days
            time_weight = max(0.1, 1.0 - (days_ago / 30))  # Decay over 30 days
            
            # Event type multiplier
            event_multipliers = {
                "fda_approval": 2.0,
                "clinical_trial": 1.8,
                "technology_breakthrough": 1.6,
                "partnership": 1.4,
                "earnings_surprise": 1.2,
                "regulatory_approval": 1.5,
                "patent_filing": 1.1,
                "news": 1.0
            }
            
            event_multiplier = event_multipliers.get(catalyst.event_type, 1.0)
            
            # Calculate weighted impact
            weighted_impact = (
                catalyst.impact_score * 
                catalyst.confidence * 
                time_weight * 
                event_multiplier
            )
            
            weighted_score += weighted_impact
            total_weight += time_weight * event_multiplier
        
        if total_weight > 0:
            avg_score = weighted_score / total_weight
            # Boost for multiple catalysts
            catalyst_bonus = min(20, len(catalysts) * 3)
            return min(100.0, avg_score + catalyst_bonus)
        
        return 20.0
    
    async def _score_technical(self, market_data: pd.DataFrame) -> float:
        """Score based on technical indicators (0-100)."""
        if len(market_data) < 20:
            return 50.0  # Neutral score for insufficient data
        
        score = 50.0  # Base score
        
        try:
            close = market_data["close"]
            volume = market_data.get("volume", pd.Series())
            
            # 1. Price momentum (30 points)
            returns_5d = (close.iloc[-1] / close.iloc[-6] - 1) * 100 if len(close) >= 6 else 0
            returns_20d = (close.iloc[-1] / close.iloc[-21] - 1) * 100 if len(close) >= 21 else 0
            
            # Positive momentum scoring
            if returns_5d > 10:
                score += 15
            elif returns_5d > 5:
                score += 10
            elif returns_5d > 0:
                score += 5
            
            if returns_20d > 20:
                score += 15
            elif returns_20d > 10:
                score += 10
            elif returns_20d > 0:
                score += 5
            
            # 2. Volume analysis (20 points)
            if len(volume) >= 20:
                recent_volume = volume.iloc[-5:].mean()
                avg_volume = volume.iloc[-20:].mean()
                
                if recent_volume > avg_volume * 2:  # 2x volume spike
                    score += 20
                elif recent_volume > avg_volume * 1.5:  # 1.5x volume spike
                    score += 15
                elif recent_volume > avg_volume * 1.2:  # 1.2x volume increase
                    score += 10
            
            # 3. Volatility analysis (10 points)
            returns = close.pct_change().dropna()
            if len(returns) >= 20:
                volatility = returns.std() * np.sqrt(252) * 100  # Annualized volatility
                
                # Moderate volatility is good for growth potential
                if 3 <= volatility <= 6:
                    score += 10
                elif 2 <= volatility <= 8:
                    score += 5
            
            # 4. Support/Resistance breakout (10 points)
            if len(close) >= 50:
                resistance = close.iloc[-50:-1].max()
                current_price = close.iloc[-1]
                
                if current_price > resistance * 1.05:  # 5% breakout
                    score += 10
                elif current_price > resistance * 1.02:  # 2% breakout
                    score += 5
            
        except Exception as e:
            self.logger.warning(f"Error in technical scoring: {e}")
        
        return min(100.0, max(0.0, score))
    
    async def _score_fundamentals(self, company_data: Dict[str, Any]) -> float:
        """Score based on fundamental metrics (0-100)."""
        score = 50.0  # Base score
        
        try:
            # 1. Market cap factor (20 points) - smaller = higher potential
            market_cap = company_data.get("market_cap", 0)
            if market_cap < 100_000_000:  # <$100M
                score += 20
            elif market_cap < 300_000_000:  # <$300M
                score += 15
            elif market_cap < 500_000_000:  # <$500M
                score += 10
            elif market_cap < 1_000_000_000:  # <$1B
                score += 5
            
            # 2. Financial health (30 points)
            pe_ratio = company_data.get("pe_ratio")
            if pe_ratio:
                if 10 <= pe_ratio <= 25:  # Reasonable valuation
                    score += 10
                elif 5 <= pe_ratio <= 40:  # Acceptable range
                    score += 5
            
            # Revenue growth (if available)
            revenue_growth = company_data.get("quarterly_revenue_growth")
            if revenue_growth:
                if revenue_growth > 50:  # >50% growth
                    score += 20
                elif revenue_growth > 25:  # >25% growth
                    score += 15
                elif revenue_growth > 10:  # >10% growth
                    score += 10
                elif revenue_growth > 0:  # Positive growth
                    score += 5
            
            # 3. Sector bonus (20 points)
            sector = company_data.get("sector_category", "other")
            sector_bonuses = {
                "biotech": 20,
                "ai_ml": 18,
                "clean_energy": 15,
                "space_tech": 12,
                "quantum": 15,
                "cybersecurity": 10,
                "fintech": 8,
                "other": 0
            }
            score += sector_bonuses.get(sector, 0)
            
        except Exception as e:
            self.logger.warning(f"Error in fundamental scoring: {e}")
        
        return min(100.0, max(0.0, score))
    
    async def _score_sentiment(self, sentiment_data: Dict[str, Any]) -> float:
        """Score based on sentiment analysis (0-100)."""
        if not sentiment_data:
            return 50.0
        
        score = 50.0  # Base score
        
        try:
            sentiment_score = sentiment_data.get("sentiment_score", 0)
            article_count = sentiment_data.get("article_count", 0)
            positive_ratio = sentiment_data.get("positive_ratio", 0)
            
            # 1. Sentiment direction (40 points)
            if sentiment_score > 0.5:
                score += 20
            elif sentiment_score > 0.2:
                score += 15
            elif sentiment_score > 0:
                score += 10
            elif sentiment_score > -0.2:
                score += 5
            
            # 2. News volume (30 points)
            if article_count > 20:
                score += 15
            elif article_count > 10:
                score += 10
            elif article_count > 5:
                score += 5
            
            # 3. Positive sentiment ratio (30 points)
            if positive_ratio > 0.7:
                score += 15
            elif positive_ratio > 0.6:
                score += 10
            elif positive_ratio > 0.5:
                score += 5
            
        except Exception as e:
            self.logger.warning(f"Error in sentiment scoring: {e}")
        
        return min(100.0, max(0.0, score))
    
    async def _score_innovation(
        self, 
        patent_data: Optional[List[Dict[str, Any]]], 
        sector: str
    ) -> float:
        """Score based on innovation metrics (0-100)."""
        score = 30.0  # Base score
        
        try:
            if patent_data:
                # 1. Patent count (40 points)
                patent_count = len(patent_data)
                if patent_count > 10:
                    score += 20
                elif patent_count > 5:
                    score += 15
                elif patent_count > 2:
                    score += 10
                elif patent_count > 0:
                    score += 5
                
                # 2. Recent patent activity (30 points)
                recent_patents = [
                    p for p in patent_data
                    if (datetime.now() - datetime.fromisoformat(p.get("publication_date", "2020-01-01"))).days <= 365
                ]
                
                if len(recent_patents) > 5:
                    score += 15
                elif len(recent_patents) > 2:
                    score += 10
                elif len(recent_patents) > 0:
                    score += 5
                
                # 3. Patent quality indicators (30 points)
                breakthrough_keywords = [
                    "breakthrough", "novel", "first", "innovative", "revolutionary",
                    "artificial intelligence", "machine learning", "quantum", "biomarker"
                ]
                
                quality_score = 0
                for patent in patent_data:
                    title = patent.get("title", "").lower()
                    abstract = patent.get("abstract", "").lower()
                    content = title + " " + abstract
                    
                    keyword_matches = sum(1 for keyword in breakthrough_keywords if keyword in content)
                    quality_score += min(5, keyword_matches)
                
                score += min(15, quality_score)
            
            # Apply sector multiplier
            multiplier = self.innovation_multipliers.get(sector, 1.0)
            score *= multiplier
            
        except Exception as e:
            self.logger.warning(f"Error in innovation scoring: {e}")
        
        return min(100.0, max(0.0, score))
    
    async def _assess_risks(
        self,
        company_data: Dict[str, Any],
        market_data: Optional[pd.DataFrame]
    ) -> Tuple[float, List[str]]:
        """Assess risk factors and return risk score (negative) and risk list."""
        risk_score = 0.0
        risk_factors = []
        
        try:
            # 1. Liquidity risk
            avg_volume = company_data.get("avg_volume", 0)
            if avg_volume < self.risk_factors["low_liquidity"]["threshold"]:
                risk_score += self.risk_factors["low_liquidity"]["penalty"]
                risk_factors.append("Low liquidity")
            
            # 2. Volatility risk
            if market_data is not None and len(market_data) >= 20:
                returns = market_data["close"].pct_change().dropna()
                volatility = returns.std() * np.sqrt(252) * 100
                
                if volatility > self.risk_factors["high_volatility"]["threshold"]:
                    risk_score += self.risk_factors["high_volatility"]["penalty"]
                    risk_factors.append("High volatility")
            
            # 3. Sector-specific risks
            sector = company_data.get("sector_category", "other")
            if sector in ["biotech", "pharmaceuticals"]:
                risk_score += self.risk_factors["regulatory_risk"]["penalty"]
                risk_factors.append("Regulatory risk (biotech)")
            
            # 4. Market cap risk (very small companies)
            market_cap = company_data.get("market_cap", 0)
            if market_cap < 50_000_000:  # <$50M
                risk_score -= 10
                risk_factors.append("Very small market cap")
            
        except Exception as e:
            self.logger.warning(f"Error in risk assessment: {e}")
        
        return risk_score, risk_factors
    
    def _calculate_confidence(
        self,
        market_data: Optional[pd.DataFrame],
        sentiment_data: Optional[Dict[str, Any]],
        patent_data: Optional[List[Dict[str, Any]]],
        catalyst_count: int
    ) -> float:
        """Calculate confidence score based on data availability (0-1)."""
        confidence = 0.3  # Base confidence
        
        # Market data availability
        if market_data is not None:
            if len(market_data) >= 50:
                confidence += 0.2
            elif len(market_data) >= 20:
                confidence += 0.15
            elif len(market_data) >= 10:
                confidence += 0.1
        
        # Sentiment data availability
        if sentiment_data:
            article_count = sentiment_data.get("article_count", 0)
            if article_count >= 10:
                confidence += 0.15
            elif article_count >= 5:
                confidence += 0.1
            elif article_count >= 1:
                confidence += 0.05
        
        # Patent data availability
        if patent_data:
            if len(patent_data) >= 5:
                confidence += 0.15
            elif len(patent_data) >= 2:
                confidence += 0.1
            elif len(patent_data) >= 1:
                confidence += 0.05
        
        # Catalyst data availability
        if catalyst_count >= 3:
            confidence += 0.15
        elif catalyst_count >= 1:
            confidence += 0.1
        
        return min(1.0, confidence)
    
    async def score_multiple_companies(
        self,
        companies_data: Dict[str, Dict[str, Any]],
        all_catalysts: Dict[str, List[CatalystEvent]],
        market_data: Optional[Dict[str, pd.DataFrame]] = None,
        sentiment_data: Optional[Dict[str, Dict[str, Any]]] = None,
        patent_data: Optional[Dict[str, List[Dict[str, Any]]]] = None
    ) -> List[GrowthScore]:
        """Score multiple companies and return sorted by total score."""
        scores = []
        
        for symbol, company_data in companies_data.items():
            try:
                score = await self.score_growth_potential(
                    symbol=symbol,
                    company_data=company_data,
                    catalysts=all_catalysts.get(symbol, []),
                    market_data=market_data.get(symbol) if market_data else None,
                    sentiment_data=sentiment_data.get(symbol) if sentiment_data else None,
                    patent_data=patent_data.get(symbol) if patent_data else None
                )
                scores.append(score)
                
            except Exception as e:
                self.logger.error(f"Error scoring {symbol}: {e}")
                continue
        
        # Sort by total score
        scores.sort(key=lambda x: x.total_score, reverse=True)
        return scores
