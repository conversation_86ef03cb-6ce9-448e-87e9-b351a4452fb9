"""
Microcap Universe Builder for QuantumEdge system.
Identifies and maintains the target universe of microcap stocks for growth discovery.
"""

import asyncio
from typing import Dict, Any, List, Optional, Set
import pandas as pd
from datetime import datetime, timedelta
import json
from pathlib import Path

from ..config.logging_config import LoggerMixin
from ..data.sources.polygon_client import PolygonClient


class MicrocapUniverse(LoggerMixin):
    """
    Builds and maintains the universe of microcap stocks for growth discovery.
    Focuses on companies with market caps between $50M - $2B.
    """
    
    def __init__(
        self,
        polygon_client: PolygonClient,
        min_market_cap: float = 50_000_000,  # $50M
        max_market_cap: float = 2_000_000_000,  # $2B
        min_daily_volume: float = 1_000_000,  # $1M daily volume
        cache_file: str = "data/microcap_universe.json"
    ):
        """
        Initialize microcap universe builder.
        
        Args:
            polygon_client: Polygon.io client for market data
            min_market_cap: Minimum market cap for inclusion
            max_market_cap: Maximum market cap for inclusion
            min_daily_volume: Minimum daily volume for liquidity
            cache_file: File to cache universe data
        """
        self.polygon_client = polygon_client
        self.min_market_cap = min_market_cap
        self.max_market_cap = max_market_cap
        self.min_daily_volume = min_daily_volume
        self.cache_file = Path(cache_file)
        
        # Universe data
        self.universe = {}
        self.last_updated = None
        self.update_frequency_hours = 24  # Update daily
        
        # Sector focus areas for 100x growth potential
        self.focus_sectors = {
            "biotech": ["biotechnology", "pharmaceuticals", "medical devices"],
            "ai_ml": ["artificial intelligence", "machine learning", "software"],
            "clean_energy": ["renewable energy", "battery", "solar", "wind"],
            "space_tech": ["aerospace", "satellite", "space technology"],
            "quantum": ["quantum computing", "quantum technology"],
            "cybersecurity": ["cybersecurity", "information security"],
            "fintech": ["financial technology", "payments", "blockchain"]
        }
        
        self.logger.info("Microcap universe builder initialized")
    
    async def build_universe(self, force_refresh: bool = False) -> Dict[str, Dict[str, Any]]:
        """
        Build the complete microcap universe.
        
        Args:
            force_refresh: Force refresh even if cache is recent
            
        Returns:
            Dictionary mapping symbols to company data
        """
        # Check if we need to refresh
        if not force_refresh and self._is_cache_valid():
            self.logger.info("Loading universe from cache")
            return self._load_cache()
        
        self.logger.info("Building fresh microcap universe...")
        
        # Step 1: Get all US stocks
        all_tickers = await self._get_all_us_tickers()
        self.logger.info(f"Found {len(all_tickers)} total US tickers")
        
        # Step 2: Filter by market cap and volume
        microcap_candidates = await self._filter_by_market_metrics(all_tickers)
        self.logger.info(f"Filtered to {len(microcap_candidates)} microcap candidates")
        
        # Step 3: Enrich with company details
        enriched_universe = await self._enrich_company_data(microcap_candidates)
        self.logger.info(f"Enriched {len(enriched_universe)} companies with detailed data")
        
        # Step 4: Categorize by sectors
        categorized_universe = await self._categorize_by_sectors(enriched_universe)
        
        # Step 5: Save to cache
        self.universe = categorized_universe
        self.last_updated = datetime.now()
        self._save_cache()
        
        self.logger.info(f"Microcap universe built: {len(self.universe)} companies")
        return self.universe
    
    async def _get_all_us_tickers(self) -> List[str]:
        """Get all US stock tickers from Polygon."""
        try:
            # Get all tickers from Polygon
            tickers_data = await self.polygon_client.get_tickers(
                market="stocks",
                locale="us",
                active=True,
                limit=5000  # Get maximum allowed
            )
            
            # Extract symbols
            symbols = []
            for ticker in tickers_data:
                symbol = ticker.get("ticker", "")
                # Filter out complex instruments
                if (symbol and 
                    len(symbol) <= 5 and  # Reasonable symbol length
                    "." not in symbol and  # No complex instruments
                    "-" not in symbol and  # No warrants/rights
                    symbol.isalpha()):     # Only letters
                    symbols.append(symbol)
            
            return symbols
            
        except Exception as e:
            self.logger.error(f"Error getting US tickers: {e}")
            # Fallback to a curated list of known microcaps
            return self._get_fallback_ticker_list()
    
    async def _filter_by_market_metrics(self, tickers: List[str]) -> List[Dict[str, Any]]:
        """Filter tickers by market cap and volume criteria."""
        candidates = []
        batch_size = 50  # Process in batches to respect rate limits
        
        for i in range(0, len(tickers), batch_size):
            batch = tickers[i:i + batch_size]
            
            try:
                # Get ticker details for the batch
                for symbol in batch:
                    try:
                        # Get basic ticker info
                        ticker_details = await self.polygon_client.get_ticker_details(symbol)
                        
                        if not ticker_details:
                            continue
                        
                        # Extract market cap
                        market_cap = ticker_details.get("market_cap")
                        if not market_cap:
                            continue
                        
                        # Check market cap criteria
                        if self.min_market_cap <= market_cap <= self.max_market_cap:
                            # Get recent volume data
                            recent_volume = await self._get_recent_average_volume(symbol)
                            
                            if recent_volume and recent_volume >= self.min_daily_volume:
                                candidates.append({
                                    "symbol": symbol,
                                    "market_cap": market_cap,
                                    "avg_volume": recent_volume,
                                    "name": ticker_details.get("name", ""),
                                    "description": ticker_details.get("description", ""),
                                    "industry": ticker_details.get("sic_description", ""),
                                    "exchange": ticker_details.get("primary_exchange", "")
                                })
                        
                        # Rate limiting
                        await asyncio.sleep(0.1)  # 10 calls per second
                        
                    except Exception as e:
                        self.logger.warning(f"Error processing {symbol}: {e}")
                        continue
                
                # Batch delay
                await asyncio.sleep(1)
                self.logger.info(f"Processed batch {i//batch_size + 1}/{(len(tickers)-1)//batch_size + 1}")
                
            except Exception as e:
                self.logger.error(f"Error processing batch {i}-{i+batch_size}: {e}")
                continue
        
        return candidates
    
    async def _get_recent_average_volume(self, symbol: str, days: int = 20) -> Optional[float]:
        """Get recent average daily volume for a symbol."""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # Get recent aggregates
            aggregates = await self.polygon_client.get_aggregates(
                symbol=symbol,
                from_date=start_date.strftime("%Y-%m-%d"),
                to_date=end_date.strftime("%Y-%m-%d"),
                timespan="day"
            )

            # Validate aggregates data
            if not aggregates or not isinstance(aggregates, list):
                return None

            if len(aggregates) >= 10:  # Need at least 10 days of data
                volumes = []
                prices = []

                for bar in aggregates:
                    if isinstance(bar, dict):
                        volume = bar.get("volume", 0)
                        price = bar.get("close", 0)

                        # Validate numeric values
                        if isinstance(volume, (int, float)) and isinstance(price, (int, float)):
                            if volume > 0 and price > 0:
                                volumes.append(volume)
                                prices.append(price)

                # Calculate average dollar volume
                if volumes and prices and len(volumes) == len(prices):
                    dollar_volumes = [vol * price for vol, price in zip(volumes, prices)]

                    if dollar_volumes:
                        return sum(dollar_volumes) / len(dollar_volumes)

            return None

        except Exception as e:
            self.logger.warning(f"Error getting volume for {symbol}: {e}")
            return None
    
    async def _enrich_company_data(self, candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enrich candidate companies with additional data."""
        enriched = []
        
        for candidate in candidates:
            try:
                symbol = candidate["symbol"]
                
                # Get recent price data
                recent_data = await self._get_recent_price_data(symbol)
                
                if recent_data:
                    candidate.update({
                        "current_price": recent_data.get("close"),
                        "price_change_1d": recent_data.get("change_1d", 0),
                        "price_change_5d": recent_data.get("change_5d", 0),
                        "price_change_20d": recent_data.get("change_20d", 0),
                        "volatility_20d": recent_data.get("volatility_20d", 0),
                        "last_updated": datetime.now().isoformat()
                    })
                    
                    enriched.append(candidate)
                
                # Rate limiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                self.logger.warning(f"Error enriching {candidate.get('symbol')}: {e}")
                continue
        
        return enriched
    
    async def _get_recent_price_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get recent price data and calculate metrics."""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            aggregates = await self.polygon_client.get_aggregates(
                symbol=symbol,
                from_date=start_date.strftime("%Y-%m-%d"),
                to_date=end_date.strftime("%Y-%m-%d"),
                timespan="day"
            )
            
            if len(aggregates) >= 20:
                closes = [bar.get("close", 0) for bar in aggregates if bar.get("close")]
                
                if len(closes) >= 20:
                    current_price = closes[-1]
                    
                    # Calculate returns
                    change_1d = (closes[-1] / closes[-2] - 1) * 100 if len(closes) >= 2 else 0
                    change_5d = (closes[-1] / closes[-6] - 1) * 100 if len(closes) >= 6 else 0
                    change_20d = (closes[-1] / closes[-21] - 1) * 100 if len(closes) >= 21 else 0
                    
                    # Calculate volatility
                    returns = [(closes[i] / closes[i-1] - 1) for i in range(1, len(closes))]
                    volatility = (sum([(r - sum(returns)/len(returns))**2 for r in returns]) / len(returns))**0.5 * 100
                    
                    return {
                        "close": current_price,
                        "change_1d": change_1d,
                        "change_5d": change_5d,
                        "change_20d": change_20d,
                        "volatility_20d": volatility
                    }
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error getting price data for {symbol}: {e}")
            return None
    
    async def _categorize_by_sectors(self, companies: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Categorize companies by focus sectors."""
        categorized = {}
        
        for company in companies:
            symbol = company["symbol"]
            description = (company.get("description", "") + " " + company.get("industry", "")).lower()
            
            # Determine sector category
            sector_category = "other"
            for sector, keywords in self.focus_sectors.items():
                if any(keyword.lower() in description for keyword in keywords):
                    sector_category = sector
                    break
            
            company["sector_category"] = sector_category
            company["growth_potential_score"] = self._calculate_initial_growth_score(company)
            
            categorized[symbol] = company
        
        return categorized
    
    def _calculate_initial_growth_score(self, company: Dict[str, Any]) -> float:
        """Calculate initial growth potential score (0-100)."""
        score = 50.0  # Base score
        
        # Market cap factor (smaller = higher potential)
        market_cap = company.get("market_cap", 0)
        if market_cap < 100_000_000:  # < $100M
            score += 20
        elif market_cap < 500_000_000:  # < $500M
            score += 10
        
        # Volatility factor (higher volatility = higher potential)
        volatility = company.get("volatility_20d", 0)
        if volatility > 5:
            score += 10
        elif volatility > 3:
            score += 5
        
        # Sector factor
        sector = company.get("sector_category", "other")
        sector_multipliers = {
            "biotech": 1.3,
            "ai_ml": 1.2,
            "clean_energy": 1.2,
            "space_tech": 1.1,
            "quantum": 1.1,
            "cybersecurity": 1.1,
            "fintech": 1.1,
            "other": 1.0
        }
        score *= sector_multipliers.get(sector, 1.0)
        
        return min(100.0, max(0.0, score))
    
    def _get_fallback_ticker_list(self) -> List[str]:
        """Fallback list of known microcap tickers."""
        return [
            # Biotech microcaps
            "ATOS", "CYTH", "ADMP", "ATNF", "BCRX", "CRIS", "DMAC", "EARS", "FENC", "GTHX",
            # AI/Tech microcaps  
            "BBAI", "CXAI", "SOUN", "VERI", "AITX", "GFAI", "RGTI", "CLSK", "MARA", "RIOT",
            # Clean energy microcaps
            "PLUG", "FCEL", "BLDP", "HYLN", "NKLA", "WKHS", "RIDE", "GOEV", "ARVL", "LCID",
            # Space/Defense microcaps
            "ASTR", "RKLB", "SPCE", "IRDM", "KTOS", "AVAV", "UAVS", "DPRO", "VSAT", "GILT"
        ]
    
    def _is_cache_valid(self) -> bool:
        """Check if cached universe is still valid."""
        if not self.cache_file.exists():
            return False
        
        if not self.last_updated:
            try:
                self._load_cache()
            except:
                return False
        
        if not self.last_updated:
            return False
        
        hours_since_update = (datetime.now() - self.last_updated).total_seconds() / 3600
        return hours_since_update < self.update_frequency_hours
    
    def _load_cache(self) -> Dict[str, Dict[str, Any]]:
        """Load universe from cache file."""
        try:
            with open(self.cache_file, 'r') as f:
                data = json.load(f)
            
            self.universe = data.get("universe", {})
            self.last_updated = datetime.fromisoformat(data.get("last_updated", datetime.now().isoformat()))
            
            return self.universe
            
        except Exception as e:
            self.logger.error(f"Error loading cache: {e}")
            return {}
    
    def _save_cache(self):
        """Save universe to cache file."""
        try:
            # Create directory if it doesn't exist
            self.cache_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                "universe": self.universe,
                "last_updated": self.last_updated.isoformat() if self.last_updated else datetime.now().isoformat(),
                "total_companies": len(self.universe),
                "sector_breakdown": self._get_sector_breakdown()
            }
            
            with open(self.cache_file, 'w') as f:
                json.dump(data, f, indent=2)
            
            self.logger.info(f"Universe cached to {self.cache_file}")
            
        except Exception as e:
            self.logger.error(f"Error saving cache: {e}")
    
    def _get_sector_breakdown(self) -> Dict[str, int]:
        """Get breakdown of companies by sector."""
        breakdown = {}
        for company in self.universe.values():
            sector = company.get("sector_category", "other")
            breakdown[sector] = breakdown.get(sector, 0) + 1
        return breakdown
    
    def get_universe_stats(self) -> Dict[str, Any]:
        """Get statistics about the current universe."""
        if not self.universe:
            return {}
        
        companies = list(self.universe.values())
        
        return {
            "total_companies": len(companies),
            "last_updated": self.last_updated.isoformat() if self.last_updated else None,
            "sector_breakdown": self._get_sector_breakdown(),
            "market_cap_range": {
                "min": min(c.get("market_cap", 0) for c in companies),
                "max": max(c.get("market_cap", 0) for c in companies),
                "avg": sum(c.get("market_cap", 0) for c in companies) / len(companies)
            },
            "top_growth_potential": sorted(
                [(k, v.get("growth_potential_score", 0)) for k, v in self.universe.items()],
                key=lambda x: x[1],
                reverse=True
            )[:10]
        }
    
    def get_sector_companies(self, sector: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get companies in a specific sector."""
        sector_companies = [
            company for company in self.universe.values()
            if company.get("sector_category") == sector
        ]
        
        # Sort by growth potential score
        sector_companies.sort(key=lambda x: x.get("growth_potential_score", 0), reverse=True)
        
        return sector_companies[:limit]
