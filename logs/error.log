2025-06-30 16:01:01 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - test_connection:62 - Alpaca connection test failed: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:01:01 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12781ddb0>
2025-06-30 16:01:01 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12780b6a0>, 191666.338718041)]']
connector: <aiohttp.connector.TCPConnector object at 0x12781f100>
2025-06-30 16:01:03 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - get_assets:119 - Error fetching assets: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:01:04 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_snapshots:209 - Error fetching snapshots: Client error 404: {"status":"NotFound","request_id":"3c3fdae125f636a6dac3885254d161ca"}
2025-06-30 16:01:04 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - get_snapshots:336 - Error fetching snapshots: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:01:06 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - get_assets:119 - Error fetching assets: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:01:07 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_snapshots:209 - Error fetching snapshots: Client error 404: {"status":"NotFound","request_id":"e72d7e370e28c8accf61e4ad30ebeea1"}
2025-06-30 16:01:07 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - get_snapshots:336 - Error fetching snapshots: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:38:57 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - test_connection:62 - Alpaca connection test failed: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:39:00 - quantum_edge.data.sources.sec_client.SECClient - ERROR - test_connection:63 - SEC API connection test failed: Client error 404: <!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot GET /filing</pre>
</body>
</html>

2025-06-30 16:39:01 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 16:39:27 - quantum_edge.data.sources.sec_client.SECClient - ERROR - get_insider_trading:227 - Error fetching insider trading data: Client error 404: <!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot GET /insider-trading</pre>
</body>
</html>

2025-06-30 16:39:27 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 16:39:28 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 16:39:29 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - analyze_innovation_trends:382 - Error analyzing trends for AI/ML: PatentsClient.search_patents() got an unexpected keyword argument 'days_back'
2025-06-30 16:39:29 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - analyze_innovation_trends:382 - Error analyzing trends for Quantum: PatentsClient.search_patents() got an unexpected keyword argument 'days_back'
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - ERROR - _create_quantum_features:186 - Error creating quantum features: 'numpy.ndarray' object has no attribute 'diff'
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - ERROR - _create_quantum_features:186 - Error creating quantum features: 'numpy.ndarray' object has no attribute 'diff'
2025-06-30 16:56:10 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - ERROR - train:208 - Error training XGBoost model: XGBClassifier.fit() got an unexpected keyword argument 'early_stopping_rounds'
2025-06-30 16:56:15 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x1242b81f0>
2025-06-30 16:56:15 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x1242b83a0>
2025-06-30 16:56:15 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x1242d90c0>, 194958.545076625)]']
connector: <aiohttp.connector.TCPConnector object at 0x1242b8400>
2025-06-30 16:56:15 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x1242b9660>
2025-06-30 16:56:15 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x1242d8ee0>, 194971.378837791)]']
connector: <aiohttp.connector.TCPConnector object at 0x1242b95d0>
2025-06-30 17:21:09 - quantum_edge.data.sources.sec_client.SECClient - ERROR - get_filings:115 - Error fetching SEC filings: Client error 404: <!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot GET /filing</pre>
</body>
</html>

2025-06-30 17:21:15 - quantum_edge.data.sources.sec_client.SECClient - ERROR - get_filings:115 - Error fetching SEC filings: Client error 404: <!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot GET /filing</pre>
</body>
</html>

2025-06-30 17:21:21 - quantum_edge.data.sources.sec_client.SECClient - ERROR - get_filings:115 - Error fetching SEC filings: Client error 404: <!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot GET /filing</pre>
</body>
</html>

2025-06-30 17:21:30 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 17:21:31 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 17:21:32 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 17:21:33 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - ERROR - _get_all_us_tickers:131 - Error getting US tickers: 'PolygonClient' object has no attribute 'get_tickers'
2025-06-30 17:21:35 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:258 - Error fetching ticker details for CYTH: Client error 404: {"status":"NOT_FOUND","request_id":"2eeb526023c7fb821e128b27ead31b7a","message":"Ticker not found."}
2025-06-30 17:21:35 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:258 - Error fetching ticker details for ADMP: Client error 404: {"status":"NOT_FOUND","request_id":"647ae506a2c6ba0089d569c41dcb4742","message":"Ticker not found."}
2025-06-30 17:26:30 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ca6fbb0>
2025-06-30 17:26:30 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ca6c100>
2025-06-30 17:26:30 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ca6df30>
2025-06-30 17:26:30 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ca6d120>
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12f5e2bc0>
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12f53eb60>, 200048.866253333)]']
connector: <aiohttp.connector.TCPConnector object at 0x12f5e26e0>
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12f5e1750>
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12f60cbe0>, 200049.876878416)]']
connector: <aiohttp.connector.TCPConnector object at 0x12f5e1840>
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12f5c8130>
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12f60d1e0>, 200051.099856541)]']
connector: <aiohttp.connector.TCPConnector object at 0x12f5ca3e0>
2025-06-30 18:21:13 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12f9ce500>
2025-06-30 18:21:13 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12f53e860>, 200078.454677083)]']
connector: <aiohttp.connector.TCPConnector object at 0x12f9ce410>
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ba6c8b0>
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12ba96f20>, 200168.371325083)]']
connector: <aiohttp.connector.TCPConnector object at 0x12ba6df00>
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ba7c520>
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12bad1000>, 200169.23748475)]']
connector: <aiohttp.connector.TCPConnector object at 0x12ba7cf40>
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ba7c9a0>
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12bad1600>, 200169.953886333)]']
connector: <aiohttp.connector.TCPConnector object at 0x12ba7cd00>
2025-06-30 18:23:17 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x13a0f2e00>
2025-06-30 18:23:17 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12bad0f40>, 200201.810363875)]']
connector: <aiohttp.connector.TCPConnector object at 0x13a0f2d10>
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x16a6b0100>
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x16a67b100>, 200313.070661916)]']
connector: <aiohttp.connector.TCPConnector object at 0x16a661360>
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x16a6b1f60>
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x16a6d11e0>, 200313.761463041)]']
connector: <aiohttp.connector.TCPConnector object at 0x16a6b1e70>
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x16a6b3310>
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x16a6d17e0>, 200314.902208416)]']
connector: <aiohttp.connector.TCPConnector object at 0x16a6b3220>
2025-06-30 18:25:38 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x16c7f2d10>
2025-06-30 18:25:38 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x16a6d1120>, 200342.627925041)]']
connector: <aiohttp.connector.TCPConnector object at 0x16c7f2c20>
2025-06-30 18:38:13 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x13053a380>
2025-06-30 18:38:13 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x13055f820>, 201098.47730875)]']
connector: <aiohttp.connector.TCPConnector object at 0x13053b310>
2025-06-30 18:38:15 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:291 - Error fetching ticker details for AAPL: Client error 401: {"status":"ERROR","request_id":"acda10c7130915e6f74731d1253829ef","error":"Unknown API Key"}
2025-06-30 18:39:54 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x14404fa90>
2025-06-30 18:39:54 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x144069600>, 201199.541318916)]']
connector: <aiohttp.connector.TCPConnector object at 0x14404f7c0>
2025-06-30 18:39:56 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:291 - Error fetching ticker details for AAPL: Client error 401: {"status":"ERROR","request_id":"af6f8737d3be2a48cb64674db13164fd","error":"Unknown API Key"}
2025-06-30 18:42:19 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:291 - Error fetching ticker details for AAPL: Client error 401: {"status":"ERROR","request_id":"ff3ecc4be03b4a1d0190fe750942f7aa","error":"Unknown API Key"}
2025-06-30 18:43:10 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:291 - Error fetching ticker details for AAPL: Client error 401: {"status":"ERROR","request_id":"4fa01cdbde9f50c2318fe104169cd8a0","error":"Unknown API Key"}
2025-06-30 19:39:33 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x130e893f0>
2025-06-30 19:39:33 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x130e89450>
2025-06-30 19:39:33 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x130e894b0>
2025-06-30 20:11:02 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x11c0908b0>
2025-06-30 23:00:05 - quantum_edge.data.sources.benzinga_client.BenzingaClient - ERROR - get_news:142 - Error fetching news: Client error 404: {
  "message":"no Route matched with those values",
  "request_id":"b0f9901e36ee35716eb7215d21c4ac2b"
}
2025-06-30 23:00:05 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x14b434250>
2025-06-30 23:00:05 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x14b3f73a0>, 216808.511952541)]']
connector: <aiohttp.connector.TCPConnector object at 0x14b434160>
2025-06-30 23:00:05 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x14b4360b0>
2025-06-30 23:00:05 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x14b459480>, 216809.149768)]']
connector: <aiohttp.connector.TCPConnector object at 0x14b435fc0>
2025-06-30 23:00:05 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x14b437460>
2025-06-30 23:00:05 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x14b459a80>, 216809.798749958)]']
connector: <aiohttp.connector.TCPConnector object at 0x14b437370>
2025-06-30 23:00:31 - quantum_edge.data.sources.benzinga_client.BenzingaClient - ERROR - get_news:142 - Error fetching news: Client error 404: {
  "message":"no Route matched with those values",
  "request_id":"232cd31178e24f292041b97da5582e1c"
}
2025-06-30 23:00:32 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x150b72d10>
2025-06-30 23:00:32 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x14b459420>, 216836.499668375)]']
connector: <aiohttp.connector.TCPConnector object at 0x150b72c20>
