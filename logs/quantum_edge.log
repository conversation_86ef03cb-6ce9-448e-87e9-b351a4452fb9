2025-06-30 15:56:21 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 15:56:21 - quantum_edge.config.settings - WARNING - _validate_configuration:174 - Missing API keys (development mode): Polygon.io API key (POLYGON_API_KEY), Alpaca API key (ALPACA_API_KEY), Alpaca secret key (ALPACA_SECRET_KEY), Alpha Vantage API key (ALPHA_VANTAGE_API_KEY), Benzinga API key (BENZINGA_API_KEY)
2025-06-30 15:56:21 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 15:56:21 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:45 - MarketDataAggregator initialized with sources: []
2025-06-30 15:56:21 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:383 - MarketDataAggregator connections closed
2025-06-30 15:56:21 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:45 - MarketDataAggregator initialized with sources: []
2025-06-30 15:56:21 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - WARNING - get_premarket_gappers:65 - No Polygon client available for gappers data
2025-06-30 15:56:21 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:383 - MarketDataAggregator connections closed
2025-06-30 16:00:00 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 16:00:00 - quantum_edge.config.settings - WARNING - _validate_configuration:174 - Missing API keys (development mode): Polygon.io API key (POLYGON_API_KEY), Alpaca API key (ALPACA_API_KEY), Alpaca secret key (ALPACA_SECRET_KEY), Alpha Vantage API key (ALPHA_VANTAGE_API_KEY), Benzinga API key (BENZINGA_API_KEY)
2025-06-30 16:00:00 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 16:00:00 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:45 - MarketDataAggregator initialized with sources: []
2025-06-30 16:00:00 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:383 - MarketDataAggregator connections closed
2025-06-30 16:00:00 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:45 - MarketDataAggregator initialized with sources: []
2025-06-30 16:00:00 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - WARNING - get_premarket_gappers:65 - No Polygon client available for gappers data
2025-06-30 16:00:00 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:383 - MarketDataAggregator connections closed
2025-06-30 16:00:59 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 16:00:59 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 16:01:00 - quantum_edge.data.sources.polygon_client.PolygonClient - WARNING - get_gainers:88 - No results in gainers response
2025-06-30 16:01:01 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - test_connection:62 - Alpaca connection test failed: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:01:01 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12781ddb0>
2025-06-30 16:01:01 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12780b6a0>, 191666.338718041)]']
connector: <aiohttp.connector.TCPConnector object at 0x12781f100>
2025-06-30 16:01:01 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:45 - MarketDataAggregator initialized with sources: ['polygon', 'alpaca']
2025-06-30 16:01:02 - quantum_edge.data.sources.polygon_client.PolygonClient - WARNING - get_gainers:88 - No results in gainers response
2025-06-30 16:01:03 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - get_assets:119 - Error fetching assets: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:01:03 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - enrich_with_fundamentals:141 - Enriched 0 symbols with Alpaca asset data
2025-06-30 16:01:04 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - enrich_with_fundamentals:186 - Enriched symbols with Polygon ticker details
2025-06-30 16:01:04 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_snapshots:209 - Error fetching snapshots: Client error 404: {"status":"NotFound","request_id":"3c3fdae125f636a6dac3885254d161ca"}
2025-06-30 16:01:04 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_market_snapshots:245 - Got Polygon snapshots for 0 symbols
2025-06-30 16:01:04 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - get_snapshots:336 - Error fetching snapshots: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:01:04 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_market_snapshots:272 - Got Alpaca snapshots for 0 symbols
2025-06-30 16:01:04 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:383 - MarketDataAggregator connections closed
2025-06-30 16:01:04 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:45 - MarketDataAggregator initialized with sources: ['polygon', 'alpaca']
2025-06-30 16:01:05 - quantum_edge.data.sources.polygon_client.PolygonClient - WARNING - get_gainers:88 - No results in gainers response
2025-06-30 16:01:06 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - get_assets:119 - Error fetching assets: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:01:06 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - enrich_with_fundamentals:141 - Enriched 0 symbols with Alpaca asset data
2025-06-30 16:01:07 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - enrich_with_fundamentals:186 - Enriched symbols with Polygon ticker details
2025-06-30 16:01:07 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_snapshots:209 - Error fetching snapshots: Client error 404: {"status":"NotFound","request_id":"e72d7e370e28c8accf61e4ad30ebeea1"}
2025-06-30 16:01:07 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_market_snapshots:245 - Got Polygon snapshots for 0 symbols
2025-06-30 16:01:07 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - get_snapshots:336 - Error fetching snapshots: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:01:07 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_market_snapshots:272 - Got Alpaca snapshots for 0 symbols
2025-06-30 16:01:07 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:383 - MarketDataAggregator connections closed
2025-06-30 16:03:44 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 16:03:44 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 16:03:47 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:45 - MarketDataAggregator initialized with sources: ['polygon']
2025-06-30 16:08:58 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:383 - MarketDataAggregator connections closed
2025-06-30 16:38:18 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 16:38:18 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 16:38:56 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 16:38:56 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 16:38:57 - quantum_edge.data.sources.alpaca_client.AlpacaClient - ERROR - test_connection:62 - Alpaca connection test failed: Client error 403: <html>
<head><title>403 Forbidden</title></head>
<body>
<center><h1>403 Forbidden</h1></center>
<hr><center>nginx</center>
</body>
</html>

2025-06-30 16:39:00 - quantum_edge.data.sources.sec_client.SECClient - ERROR - test_connection:63 - SEC API connection test failed: Client error 404: <!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot GET /filing</pre>
</body>
</html>

2025-06-30 16:39:01 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 16:39:01 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpaca', 'alpha_vantage', 'benzinga', 'sec', 'patents']
2025-06-30 16:39:01 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:435 - Fetching fundamental data from Alpha Vantage...
2025-06-30 16:39:25 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:443 - Retrieved fundamental data for 2 symbols
2025-06-30 16:39:25 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:451 - Fetching sentiment data from Benzinga...
2025-06-30 16:39:26 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:459 - Retrieved sentiment data for 2 symbols
2025-06-30 16:39:26 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:467 - Fetching insider trading data from SEC...
2025-06-30 16:39:27 - quantum_edge.data.sources.sec_client.SECClient - ERROR - get_insider_trading:227 - Error fetching insider trading data: Client error 404: <!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot GET /insider-trading</pre>
</body>
</html>

2025-06-30 16:39:27 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:475 - Retrieved insider data for 2 symbols
2025-06-30 16:39:27 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:483 - Fetching patent data...
2025-06-30 16:39:27 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 16:39:28 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 16:39:29 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:495 - Retrieved patent data for 2 symbols
2025-06-30 16:39:29 - quantum_edge.data.sources.patents_client.PatentsClient - INFO - analyze_innovation_trends:314 - Analyzing innovation trends for AI/ML
2025-06-30 16:39:29 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - analyze_innovation_trends:382 - Error analyzing trends for AI/ML: PatentsClient.search_patents() got an unexpected keyword argument 'days_back'
2025-06-30 16:39:29 - quantum_edge.data.sources.patents_client.PatentsClient - INFO - analyze_innovation_trends:314 - Analyzing innovation trends for Quantum
2025-06-30 16:39:29 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - analyze_innovation_trends:382 - Error analyzing trends for Quantum: PatentsClient.search_patents() got an unexpected keyword argument 'days_back'
2025-06-30 16:39:29 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:691 - MarketDataAggregator connections closed
2025-06-30 16:54:57 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 16:54:57 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 16:54:57 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpha_vantage', 'benzinga']
2025-06-30 16:55:40 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 16:55:40 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 16:55:40 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpha_vantage', 'benzinga']
2025-06-30 16:55:41 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:435 - Fetching fundamental data from Alpha Vantage...
2025-06-30 16:56:06 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:443 - Retrieved fundamental data for 2 symbols
2025-06-30 16:56:06 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:451 - Fetching sentiment data from Benzinga...
2025-06-30 16:56:07 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:459 - Retrieved sentiment data for 2 symbols
2025-06-30 16:56:07 - quantum_edge.features.technical_indicators.TechnicalIndicators - INFO - __init__:20 - Technical indicators calculator initialized
2025-06-30 16:56:09 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - _initialize_quantum_device:58 - Quantum device initialized: lightning.qubit
2025-06-30 16:56:09 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - __init__:47 - Quantum feature engineer initialized with 4 qubits
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - __init__:65 - Feature pipeline initialized
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:86 - Creating features for AAPL
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:94 - Added 83 technical features
2025-06-30 16:56:09 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - create_quantum_features:84 - Creating quantum-enhanced features
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - ERROR - _create_quantum_features:186 - Error creating quantum features: 'numpy.ndarray' object has no attribute 'diff'
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:100 - Added 0 quantum features
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:108 - Added 8 fundamental features
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:116 - Added 9 sentiment features
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:121 - Added 12 time features
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:126 - Added 31 statistical features
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:369 - Removed 20 constant features
2025-06-30 16:56:09 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:385 - Removed 54 highly correlated features
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _select_features:422 - Selected 50 features using mutual information
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:136 - Final feature set: 50 features
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:86 - Creating features for MSFT
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:94 - Added 83 technical features
2025-06-30 16:56:10 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - create_quantum_features:84 - Creating quantum-enhanced features
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - ERROR - _create_quantum_features:186 - Error creating quantum features: 'numpy.ndarray' object has no attribute 'diff'
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:100 - Added 0 quantum features
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:108 - Added 8 fundamental features
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:116 - Added 9 sentiment features
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:121 - Added 12 time features
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:126 - Added 31 statistical features
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:369 - Removed 19 constant features
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:385 - Removed 60 highly correlated features
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _select_features:422 - Selected 50 features using mutual information
2025-06-30 16:56:10 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:136 - Final feature set: 50 features
2025-06-30 16:56:10 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - __init__:76 - Initialized XGBoost model (classifier)
2025-06-30 16:56:10 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - train:96 - Starting XGBoost training
2025-06-30 16:56:10 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - ERROR - train:208 - Error training XGBoost model: XGBClassifier.fit() got an unexpected keyword argument 'early_stopping_rounds'
2025-06-30 16:56:10 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - __init__:76 - Initialized LightGBM model (classifier)
2025-06-30 16:56:11 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:100 - Starting LightGBM training
2025-06-30 16:56:11 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:56:11 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:56:11 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:241 - LightGBM training completed in 0.06 seconds
2025-06-30 16:56:11 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - __init__:76 - Initialized QuantumClassifier model (quantum_classifier)
2025-06-30 16:56:11 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _initialize_quantum_device:85 - Quantum device initialized: lightning.qubit
2025-06-30 16:56:11 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - __init__:71 - Quantum classifier initialized with 4 qubits
2025-06-30 16:56:11 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:173 - Starting quantum classifier training
2025-06-30 16:56:11 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:270 - Quantum training iteration 0, cost: 1.491398
2025-06-30 16:56:12 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:266 - Quantum training converged at iteration 11
2025-06-30 16:56:13 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:226 - Quantum classifier training completed
2025-06-30 16:56:13 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - __init__:76 - Initialized EnsemblePredictor model (ensemble)
2025-06-30 16:56:13 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - __init__:68 - Initialized ensemble with 2 models using weighted_voting
2025-06-30 16:56:13 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:92 - Training ensemble with 2 models
2025-06-30 16:56:13 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:107 - Training base model: LightGBM
2025-06-30 16:56:13 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:100 - Starting LightGBM training
2025-06-30 16:56:13 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:56:13 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:56:13 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:241 - LightGBM training completed in 0.06 seconds
2025-06-30 16:56:13 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:119 - Completed training: LightGBM
2025-06-30 16:56:13 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:107 - Training base model: QuantumClassifier
2025-06-30 16:56:13 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:173 - Starting quantum classifier training
2025-06-30 16:56:13 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:270 - Quantum training iteration 0, cost: 1.860759
2025-06-30 16:56:14 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:266 - Quantum training converged at iteration 11
2025-06-30 16:56:15 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:226 - Quantum classifier training completed
2025-06-30 16:56:15 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:119 - Completed training: QuantumClassifier
2025-06-30 16:56:15 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - _calculate_model_weights:342 - Calculated model weights: {'LightGBM': 0.9090909090909091, 'QuantumClassifier': 0.09090909090909091}
2025-06-30 16:56:15 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:153 - Ensemble training completed successfully
2025-06-30 16:56:15 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x1242b81f0>
2025-06-30 16:56:15 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x1242b83a0>
2025-06-30 16:56:15 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x1242d90c0>, 194958.545076625)]']
connector: <aiohttp.connector.TCPConnector object at 0x1242b8400>
2025-06-30 16:56:15 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x1242b9660>
2025-06-30 16:56:15 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x1242d8ee0>, 194971.378837791)]']
connector: <aiohttp.connector.TCPConnector object at 0x1242b95d0>
2025-06-30 16:57:21 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 16:57:21 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 16:57:21 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpha_vantage', 'benzinga']
2025-06-30 16:57:22 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:435 - Fetching fundamental data from Alpha Vantage...
2025-06-30 16:57:46 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:443 - Retrieved fundamental data for 2 symbols
2025-06-30 16:57:46 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:451 - Fetching sentiment data from Benzinga...
2025-06-30 16:57:47 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:459 - Retrieved sentiment data for 2 symbols
2025-06-30 16:57:47 - quantum_edge.features.technical_indicators.TechnicalIndicators - INFO - __init__:20 - Technical indicators calculator initialized
2025-06-30 16:57:48 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - _initialize_quantum_device:58 - Quantum device initialized: lightning.qubit
2025-06-30 16:57:48 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - __init__:47 - Quantum feature engineer initialized with 4 qubits
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - __init__:65 - Feature pipeline initialized
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:86 - Creating features for AAPL
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:94 - Added 83 technical features
2025-06-30 16:57:48 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - create_quantum_features:84 - Creating quantum-enhanced features
2025-06-30 16:57:48 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - create_quantum_features:108 - Created 78 quantum features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:100 - Added 90 quantum features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:108 - Added 8 fundamental features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:116 - Added 9 sentiment features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:121 - Added 12 time features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:126 - Added 31 statistical features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:369 - Removed 33 constant features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:385 - Removed 72 highly correlated features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _select_features:422 - Selected 50 features using mutual information
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:136 - Final feature set: 50 features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:86 - Creating features for MSFT
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:94 - Added 83 technical features
2025-06-30 16:57:48 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - create_quantum_features:84 - Creating quantum-enhanced features
2025-06-30 16:57:48 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - create_quantum_features:108 - Created 78 quantum features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:100 - Added 90 quantum features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:108 - Added 8 fundamental features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:116 - Added 9 sentiment features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:121 - Added 12 time features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:126 - Added 31 statistical features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:369 - Removed 32 constant features
2025-06-30 16:57:48 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:385 - Removed 96 highly correlated features
2025-06-30 16:57:49 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _select_features:422 - Selected 50 features using mutual information
2025-06-30 16:57:49 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:136 - Final feature set: 50 features
2025-06-30 16:57:49 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - __init__:76 - Initialized XGBoost model (classifier)
2025-06-30 16:57:49 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - train:96 - Starting XGBoost training
2025-06-30 16:57:49 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:57:49 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:57:49 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - train:202 - XGBoost training completed in 0.13 seconds
2025-06-30 16:57:49 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - __init__:76 - Initialized LightGBM model (classifier)
2025-06-30 16:57:49 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:100 - Starting LightGBM training
2025-06-30 16:57:49 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:57:49 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:57:49 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:241 - LightGBM training completed in 0.06 seconds
2025-06-30 16:57:49 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - __init__:76 - Initialized QuantumClassifier model (quantum_classifier)
2025-06-30 16:57:49 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _initialize_quantum_device:85 - Quantum device initialized: lightning.qubit
2025-06-30 16:57:49 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - __init__:71 - Quantum classifier initialized with 4 qubits
2025-06-30 16:57:49 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:173 - Starting quantum classifier training
2025-06-30 16:57:49 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:270 - Quantum training iteration 0, cost: 1.696601
2025-06-30 16:57:51 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:266 - Quantum training converged at iteration 11
2025-06-30 16:57:51 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:226 - Quantum classifier training completed
2025-06-30 16:57:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - __init__:76 - Initialized EnsemblePredictor model (ensemble)
2025-06-30 16:57:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - __init__:68 - Initialized ensemble with 3 models using weighted_voting
2025-06-30 16:57:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:92 - Training ensemble with 3 models
2025-06-30 16:57:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:107 - Training base model: XGBoost
2025-06-30 16:57:51 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - train:96 - Starting XGBoost training
2025-06-30 16:57:51 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:57:51 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:57:51 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - train:202 - XGBoost training completed in 0.12 seconds
2025-06-30 16:57:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:119 - Completed training: XGBoost
2025-06-30 16:57:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:107 - Training base model: LightGBM
2025-06-30 16:57:51 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:100 - Starting LightGBM training
2025-06-30 16:57:51 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:57:51 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:57:51 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:241 - LightGBM training completed in 0.04 seconds
2025-06-30 16:57:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:119 - Completed training: LightGBM
2025-06-30 16:57:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:107 - Training base model: QuantumClassifier
2025-06-30 16:57:51 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:173 - Starting quantum classifier training
2025-06-30 16:57:51 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:270 - Quantum training iteration 0, cost: 1.254572
2025-06-30 16:57:53 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:266 - Quantum training converged at iteration 11
2025-06-30 16:57:53 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:226 - Quantum classifier training completed
2025-06-30 16:57:53 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:119 - Completed training: QuantumClassifier
2025-06-30 16:57:53 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - _calculate_model_weights:342 - Calculated model weights: {'XGBoost': 0.47619047619047616, 'LightGBM': 0.47619047619047616, 'QuantumClassifier': 0.047619047619047616}
2025-06-30 16:57:53 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:153 - Ensemble training completed successfully
2025-06-30 16:57:53 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:691 - MarketDataAggregator connections closed
2025-06-30 16:58:11 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 16:58:11 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 16:58:11 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpha_vantage', 'benzinga']
2025-06-30 16:58:12 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:435 - Fetching fundamental data from Alpha Vantage...
2025-06-30 16:58:37 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:443 - Retrieved fundamental data for 2 symbols
2025-06-30 16:58:37 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:451 - Fetching sentiment data from Benzinga...
2025-06-30 16:58:37 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:459 - Retrieved sentiment data for 2 symbols
2025-06-30 16:58:37 - quantum_edge.features.technical_indicators.TechnicalIndicators - INFO - __init__:20 - Technical indicators calculator initialized
2025-06-30 16:58:39 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - _initialize_quantum_device:58 - Quantum device initialized: lightning.qubit
2025-06-30 16:58:39 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - __init__:47 - Quantum feature engineer initialized with 4 qubits
2025-06-30 16:58:39 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - __init__:65 - Feature pipeline initialized
2025-06-30 16:58:39 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:86 - Creating features for AAPL
2025-06-30 16:58:39 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:94 - Added 83 technical features
2025-06-30 16:58:39 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - create_quantum_features:84 - Creating quantum-enhanced features
2025-06-30 16:58:39 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - create_quantum_features:108 - Created 78 quantum features
2025-06-30 16:58:39 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:100 - Added 90 quantum features
2025-06-30 16:58:39 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:108 - Added 8 fundamental features
2025-06-30 16:58:39 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:116 - Added 9 sentiment features
2025-06-30 16:58:39 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:121 - Added 12 time features
2025-06-30 16:58:39 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:126 - Added 31 statistical features
2025-06-30 16:58:39 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:369 - Removed 33 constant features
2025-06-30 16:58:39 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:385 - Removed 72 highly correlated features
2025-06-30 16:58:41 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _select_features:422 - Selected 50 features using mutual information
2025-06-30 16:58:41 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:136 - Final feature set: 50 features
2025-06-30 16:58:41 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:86 - Creating features for MSFT
2025-06-30 16:58:41 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:94 - Added 83 technical features
2025-06-30 16:58:41 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - create_quantum_features:84 - Creating quantum-enhanced features
2025-06-30 16:58:42 - quantum_edge.models.quantum.quantum_feature_engineer.QuantumFeatureEngineer - INFO - create_quantum_features:108 - Created 78 quantum features
2025-06-30 16:58:42 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:100 - Added 90 quantum features
2025-06-30 16:58:42 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:108 - Added 8 fundamental features
2025-06-30 16:58:42 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:116 - Added 9 sentiment features
2025-06-30 16:58:42 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:121 - Added 12 time features
2025-06-30 16:58:42 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:126 - Added 31 statistical features
2025-06-30 16:58:42 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:369 - Removed 32 constant features
2025-06-30 16:58:42 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _clean_features:385 - Removed 96 highly correlated features
2025-06-30 16:58:42 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - _select_features:422 - Selected 50 features using mutual information
2025-06-30 16:58:42 - quantum_edge.features.feature_pipeline.FeaturePipeline - INFO - create_features:136 - Final feature set: 50 features
2025-06-30 16:58:42 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - __init__:76 - Initialized XGBoost model (classifier)
2025-06-30 16:58:42 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - train:96 - Starting XGBoost training
2025-06-30 16:58:43 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:58:43 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:58:43 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - train:202 - XGBoost training completed in 0.50 seconds
2025-06-30 16:58:43 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - __init__:76 - Initialized LightGBM model (classifier)
2025-06-30 16:58:43 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:100 - Starting LightGBM training
2025-06-30 16:58:43 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:58:43 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:58:43 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:241 - LightGBM training completed in 0.45 seconds
2025-06-30 16:58:43 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - __init__:76 - Initialized QuantumClassifier model (quantum_classifier)
2025-06-30 16:58:43 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _initialize_quantum_device:85 - Quantum device initialized: lightning.qubit
2025-06-30 16:58:43 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - __init__:71 - Quantum classifier initialized with 4 qubits
2025-06-30 16:58:43 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:173 - Starting quantum classifier training
2025-06-30 16:58:44 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:270 - Quantum training iteration 0, cost: 1.391887
2025-06-30 16:58:50 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:266 - Quantum training converged at iteration 11
2025-06-30 16:58:51 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:226 - Quantum classifier training completed
2025-06-30 16:58:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - __init__:76 - Initialized EnsemblePredictor model (ensemble)
2025-06-30 16:58:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - __init__:68 - Initialized ensemble with 3 models using weighted_voting
2025-06-30 16:58:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:92 - Training ensemble with 3 models
2025-06-30 16:58:51 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:107 - Training base model: XGBoost
2025-06-30 16:58:51 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - train:96 - Starting XGBoost training
2025-06-30 16:58:51 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:58:51 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:58:52 - quantum_edge.models.ensemble.xgboost_model.XGBoostModel - INFO - train:202 - XGBoost training completed in 0.67 seconds
2025-06-30 16:58:52 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:119 - Completed training: XGBoost
2025-06-30 16:58:52 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:107 - Training base model: LightGBM
2025-06-30 16:58:52 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:100 - Starting LightGBM training
2025-06-30 16:58:52 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:58:52 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - WARNING - validate_features:153 - Found missing values in features, filling with median
2025-06-30 16:58:52 - quantum_edge.models.ensemble.lightgbm_model.LightGBMModel - INFO - train:241 - LightGBM training completed in 0.61 seconds
2025-06-30 16:58:52 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:119 - Completed training: LightGBM
2025-06-30 16:58:52 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:107 - Training base model: QuantumClassifier
2025-06-30 16:58:52 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:173 - Starting quantum classifier training
2025-06-30 16:58:53 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:270 - Quantum training iteration 0, cost: 1.325217
2025-06-30 16:58:59 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - _train_quantum_model:266 - Quantum training converged at iteration 11
2025-06-30 16:59:00 - quantum_edge.models.quantum.quantum_classifier.QuantumClassifier - INFO - train:226 - Quantum classifier training completed
2025-06-30 16:59:00 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:119 - Completed training: QuantumClassifier
2025-06-30 16:59:00 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - _calculate_model_weights:342 - Calculated model weights: {'XGBoost': 0.47619047619047616, 'LightGBM': 0.47619047619047616, 'QuantumClassifier': 0.047619047619047616}
2025-06-30 16:59:00 - quantum_edge.models.ensemble.ensemble_predictor.EnsemblePredictor - INFO - train:153 - Ensemble training completed successfully
2025-06-30 16:59:00 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:691 - MarketDataAggregator connections closed
2025-06-30 17:21:08 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 17:21:08 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 17:21:08 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpha_vantage', 'benzinga', 'sec', 'patents']
2025-06-30 17:21:08 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - INFO - __init__:63 - Microcap universe builder initialized
2025-06-30 17:21:08 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 17:21:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 17:21:08 - quantum_edge.discovery.discovery_engine.DiscoveryEngine - INFO - __init__:55 - Discovery engine initialized
2025-06-30 17:21:08 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 3 symbols
2025-06-30 17:21:09 - quantum_edge.data.sources.sec_client.SECClient - ERROR - get_filings:115 - Error fetching SEC filings: Client error 404: <!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot GET /filing</pre>
</body>
</html>

2025-06-30 17:21:15 - quantum_edge.data.sources.sec_client.SECClient - ERROR - get_filings:115 - Error fetching SEC filings: Client error 404: <!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot GET /filing</pre>
</body>
</html>

2025-06-30 17:21:21 - quantum_edge.data.sources.sec_client.SECClient - ERROR - get_filings:115 - Error fetching SEC filings: Client error 404: <!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Error</title>
</head>
<body>
<pre>Cannot GET /filing</pre>
</body>
</html>

2025-06-30 17:21:30 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 17:21:31 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 17:21:32 - quantum_edge.data.sources.patents_client.PatentsClient - ERROR - search_patents:149 - Error searching patents: Client error 404: <!DOCTYPE html>
<html lang=en>
  <meta charset=utf-8>
  <meta name=viewport content="initial-scale=1, minimum-scale=1, width=device-width">
  <title>Error 404 (Not Found)!!1</title>
  <style>
    *{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}
  </style>
  <a href=//www.google.com/><span id=logo aria-label=Google></span></a>
  <p><b>404.</b> <ins>That’s an error.</ins>
  <p>The requested URL <code>/v1/patents:search</code> was not found on this server.  <ins>That’s all we know.</ins>

2025-06-30 17:21:33 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 17:21:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 17:21:33 - quantum_edge.discovery.discovery_engine.DiscoveryEngine - INFO - discover_growth_opportunities:74 - 🚀 Starting QuantumEdge Growth Discovery Process
2025-06-30 17:21:33 - quantum_edge.discovery.discovery_engine.DiscoveryEngine - INFO - discover_growth_opportunities:96 - 📊 Step 1: Building microcap universe...
2025-06-30 17:21:33 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - INFO - build_universe:80 - Building fresh microcap universe...
2025-06-30 17:21:33 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - ERROR - _get_all_us_tickers:131 - Error getting US tickers: 'PolygonClient' object has no attribute 'get_tickers'
2025-06-30 17:21:33 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - INFO - build_universe:84 - Found 40 total US tickers
2025-06-30 17:21:34 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - WARNING - _get_recent_average_volume:218 - Error getting volume for ATOS: 'str' object has no attribute 'get'
2025-06-30 17:21:35 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:258 - Error fetching ticker details for CYTH: Client error 404: {"status":"NOT_FOUND","request_id":"2eeb526023c7fb821e128b27ead31b7a","message":"Ticker not found."}
2025-06-30 17:21:35 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:258 - Error fetching ticker details for ADMP: Client error 404: {"status":"NOT_FOUND","request_id":"647ae506a2c6ba0089d569c41dcb4742","message":"Ticker not found."}
2025-06-30 17:26:30 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ca6fbb0>
2025-06-30 17:26:30 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ca6c100>
2025-06-30 17:26:30 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ca6df30>
2025-06-30 17:26:30 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ca6d120>
2025-06-30 18:20:43 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 18:20:43 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12f5e2bc0>
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12f53eb60>, 200048.866253333)]']
connector: <aiohttp.connector.TCPConnector object at 0x12f5e26e0>
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12f5e1750>
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12f60cbe0>, 200049.876878416)]']
connector: <aiohttp.connector.TCPConnector object at 0x12f5e1840>
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12f5c8130>
2025-06-30 18:20:46 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12f60d1e0>, 200051.099856541)]']
connector: <aiohttp.connector.TCPConnector object at 0x12f5ca3e0>
2025-06-30 18:20:46 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpha_vantage', 'benzinga']
2025-06-30 18:20:46 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:435 - Fetching fundamental data from Alpha Vantage...
2025-06-30 18:21:11 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:443 - Retrieved fundamental data for 2 symbols
2025-06-30 18:21:11 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:451 - Fetching sentiment data from Benzinga...
2025-06-30 18:21:12 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:459 - Retrieved sentiment data for 2 symbols
2025-06-30 18:21:12 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:691 - MarketDataAggregator connections closed
2025-06-30 18:21:12 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - INFO - __init__:63 - Microcap universe builder initialized
2025-06-30 18:21:12 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 18:21:12 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 2 symbols
2025-06-30 18:21:12 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 18:21:12 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:21:12 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:21:12 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:110 - Risk manager initialized
2025-06-30 18:21:12 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:131 - Assessing risk for AAPL
2025-06-30 18:21:12 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:21:12 - quantum_edge.risk.position_sizer.PositionSizer - INFO - calculate_position_size:88 - Calculating position size for AAPL
2025-06-30 18:21:12 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:110 - Risk manager initialized
2025-06-30 18:21:12 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:21:12 - quantum_edge.backtesting.backtest_engine.BacktestEngine - INFO - __init__:102 - Backtest engine initialized
2025-06-30 18:21:12 - quantum_edge.backtesting.performance_analyzer.PerformanceAnalyzer - INFO - __init__:43 - Performance analyzer initialized
2025-06-30 18:21:12 - quantum_edge.backtesting.walk_forward_tester.WalkForwardTester - INFO - __init__:76 - Walk-forward tester initialized
2025-06-30 18:21:13 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 18:21:13 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:21:13 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 1 symbols
2025-06-30 18:21:13 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 18:21:13 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:21:13 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:110 - Risk manager initialized
2025-06-30 18:21:13 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:131 - Assessing risk for AAPL
2025-06-30 18:21:13 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:21:13 - quantum_edge.risk.position_sizer.PositionSizer - INFO - calculate_position_size:88 - Calculating position size for AAPL
2025-06-30 18:21:13 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12f9ce500>
2025-06-30 18:21:13 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12f53e860>, 200078.454677083)]']
connector: <aiohttp.connector.TCPConnector object at 0x12f9ce410>
2025-06-30 18:22:42 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 18:22:42 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ba6c8b0>
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12ba96f20>, 200168.371325083)]']
connector: <aiohttp.connector.TCPConnector object at 0x12ba6df00>
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ba7c520>
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12bad1000>, 200169.23748475)]']
connector: <aiohttp.connector.TCPConnector object at 0x12ba7cf40>
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x12ba7c9a0>
2025-06-30 18:22:45 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12bad1600>, 200169.953886333)]']
connector: <aiohttp.connector.TCPConnector object at 0x12ba7cd00>
2025-06-30 18:22:45 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpha_vantage', 'benzinga']
2025-06-30 18:22:45 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:435 - Fetching fundamental data from Alpha Vantage...
2025-06-30 18:23:14 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:443 - Retrieved fundamental data for 2 symbols
2025-06-30 18:23:14 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:451 - Fetching sentiment data from Benzinga...
2025-06-30 18:23:15 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:459 - Retrieved sentiment data for 2 symbols
2025-06-30 18:23:15 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:691 - MarketDataAggregator connections closed
2025-06-30 18:23:15 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - INFO - __init__:63 - Microcap universe builder initialized
2025-06-30 18:23:15 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 18:23:15 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 2 symbols
2025-06-30 18:23:15 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 18:23:15 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:23:15 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:23:15 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 18:23:15 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for AAPL
2025-06-30 18:23:15 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:23:15 - quantum_edge.risk.position_sizer.PositionSizer - INFO - calculate_position_size:88 - Calculating position size for AAPL
2025-06-30 18:23:15 - quantum_edge.risk.risk_metrics.RiskMetrics - INFO - __init__:61 - Risk metrics calculator initialized
2025-06-30 18:23:15 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 18:23:15 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:23:15 - quantum_edge.backtesting.backtest_engine.BacktestEngine - INFO - __init__:102 - Backtest engine initialized
2025-06-30 18:23:15 - quantum_edge.backtesting.performance_analyzer.PerformanceAnalyzer - INFO - __init__:43 - Performance analyzer initialized
2025-06-30 18:23:15 - quantum_edge.backtesting.walk_forward_tester.WalkForwardTester - INFO - __init__:76 - Walk-forward tester initialized
2025-06-30 18:23:15 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:23:15 - quantum_edge.web.dashboard.DashboardManager - INFO - __init__:26 - Dashboard manager initialized
2025-06-30 18:23:15 - quantum_edge.web.app.QuantumEdgeApp - INFO - __init__:47 - QuantumEdge web application initialized
2025-06-30 18:23:15 - quantum_edge.web.dashboard.DashboardManager - INFO - __init__:26 - Dashboard manager initialized
2025-06-30 18:23:17 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 18:23:17 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:23:17 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 1 symbols
2025-06-30 18:23:17 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 18:23:17 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:23:17 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 18:23:17 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for AAPL
2025-06-30 18:23:17 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:23:17 - quantum_edge.risk.position_sizer.PositionSizer - INFO - calculate_position_size:88 - Calculating position size for AAPL
2025-06-30 18:23:17 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x13a0f2e00>
2025-06-30 18:23:17 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x12bad0f40>, 200201.810363875)]']
connector: <aiohttp.connector.TCPConnector object at 0x13a0f2d10>
2025-06-30 18:25:07 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 18:25:07 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x16a6b0100>
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x16a67b100>, 200313.070661916)]']
connector: <aiohttp.connector.TCPConnector object at 0x16a661360>
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x16a6b1f60>
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x16a6d11e0>, 200313.761463041)]']
connector: <aiohttp.connector.TCPConnector object at 0x16a6b1e70>
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x16a6b3310>
2025-06-30 18:25:10 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x16a6d17e0>, 200314.902208416)]']
connector: <aiohttp.connector.TCPConnector object at 0x16a6b3220>
2025-06-30 18:25:10 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpha_vantage', 'benzinga']
2025-06-30 18:25:10 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:435 - Fetching fundamental data from Alpha Vantage...
2025-06-30 18:25:35 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:443 - Retrieved fundamental data for 2 symbols
2025-06-30 18:25:35 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:451 - Fetching sentiment data from Benzinga...
2025-06-30 18:25:36 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:459 - Retrieved sentiment data for 2 symbols
2025-06-30 18:25:36 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:691 - MarketDataAggregator connections closed
2025-06-30 18:25:36 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - INFO - __init__:63 - Microcap universe builder initialized
2025-06-30 18:25:36 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 18:25:36 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 2 symbols
2025-06-30 18:25:36 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 18:25:36 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:25:36 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:25:36 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 18:25:36 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for AAPL
2025-06-30 18:25:36 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:25:36 - quantum_edge.risk.position_sizer.PositionSizer - INFO - calculate_position_size:88 - Calculating position size for AAPL
2025-06-30 18:25:36 - quantum_edge.risk.risk_metrics.RiskMetrics - INFO - __init__:61 - Risk metrics calculator initialized
2025-06-30 18:25:36 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 18:25:36 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:25:36 - quantum_edge.backtesting.backtest_engine.BacktestEngine - INFO - __init__:102 - Backtest engine initialized
2025-06-30 18:25:36 - quantum_edge.backtesting.performance_analyzer.PerformanceAnalyzer - INFO - __init__:43 - Performance analyzer initialized
2025-06-30 18:25:36 - quantum_edge.backtesting.walk_forward_tester.WalkForwardTester - INFO - __init__:76 - Walk-forward tester initialized
2025-06-30 18:25:36 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:25:36 - quantum_edge.web.dashboard.DashboardManager - INFO - __init__:26 - Dashboard manager initialized
2025-06-30 18:25:36 - quantum_edge.web.app.QuantumEdgeApp - INFO - __init__:47 - QuantumEdge web application initialized
2025-06-30 18:25:36 - quantum_edge.web.dashboard.DashboardManager - INFO - __init__:26 - Dashboard manager initialized
2025-06-30 18:25:38 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 18:25:38 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:25:38 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 1 symbols
2025-06-30 18:25:38 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 18:25:38 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:25:38 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 18:25:38 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for AAPL
2025-06-30 18:25:38 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:25:38 - quantum_edge.risk.position_sizer.PositionSizer - INFO - calculate_position_size:88 - Calculating position size for AAPL
2025-06-30 18:25:38 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x16c7f2d10>
2025-06-30 18:25:38 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x16a6d1120>, 200342.627925041)]']
connector: <aiohttp.connector.TCPConnector object at 0x16c7f2c20>
2025-06-30 18:32:15 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 18:32:15 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:32:20 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:38:08 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 18:38:08 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:38:13 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x13053a380>
2025-06-30 18:38:13 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x13055f820>, 201098.47730875)]']
connector: <aiohttp.connector.TCPConnector object at 0x13053b310>
2025-06-30 18:38:15 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:291 - Error fetching ticker details for AAPL: Client error 401: {"status":"ERROR","request_id":"acda10c7130915e6f74731d1253829ef","error":"Unknown API Key"}
2025-06-30 18:38:15 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpha_vantage', 'benzinga']
2025-06-30 18:38:15 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:435 - Fetching fundamental data from Alpha Vantage...
2025-06-30 18:38:28 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:443 - Retrieved fundamental data for 1 symbols
2025-06-30 18:38:28 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:451 - Fetching sentiment data from Benzinga...
2025-06-30 18:38:30 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:459 - Retrieved sentiment data for 1 symbols
2025-06-30 18:38:30 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:691 - MarketDataAggregator connections closed
2025-06-30 18:38:30 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - INFO - __init__:63 - Microcap universe builder initialized
2025-06-30 18:38:30 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 18:38:30 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 1 symbols
2025-06-30 18:38:30 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 18:38:30 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:38:30 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:39:52 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 18:39:52 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:39:54 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x14404fa90>
2025-06-30 18:39:54 - asyncio - ERROR - default_exception_handler:1758 - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x144069600>, 201199.541318916)]']
connector: <aiohttp.connector.TCPConnector object at 0x14404f7c0>
2025-06-30 18:39:56 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:291 - Error fetching ticker details for AAPL: Client error 401: {"status":"ERROR","request_id":"af6f8737d3be2a48cb64674db13164fd","error":"Unknown API Key"}
2025-06-30 18:39:56 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpha_vantage', 'benzinga']
2025-06-30 18:39:56 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:435 - Fetching fundamental data from Alpha Vantage...
2025-06-30 18:40:09 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:443 - Retrieved fundamental data for 1 symbols
2025-06-30 18:40:09 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:451 - Fetching sentiment data from Benzinga...
2025-06-30 18:40:10 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:459 - Retrieved sentiment data for 1 symbols
2025-06-30 18:40:10 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:691 - MarketDataAggregator connections closed
2025-06-30 18:40:10 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - INFO - __init__:63 - Microcap universe builder initialized
2025-06-30 18:40:10 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 18:40:10 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 1 symbols
2025-06-30 18:40:10 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 18:40:10 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:40:10 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:40:10 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 18:40:10 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for AAPL
2025-06-30 18:40:10 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:40:10 - quantum_edge.risk.position_sizer.PositionSizer - INFO - calculate_position_size:88 - Calculating position size for AAPL
2025-06-30 18:40:10 - quantum_edge.risk.risk_metrics.RiskMetrics - INFO - __init__:61 - Risk metrics calculator initialized
2025-06-30 18:40:10 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 18:40:10 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:40:10 - quantum_edge.backtesting.backtest_engine.BacktestEngine - INFO - __init__:102 - Backtest engine initialized
2025-06-30 18:40:10 - quantum_edge.backtesting.performance_analyzer.PerformanceAnalyzer - INFO - __init__:43 - Performance analyzer initialized
2025-06-30 18:40:10 - quantum_edge.backtesting.walk_forward_tester.WalkForwardTester - INFO - __init__:76 - Walk-forward tester initialized
2025-06-30 18:40:10 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:40:10 - quantum_edge.web.dashboard.DashboardManager - INFO - __init__:26 - Dashboard manager initialized
2025-06-30 18:40:10 - quantum_edge.web.app.QuantumEdgeApp - INFO - __init__:47 - QuantumEdge web application initialized
2025-06-30 18:40:10 - quantum_edge.web.dashboard.DashboardManager - INFO - __init__:26 - Dashboard manager initialized
2025-06-30 18:40:11 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 18:40:11 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:40:11 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 1 symbols
2025-06-30 18:40:11 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 18:40:11 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:40:11 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 18:40:11 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for AAPL
2025-06-30 18:42:15 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 18:42:15 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:42:18 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:42:18 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:42:18 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for MSFT
2025-06-30 18:42:18 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for GOOGL
2025-06-30 18:42:18 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AMZN
2025-06-30 18:42:18 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for TSLA
2025-06-30 18:42:19 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:291 - Error fetching ticker details for AAPL: Client error 401: {"status":"ERROR","request_id":"ff3ecc4be03b4a1d0190fe750942f7aa","error":"Unknown API Key"}
2025-06-30 18:42:19 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: []
2025-06-30 18:42:19 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:691 - MarketDataAggregator connections closed
2025-06-30 18:42:19 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:42:19 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for 
2025-06-30 18:42:19 - quantum_edge.config.settings - WARNING - _validate_configuration:174 - Missing API keys (development mode): Polygon.io API key (POLYGON_API_KEY)
2025-06-30 18:42:19 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:42:19 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:42:19 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:43:02 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 18:43:02 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:43:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:43:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:43:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for MSFT
2025-06-30 18:43:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for GOOGL
2025-06-30 18:43:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AMZN
2025-06-30 18:43:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for TSLA
2025-06-30 18:43:10 - quantum_edge.data.sources.polygon_client.PolygonClient - ERROR - get_ticker_details:291 - Error fetching ticker details for AAPL: Client error 401: {"status":"ERROR","request_id":"4fa01cdbde9f50c2318fe104169cd8a0","error":"Unknown API Key"}
2025-06-30 18:43:10 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: []
2025-06-30 18:43:10 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - close:691 - MarketDataAggregator connections closed
2025-06-30 18:43:10 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:43:10 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for 
2025-06-30 18:43:10 - quantum_edge.config.settings - WARNING - _validate_configuration:174 - Missing API keys (development mode): Polygon.io API key (POLYGON_API_KEY)
2025-06-30 18:43:10 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:43:10 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:43:10 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:44:50 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 18:44:50 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:44:52 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 18:44:52 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 1 symbols
2025-06-30 18:44:52 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 18:44:52 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 18:44:52 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AAPL
2025-06-30 18:44:52 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 18:44:52 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for AAPL
2025-06-30 18:44:52 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 18:44:52 - quantum_edge.risk.position_sizer.PositionSizer - INFO - calculate_position_size:88 - Calculating position size for AAPL
2025-06-30 18:44:52 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 18:44:52 - quantum_edge.web.dashboard.DashboardManager - INFO - __init__:26 - Dashboard manager initialized
2025-06-30 18:44:52 - quantum_edge.web.app.QuantumEdgeApp - INFO - __init__:47 - QuantumEdge web application initialized
2025-06-30 18:44:52 - quantum_edge.web.dashboard.DashboardManager - INFO - __init__:26 - Dashboard manager initialized
2025-06-30 19:31:08 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 19:31:08 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 19:31:08 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - __init__:69 - MarketDataAggregator initialized with sources: ['polygon', 'alpha_vantage', 'benzinga']
2025-06-30 19:31:08 - quantum_edge.discovery.microcap_universe.MicrocapUniverse - INFO - __init__:63 - Microcap universe builder initialized
2025-06-30 19:31:08 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 19:31:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 19:31:08 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 19:31:08 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 19:31:08 - quantum_edge.discovery.discovery_engine.DiscoveryEngine - INFO - __init__:55 - Discovery engine initialized
2025-06-30 19:31:11 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:435 - Fetching fundamental data from Alpha Vantage...
2025-06-30 19:31:23 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:443 - Retrieved fundamental data for 1 symbols
2025-06-30 19:31:23 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:451 - Fetching sentiment data from Benzinga...
2025-06-30 19:31:24 - quantum_edge.data.aggregators.market_data_aggregator.MarketDataAggregator - INFO - get_comprehensive_analysis:459 - Retrieved sentiment data for 1 symbols
2025-06-30 19:39:33 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x130e893f0>
2025-06-30 19:39:33 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x130e89450>
2025-06-30 19:39:33 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x130e894b0>
2025-06-30 19:41:02 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 19:41:02 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 19:41:08 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - __init__:108 - Catalyst detector initialized
2025-06-30 19:41:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 19:41:08 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 19:41:08 - quantum_edge.risk.position_sizer.PositionSizer - INFO - __init__:63 - Position sizer initialized
2025-06-30 19:41:08 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 1 symbols
2025-06-30 19:41:08 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 19:41:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for SAVA
2025-06-30 19:41:08 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for SAVA
2025-06-30 19:41:08 - quantum_edge.risk.position_sizer.PositionSizer - INFO - calculate_position_size:88 - Calculating position size for SAVA
2025-06-30 19:41:08 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 1 symbols
2025-06-30 19:41:08 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 19:41:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AVXL
2025-06-30 19:41:08 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for AVXL
2025-06-30 19:41:08 - quantum_edge.risk.position_sizer.PositionSizer - INFO - calculate_position_size:88 - Calculating position size for AVXL
2025-06-30 19:41:08 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:127 - Detecting catalysts for 1 symbols
2025-06-30 19:41:08 - quantum_edge.discovery.catalyst_detector.CatalystDetector - INFO - detect_catalysts:155 - Detected 0 high-impact catalysts
2025-06-30 19:41:08 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for CTMX
2025-06-30 19:41:08 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for CTMX
2025-06-30 19:41:08 - quantum_edge.risk.position_sizer.PositionSizer - INFO - calculate_position_size:88 - Calculating position size for CTMX
2025-06-30 19:42:33 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 19:42:33 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for NVDA
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for SAVA
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for AVXL
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for PLUG
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for RKLB
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - __init__:74 - Growth scorer initialized
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for TEST
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for TEST
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for TEST
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for TEST
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for TEST
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for TEST
2025-06-30 19:42:33 - quantum_edge.discovery.growth_scorer.GrowthScorer - INFO - score_growth_potential:99 - Scoring growth potential for TEST
2025-06-30 19:42:33 - quantum_edge.risk.risk_manager.RiskManager - INFO - __init__:111 - Risk manager initialized
2025-06-30 19:42:33 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for NVDA
2025-06-30 19:42:33 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for SAVA
2025-06-30 19:42:33 - quantum_edge.risk.risk_manager.RiskManager - INFO - assess_risk:132 - Assessing risk for AVXL
2025-06-30 19:56:09 - root - INFO - setup_logging:126 - Logging configured - Level: INFO, File: logs/quantum_edge.log
2025-06-30 19:56:09 - quantum_edge.config.settings - INFO - __init__:165 - QuantumEdge configuration loaded for development environment
2025-06-30 20:11:02 - asyncio - ERROR - default_exception_handler:1758 - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x11c0908b0>
