#!/usr/bin/env python3
"""
QuantumEdge Web Application Startup Script
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from quantum_edge.web.app import create_app

if __name__ == '__main__':
    app = create_app()
    print("🌐 Starting QuantumEdge Web Interface...")
    print("📊 Dashboard available at: http://localhost:5000")
    print("🔍 Discovery page: http://localhost:5000/discovery")
    print("🛡️ Risk analysis: http://localhost:5000/risk")
    print("📈 Backtesting: http://localhost:5000/backtesting")
    print("💼 Portfolio: http://localhost:5000/portfolio")
    app.run(host='0.0.0.0', port=5001, debug=False)
